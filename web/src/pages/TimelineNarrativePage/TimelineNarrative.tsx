import { Link } from 'react-router-dom';
import { Accordion, AccordionControl, Flex, useMantineTheme } from '@mantine/core';
import Chatbot from '@/components/Chatbot/Chatbot';
import Filters from '@/components/Filters/Filters';
import SearchableSelect from '@/components/SearchableSelect/SearchableSelect';
import { Heading } from '@/components/Texts/Heading/Heading';
import { ChatbotProvider } from '@/context/ChatbotContext';
import { NarrativeProps } from '@/types/narrativeTypes';
import { TeamTypeProps } from '@/types/teamType';
import Timeline from '../TimelinePage/components/Timeline';
import { useTimeline } from '../TimelinePage/useTimeline';

type TimelineNarrativeProps = {
  narrative: NarrativeProps;
  groupOptions: TeamTypeProps[];
  filterForm: ReturnType<typeof useTimeline>['filterForm'];
  timelineResponseObject: ReturnType<typeof useTimeline>['timelineResponseObject'];
  paginationObject: ReturnType<typeof useTimeline>['paginationObject'];
  timelineLoading: ReturnType<typeof useTimeline>['loading'];
  handlePageNavigation: ReturnType<typeof useTimeline>['handlePageNavigation'];
  toggleBookmark: ReturnType<typeof useTimeline>['toggleBookmark'];
};

export default function TimelineNarrative({
  narrative,
  groupOptions,
  filterForm,
  timelineResponseObject,
  paginationObject,
  timelineLoading,
  handlePageNavigation,
  toggleBookmark,
}: TimelineNarrativeProps) {
  const groupPillTheme = useMantineTheme().other.pill.groupPill;

  return (
    <ChatbotProvider>
      <Flex direction="column" align="start" className="py-[24px] md:py-[32px]">
        <Heading tagLevel="2">{narrative.summary}</Heading>
        <Link to="/timeline" className="text-blue-500 hover:underline mt-1">
          ← Back to timeline
        </Link>
        <Filters filterForm={filterForm} placeholderBlockNumber={1} />
        <Accordion
          chevron={
            <img
              src="/icons/timelineIcons/timelineAccordionIcon.svg"
              alt="timeline accordion icon"
              className="transition-transform duration-30"
            />
          }
          chevronPosition="left"
          w="100%"
          mx="auto"
          chevronSize={28}
          multiple
          defaultValue={['overview', 'timeline']}
        >
          <Accordion.Item value="timeline" style={{ borderBottom: 'none' }}>
            <AccordionControl component="div" role="button">
              <Flex className="flex-col lg:flex-row lg:justify-between lg:items-center">
                <Heading tagLevel="3" className="py-[20px]">
                  Timeline
                </Heading>
                <div
                  role="presentation"
                  onClick={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                >
                  <SearchableSelect<TeamTypeProps>
                    label=""
                    placeholder="Select teams"
                    options={groupOptions}
                    value={filterForm.values.groups ?? []}
                    onChange={(group) => {
                      filterForm.setFieldValue('groups', [
                        group,
                        ...(filterForm.values.groups ?? []),
                      ]);
                    }}
                    remove={(id) => {
                      filterForm.setFieldValue(
                        'groups',
                        (filterForm.values.groups ?? []).filter(
                          (group: TeamTypeProps) => group.id !== id
                        )
                      );
                    }}
                    pillStyle={{
                      ...groupPillTheme.root,
                    }}
                    pillIcon={
                      <img
                        src="/icons/pills/pillUserIcon.svg"
                        alt="User Icon"
                        className="mr-[10.5px]"
                      />
                    }
                    containerStyle="max-w-[350px]"
                    ariaLabel="timeline-groups-filter"
                  />
                </div>
              </Flex>
            </AccordionControl>
            <Accordion.Panel>
              <Timeline
                timelineResponseObject={timelineResponseObject}
                paginationObject={paginationObject}
                filterForm={filterForm}
                loading={timelineLoading}
                handlePageNavigation={handlePageNavigation}
                toggleBookmark={toggleBookmark}
              />
            </Accordion.Panel>
          </Accordion.Item>
        </Accordion>
        <Chatbot />
      </Flex>
    </ChatbotProvider>
  );
}
