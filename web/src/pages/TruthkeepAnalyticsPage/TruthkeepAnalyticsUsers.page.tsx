import { useEffect, useState } from 'react';
import { truthkeepAnalyticsService } from '@/api/truthkeep-analytics/truthkeepAnalyticsService';
import { authGuardHOC } from '@/components/authGuardHOC';

function TruthkeepAnalyticsUsersPage() {
  const [text, setText] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const data = await truthkeepAnalyticsService.getUsers();
        setText(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return <pre>{text}</pre>;
}

export default authGuardHOC(TruthkeepAnalyticsUsersPage, true);
