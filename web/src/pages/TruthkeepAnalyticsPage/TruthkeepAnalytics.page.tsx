import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Box, Flex, Group, Loader, ScrollArea, Table, Title } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  TruthkeepAnalyticsData,
  truthkeepAnalyticsService,
} from '@/api/truthkeep-analytics/truthkeepAnalyticsService';
import { authGuardHOC } from '@/components/authGuardHOC';
import { DateRangeFilter } from '@/components/Filters/DateRangeFilter';
import { getDateRangeParams } from '@/helpers/dateUtils';
import { DateRangeOptions } from '@/types/timelineTypes';

function TruthkeepAnalyticsPage() {
  const [data, setData] = useState<TruthkeepAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);

  const filterForm = useForm<{
    dateRange: DateRangeOptions;
    customDateRange: string;
  }>({
    initialValues: {
      dateRange: 'Last month',
      customDateRange: '',
    },
  });

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const { dateFrom, dateTo } = getDateRangeParams(filterForm.values);
      const analyticsData = await truthkeepAnalyticsService.getAnalytics({
        dateFrom,
        dateTo,
      });
      setData(analyticsData);
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to load Truthkeep analytics data',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [filterForm.values.dateRange, filterForm.values.customDateRange]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader size="lg" />
      </div>
    );
  }

  return (
    <Box className="p-6" maw="1280px">
      <Title order={1} mb="lg">
        Truthkeep Analytics
      </Title>

      <Flex direction="column" justify="space-between">
        <Group mb="lg" justify="space-between">
          <DateRangeFilter filterForm={filterForm as any} />
          <Link to="/truthkeep-analytics/users" className="text-blue-500 underline" target="_blank">
            Export
          </Link>
        </Group>
        <ScrollArea>
          <Table striped highlightOnHover style={{ tableLayout: 'fixed', width: 'auto' }}>
            <Table.Thead>
              <Table.Tr>
                <Table.Th style={{ width: '1%', whiteSpace: 'nowrap' }}>First Name</Table.Th>
                <Table.Th style={{ width: '1%', whiteSpace: 'nowrap' }}>Last Name</Table.Th>
                <Table.Th style={{ width: '1%', whiteSpace: 'nowrap' }}>Timeline</Table.Th>
                <Table.Th style={{ width: '1%', whiteSpace: 'nowrap' }}>Analytics</Table.Th>
                <Table.Th style={{ width: '1%', whiteSpace: 'nowrap' }}>Topic Management</Table.Th>
                <Table.Th style={{ width: '1%', whiteSpace: 'nowrap' }}>Last Access (MST)</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {data?.users.map((user, index) => (
                <Table.Tr key={index}>
                  <Table.Td style={{ whiteSpace: 'nowrap' }}>{user.firstName}</Table.Td>
                  <Table.Td style={{ whiteSpace: 'nowrap' }}>{user.lastName}</Table.Td>
                  <Table.Td style={{ whiteSpace: 'nowrap' }}>{user.timeline}</Table.Td>
                  <Table.Td style={{ whiteSpace: 'nowrap' }}>{user.analytics}</Table.Td>
                  <Table.Td style={{ whiteSpace: 'nowrap' }}>{user.topicManagement}</Table.Td>
                  <Table.Td style={{ whiteSpace: 'nowrap' }}>
                    {user.lastAccess ? new Date(user.lastAccess).toLocaleDateString() : ''}
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
      </Flex>
    </Box>
  );
}

export default authGuardHOC(TruthkeepAnalyticsPage, true);
