import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, Flex, Loader, TextInput } from '@mantine/core';
import { useForm } from '@mantine/form';
import { userService } from '@/api/user/userService';
import { authGuardHOC } from '@/components/authGuardHOC';
import Button from '@/components/Button/Button';
import { Layout } from '@/components/Layout/Layout';
import ResetPasswordModal from '@/components/Modal/ChangePasswordModal';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';
import { notifications } from '@/helpers/notifications';
import { useSubredditSources, useYoutubeSources } from '@/hooks/useDataSources';

const SettingsPage = () => {
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const { userInfo } = useAppContext();
  const [isEditing, setIsEditing] = useState(false);

  const form = useForm({
    initialValues: {
      firstName: userInfo?.firstName || '',
      lastName: userInfo?.lastName || '',
    },
    validate: {
      firstName: (value) => (!value ? 'First name is required' : null),
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    if (!userInfo) {
      return;
    }

    try {
      await userService.editUser(userInfo.id, {
        firstName: values.firstName,
        lastName: values.lastName,
        email: userInfo.email,
        groups: userInfo.groups.map((group) => group.id),
        isAdmin: userInfo.isAdmin,
      });
      setIsEditing(false);
      notifications.show({
        title: 'Settings Updated Successfully',
        message: 'Your settings have been updated',
        color: 'teal',
        autoClose: 3000,
      });
    } catch (error) {
      notifications.show(
        {
          title: 'Error',
          message: 'Failed to update user information. Please try again.',
        },
        error
      );
    }
  };

  const handleCancel = () => {
    form.setValues({
      firstName: userInfo?.firstName || '',
      lastName: userInfo?.lastName || '',
    });
    setIsEditing(false);
  };

  const { subredditSources, isLoading: isLoadingSubredditSources } = useSubredditSources();
  const { youtubeSources, isLoading: isLoadingYoutubeSources } = useYoutubeSources();

  return (
    <Layout>
      <ResetPasswordModal
        onClose={async () => {
          setShowPasswordModal(false);
        }}
        opened={showPasswordModal}
      />
      <Flex
        direction="column"
        align="start"
        w="100%"
        h="100%"
        className="py-[24px] px-[12px] md:py-[32px] md:px-[40px]"
      >
        <Text size="xl" bold className="mb-6">
          Settings
        </Text>
        <Flex direction="column" gap="md">
          <Card withBorder p="md" radius="md" w="400px" bg="#F1F3F5">
            <form onSubmit={form.onSubmit(handleSubmit)}>
              <Flex direction="column" gap="md" w="100%">
                <Flex direction="row" justify="space-between" align="center">
                  <Text size="md" bold>
                    Name
                  </Text>
                  {!isEditing ? (
                    <button
                      type="button"
                      className="cursor-pointer bg-transparent border-none p-0"
                      onClick={() => setIsEditing(true)}
                    >
                      <Text size="md" bold className="text-[#7950F2] hover:text-[#7048E8]">
                        Edit
                      </Text>
                    </button>
                  ) : (
                    <button
                      type="button"
                      className="cursor-pointer bg-transparent border-none p-0"
                      onClick={handleCancel}
                    >
                      <Text size="md" bold className="text-[#7950F2] hover:text-[#7048E8]">
                        Cancel
                      </Text>
                    </button>
                  )}
                </Flex>
                <Flex direction="column" gap="md">
                  <TextInput
                    {...form.getInputProps('firstName')}
                    disabled={!isEditing}
                    placeholder="First Name"
                  />
                  <TextInput
                    {...form.getInputProps('lastName')}
                    disabled={!isEditing}
                    placeholder="Last Name"
                  />
                  {isEditing && (
                    <Button variant="primary" type="submit" w="100%">
                      Save
                    </Button>
                  )}
                </Flex>
              </Flex>
            </form>
          </Card>

          <Card withBorder p="md" radius="md" w="400px" bg="#F1F3F5">
            <Flex direction="column" gap="md">
              <Flex direction="row" justify="space-between" align="center">
                <Text size="md" bold>
                  Password
                </Text>
                <button
                  type="button"
                  className="cursor-pointer bg-transparent border-none p-0"
                  onClick={() => setShowPasswordModal(true)}
                >
                  <Text size="md" bold className="text-[#7950F2] hover:text-[#7048E8]">
                    Change
                  </Text>
                </button>
              </Flex>
              <TextInput value="••••••••" disabled />
            </Flex>
          </Card>
        </Flex>
      </Flex>
      <Flex
        direction="column"
        align="start"
        w="100%"
        h="100%"
        className="py-[24px] px-[12px] md:py-[32px] md:px-[40px]"
      >
        <Text size="xl" bold className="mb-6">
          Data Sources
        </Text>
        <Flex direction="column" gap="md">
          <Text size="lg" bold>
            Reddit
          </Text>
          {isLoadingSubredditSources ? (
            <Loader color="violet" />
          ) : (
            <Flex direction="column" gap={2} component="ul">
              {subredditSources.map((source) => (
                <li key={source.name}>
                  <Link
                    to={`https://www.reddit.com/r/${source.name}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline text-blue-500"
                  >
                    r/{source.name}
                  </Link>
                </li>
              ))}
            </Flex>
          )}

          <Text size="lg" bold>
            Youtube
          </Text>
          {isLoadingYoutubeSources ? (
            <Loader color="violet" />
          ) : (
            <Flex direction="column" gap={2} component="ul">
              {youtubeSources.map((source) => (
                <li key={source.channelHandle}>
                  <Link
                    to={`https://www.youtube.com/${source.channelHandle}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline text-blue-500"
                  >
                    {source.channelHandle}
                  </Link>
                </li>
              ))}
            </Flex>
          )}
        </Flex>
      </Flex>
    </Layout>
  );
};

export default authGuardHOC(SettingsPage);
