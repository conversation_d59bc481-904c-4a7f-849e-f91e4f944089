import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Accordion, AccordionControl, Flex, Tooltip } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import Button from '@/components/Button/Button';
import ConversationCard from '@/components/Cards/ConversationCard';
import NarrativeCard from '@/components/Cards/NarrativeCard';
import { Icon } from '@/components/icon/Icon';
import Markdown from '@/components/Markdown/Markdown';
import AssignGroupToTimelineModal from '@/components/Modal/AssignGroupToTimelineModal';
import NarrativeBreakdownModal from '@/components/NarrativeBreakdown/NarrativeBreakdownModal';
import TopicPill from '@/components/Pill/TopicPill';
import { Text } from '@/components/Texts/Text/Text';
import { useChatbotContext } from '@/context/ChatbotContext';
import { timeAgo } from '@/helpers/dateUtils';
import { getScoreColors } from '@/helpers/getScoreColors';
import { getCompactNumber } from '@/helpers/numbers';
import { TimelineProps } from '@/types/timelineTypes';
import { COLOR_MAP } from '../constants';

interface TimelineCardProps {
  timeline: TimelineProps;
  toggleBookmark: (timelineId: number) => Promise<void>;
  isOpen: boolean;
}

const bookmarkedIcon = '/icons/timelineIcons/bookmarkedIcon.svg';
const notBookmarkedIcon = '/icons/timelineIcons/notBookmarkedIcon.svg';

const TimelineCard = ({ timeline, toggleBookmark }: TimelineCardProps) => {
  const [openModal, setOpenModal] = useState(false);
  const { id, type, summary, bookmarked } = timeline;

  const { expandChat, setTimelinesToBeSent, timelinesToBeSent, chatOpen, chatIsMaximized } =
    useChatbotContext();

  const hasLongSummary = timeline?.longSummary;
  const hasTipsAndActions = timeline?.tipsAndActions;

  const selectedToBeSent = timelinesToBeSent.some((timeline) => timeline.id === id);

  const getYouTubeThumbnail = (url: string) => {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^\s&]+)/);
    const videoId = match?.[1];
    return videoId ? `https://img.youtube.com/vi/${videoId}/hqdefault.jpg` : null;
  };

  const [narrativeBreakdownModalOpen, setNarrativeBreakdownModalOpen] = useState(false);
  const [selectedNarrativeId, setSelectedNarrativeId] = useState<number | null>(null);
  const openNarrativeBreakdownModal = (narrativeId: number) => {
    setNarrativeBreakdownModalOpen(true);
    setSelectedNarrativeId(narrativeId);
  };

  // Calculate average sentiment for each topic
  const topicSentiments: Record<string, number[]> = {};
  for (const narrative of timeline.narratives) {
    if (!narrative.topic) continue;
    if (!topicSentiments[narrative.topic.id]) {
      topicSentiments[narrative.topic.id] = [];
    }
    topicSentiments[narrative.topic.id].push(narrative.sentiment);
  }
  const topicPills = Object.entries(topicSentiments).map(([topicId, sentiments]) => {
    // Special case to avoid weird rounding issues
    if (sentiments.length === 1) return [topicId, sentiments[0]] as [string, number];

    const averageSentiment =
      sentiments.reduce((sum, sentiment) => sum + sentiment, 0) / sentiments.length;
    const normalizeAverageSentiment = Math.round(averageSentiment / 5) * 5;
    return [topicId, normalizeAverageSentiment] as [string, number];
  });

  const viewConversationText =
    timeline.type === 'REDDIT_POST'
      ? `View on r/${timeline.subredditName}`
      : timeline.type === 'YOUTUBE_VIDEO'
        ? `View on ${timeline.channelHandle}`
        : 'View conversation';

  const narratives = timeline.narratives?.filter((narrative) => narrative.aspect !== 'UNKNOWN');

  return (
    <Flex className="rounded-md" data-testid="timeline-card">
      <AssignGroupToTimelineModal
        opened={openModal}
        onClose={() => {
          setOpenModal(false);
        }}
        timeline={timeline}
      />
      <Accordion.Item
        value={String(id)}
        className="rounded-md border-0 border-t-[4px] border"
        style={{
          backgroundColor: COLOR_MAP[type]?.background,
          borderTopColor: COLOR_MAP[type]?.border,
        }}
        w="100%"
      >
        <Flex className="flex-col lg:flex-row">
          <Flex direction="column" className="w-full">
            <AccordionControl>
              <Flex className="h-full flex-col lg:flex-row" justify="space-between" align="stretch">
                <Flex direction="column" gap={16} className="w-full overflow-hidden">
                  <Text size="lg" component="span">
                    <Flex direction="row" gap={8} align="center">
                      <Text size="lg" component="div">
                        {(type === 'REDDIT_COMMENT' || type === 'YOUTUBE_COMMENT') && (
                          <img
                            src="/icons/timelineIcons/commentIcon.svg"
                            alt="comment thread"
                            className="inline-block min-w-[20px] min-h-[20px] mr-[2px]"
                          />
                        )}
                        {type === 'REDDIT_POST' && (
                          <Tooltip
                            label="This is the original post and a whole comment thread"
                            withArrow
                            multiline
                            styles={{
                              tooltip: {
                                maxWidth: '16rem',
                              },
                            }}
                          >
                            <div className="inline-block">
                              <Text
                                size="sm"
                                bold
                                className="bg-[#4B4B4B] border border-[#959595] rounded-[4px] px-[4px] py-[2px] text-[#FFFFFF] mr-[7px]"
                              >
                                OP
                              </Text>
                            </div>
                          </Tooltip>
                        )}

                        {summary}
                        <Tooltip
                          label={`${Number(timeline.relevance?.score) / 20}: ${timeline.relevance?.reasoning}`}
                          withArrow
                          multiline
                          styles={{
                            tooltip: {
                              maxWidth: '16rem',
                            },
                          }}
                        >
                          <div
                            className="inline-flex w-[16px] h-[16px] ml-[2px] min-w-[16px] min-h-[16px] rounded-[50%] text-white bg-gray-400 items-center justify-center"
                            hidden={!timeline.relevance?.reasoning}
                          >
                            <Text bold className="">
                              ?
                            </Text>
                          </div>
                        </Tooltip>
                      </Text>
                    </Flex>
                  </Text>
                </Flex>
              </Flex>
            </AccordionControl>
            <Flex
              align="center"
              justify="space-between"
              pl={{ base: 0, sm: 14 }}
              pr={16}
              pb={16}
              mt="auto"
            >
              <Flex direction="column" className="ml-[48px] md:ml-0">
                <Flex
                  direction={{ base: 'column', lg: 'row' }}
                  gap={{ base: 8, lg: 16 }}
                  align={{ base: 'start', lg: 'center' }}
                  mt={{ base: 0, lg: 'auto' }}
                >
                  <Flex
                    direction="row"
                    gap={8}
                    justify="start"
                    w={{ base: '100%', sm: 'auto' }}
                    miw="fit-content"
                  >
                    {(type === 'YOUTUBE_VIDEO' || type === 'YOUTUBE_COMMENT') && (
                      <img
                        src="/icons/sourceIcons/youtubeIcon.svg"
                        alt="youtube icon"
                        title="YouTube"
                        className="mr-2 min-w-[32px] min-h-[32px]"
                      />
                    )}
                    {(type === 'REDDIT_POST' || type === 'REDDIT_COMMENT') && (
                      <img
                        src="/icons/sourceIcons/redditIcon.svg"
                        alt="reddit"
                        title="Reddit"
                        className="mr-2 min-w-[32px] min-h-[32px]"
                      />
                    )}
                    {type === 'AVR_FREAKS' && (
                      <img
                        src="/icons/sourceIcons/avrFreaksIcon.png"
                        alt="avr freaks"
                        title="AVR Freaks"
                        className="mr-2"
                        width={32}
                      />
                    )}
                    {type === 'MICROCHIP_CLASSIC' && (
                      <img
                        src="/icons/sourceIcons/microchipClassicIcon.svg"
                        alt="microchip"
                        title="Microchip Community Forum"
                        className="mr-2"
                        width={32}
                      />
                    )}
                    {type === 'ALL_ABOUT_CIRCUITS' && (
                      <img
                        src="/icons/sourceIcons/aac.png"
                        alt="microchip"
                        title="All About Circuits"
                        className="mr-2 w-8 h-8"
                        width={200}
                        height={200}
                      />
                    )}
                    <Tooltip
                      disabled={!timeline.sentimentReasoning}
                      label={timeline.sentimentReasoning}
                      withArrow
                      multiline
                      styles={{ tooltip: { maxWidth: '16rem' } }}
                    >
                      <Flex
                        className={`${getScoreColors(timeline?.sentiment ?? -1)} min-w-[36px] min-h-[36px] p-[6px] rounded-full items-center justify-center text-white cursor-pointer whitespace-nowrap`}
                        hidden={timeline?.sentiment === -1}
                      >
                        <Text size="xl" bold>
                          {timeline?.sentiment ?? 0}
                        </Text>
                      </Flex>
                    </Tooltip>
                    <Flex direction="row" gap={4} align="center">
                      <img
                        src="/icons/timelineIcons/likeIcon.svg"
                        alt="like icon"
                        className="min-w-[20px] min-h-[20px]"
                      />
                      <Text size="lg" bold className="whitespace-nowrap">
                        {getCompactNumber((timeline as any)?.likeCount ?? timeline.voteScore ?? 0)}
                      </Text>
                    </Flex>
                    <Flex direction="row" gap={4} align="center">
                      <img
                        src="/icons/timelineIcons/commentIcon.svg"
                        alt="comment icon"
                        className="min-w-[20px] min-h-[20px]"
                      />
                      <Text size="lg" bold className="whitespace-nowrap">
                        {getCompactNumber(timeline.commentCount ?? timeline.replies?.count ?? 0)}
                      </Text>
                    </Flex>
                  </Flex>
                  <Flex direction="row" gap={4} align="center" wrap="wrap" component="ul">
                    {topicPills.map(([topicId, sentiment]) => (
                      <li key={topicId}>
                        <TopicPill
                          topic={{
                            name:
                              timeline.topics.find((topic) => topic.id === Number(topicId))?.name ??
                              '',
                            sentiment,
                          }}
                        />
                      </li>
                    ))}
                  </Flex>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
          <Flex
            className="flex-row lg:flex-col ml-[48px] md:ml-0"
            justify="space-between"
            align="end"
            py={{ base: 0, md: 16 }}
            pb={{ base: 14, md: 14 }}
            pl={{ base: 0, sm: 20 }}
            pr={{ base: 16, md: 20 }}
            gap={{ base: 0, md: 12 }}
          >
            <div className="contents lg:flex lg:gap-4">
              <Button
                onClick={async () => {
                  await toggleBookmark(id);
                }}
                className="cursor-pointer min-w-[32px]"
                aria-label={bookmarked ? 'Remove bookmark' : 'Add bookmark'}
              >
                <img
                  src={bookmarked ? bookmarkedIcon : notBookmarkedIcon}
                  alt={bookmarked ? 'bookmarked icon' : 'not bookmarked icon'}
                />
              </Button>

              <Button
                onClick={() => {
                  setOpenModal(true);
                }}
                className="cursor-pointer min-w-[32px]"
              >
                <img src="/icons/timelineIcons/repostIcon.svg" alt="repost icon" />
              </Button>

              <Button
                onClick={async () => {
                  await navigator.clipboard.writeText(`${window.location.origin}/timeline/${id}`);
                  notifications.show({
                    title: 'Link Copied',
                    message: (
                      <span className="line-clamp-3 truncate text-wrap">{timeline.summary}</span>
                    ),
                    autoClose: 3000,
                    color: 'teal',
                  });
                }}
                className="cursor-pointer min-w-[32px]"
              >
                <img src="/icons/timelineIcons/linkIcon.svg" alt="copy link" />
              </Button>
            </div>
            <div className="contents lg:flex lg:gap-4 lg:items-center">
              <Text className="text-gray-600 hidden lg:block text-nowrap" size="sm">
                {timeline?.publishedAt ? timeAgo(timeline?.publishedAt) : ''}
              </Text>
              <Button
                onClick={() => {
                  if (selectedToBeSent) {
                    setTimelinesToBeSent((prev) => prev.filter((t) => t.id !== timeline?.id));
                  } else {
                    setTimelinesToBeSent([...timelinesToBeSent, timeline]);
                    if (!chatOpen || !chatIsMaximized) {
                      expandChat?.();
                    }
                  }
                }}
                className="cursor-pointer w-[40px] h-[40px] rounded-[50%] bg-[#B7A7E5] flex items-center justify-center"
              >
                {selectedToBeSent ? (
                  <Icon iconCategory="misc" iconType="remove" className="text-white" />
                ) : (
                  <img src="/icons/timelineIcons/chatbotIcon.svg" alt="chatbot icon" />
                )}
              </Button>
            </div>
          </Flex>
        </Flex>

        <Accordion.Panel ml={48} className="pr-0 lg:pr-24">
          <Flex direction="column" gap={16}>
            {timeline.externalUrl && type === 'YOUTUBE_VIDEO' && (
              <div className="relative w-[280px] h-[160px] overflow-hidden flex items-center justify-start">
                <div className="relative h-full">
                  <img
                    src={getYouTubeThumbnail(timeline.externalUrl) ?? ''}
                    alt="YouTube thumbnail"
                    className="h-full object-contain rounded-md"
                  />
                  <a href={timeline.externalUrl} target="_blank" rel="noopener noreferrer">
                    <img
                      src="/icons/timelineIcons/defaultYoutubeThumbnailPlayButton.svg"
                      alt="play icon"
                      className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[40px] h-[40px] z-10 hover:scale-110 transition-transform"
                    />
                  </a>
                </div>
              </div>
            )}
            <Flex hidden={!hasLongSummary} direction="column" gap={8}>
              <Text size="lg" bold>
                Summary
              </Text>
              <Text size="md">{timeline.longSummary}</Text>
            </Flex>
            <Flex hidden={!hasTipsAndActions} direction="column" gap={8}>
              <Text size="lg" bold>
                Tips & Actions
              </Text>
              <Text size="md" component="div">
                <Markdown>{timeline.tipsAndActions}</Markdown>
              </Text>
            </Flex>
            <div className="grid gap-2 grid-cols-[1fr_auto]">
              <Flex direction="column" gap={16}>
                <Flex direction="column" gap={8} hidden={!narratives?.length}>
                  <Text size="lg" bold>
                    Narratives
                  </Text>
                  <ul className="grid grid-cols-1 lg:grid-cols-2 gap-2">
                    {narratives.map((narrative) => (
                      <li key={narrative.id}>
                        <button
                          type="button"
                          onClick={() => openNarrativeBreakdownModal(narrative.id)}
                          className="text-start w-full"
                        >
                          <NarrativeCard className="cursor-pointer hover:shadow-md transition-shadow">
                            {narrative.summary}
                          </NarrativeCard>
                        </button>
                      </li>
                    ))}
                  </ul>
                </Flex>
                <Flex direction="column" gap={12} className="w-full">
                  <Flex direction="row" justify="space-between" align="center" wrap="wrap" gap={8}>
                    <Text size="lg" bold className="text-nowrap">
                      Conversations
                    </Text>
                    <Text size="lg" className="underline">
                      <a href={timeline.externalUrl} target="_blank" rel="noopener noreferrer">
                        {viewConversationText}
                      </a>
                    </Text>
                  </Flex>
                  <div className="grid gap-2 lg:grid-cols-2 w-full">
                    <Link to={timeline.externalUrl} target="_blank" rel="noopener noreferrer">
                      <ConversationCard className="hover:shadow-md transition-shadow">
                        {timeline.title}
                      </ConversationCard>
                    </Link>
                    {(timeline.replies?.items ?? []).slice(0, 1).map((reply) => (
                      <Link
                        key={reply.id}
                        to={reply.externalUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ConversationCard
                          key={reply.id}
                          className="hover:shadow-md transition-shadow"
                        >
                          {reply.body}
                        </ConversationCard>
                      </Link>
                    ))}
                  </div>
                </Flex>
              </Flex>
              {(timeline.replies?.count ?? 0) > 1 && (
                <Text
                  size="sm"
                  className="bg-[#D9D9D9] min-w-[32px] min-h-[32px] h-fit self-end rounded-full flex items-center justify-center whitespace-nowrap"
                >
                  +{getCompactNumber((timeline.replies?.count ?? 0) - 1)}
                </Text>
              )}
            </div>
          </Flex>
        </Accordion.Panel>
      </Accordion.Item>
      <NarrativeBreakdownModal
        opened={narrativeBreakdownModalOpen}
        onClose={() => setNarrativeBreakdownModalOpen(false)}
        narrativeId={selectedNarrativeId!}
      />
    </Flex>
  );
};

export default TimelineCard;
