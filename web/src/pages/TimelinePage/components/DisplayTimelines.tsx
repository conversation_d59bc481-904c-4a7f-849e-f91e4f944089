import { useState } from 'react';
import { Accordion, Flex, Pagination } from '@mantine/core';
import { PaginationObjectProps } from '@/types/globalTypes';
import { PaginatedResponse } from '@/types/paginatedTypes';
import { TimelineProps } from '@/types/timelineTypes';
import TimelineCard from './TimelineCard';

interface DisplayTimelinesProps {
  timelineResponseObject: PaginatedResponse<TimelineProps>;
  paginationObject: PaginationObjectProps;
  bookmarkOnly?: boolean;
  handlePageNavigation: (page: number) => void;
  toggleBookmark: (timelineId: number) => Promise<void>;
  openItems?: string[];
}

const DisplayTimelines = ({
  timelineResponseObject,
  paginationObject,
  bookmarkOnly = false,
  handlePageNavigation,
  toggleBookmark,
  openItems = [],
}: DisplayTimelinesProps) => {
  const [openedItems, setOpenedItems] = useState<string[]>(openItems);

  return (
    <Flex direction={{ base: 'column-reverse', sm: 'column' }} w="100%">
      <Flex direction="column" gap={24} mt={32}>
        <Accordion
          multiple
          chevron={
            <img
              src="/icons/timelineIcons/timelineAccordionIcon.svg"
              alt="timeline accordion icon"
              className="transition-transform duration-30"
            />
          }
          chevronPosition="left"
          classNames={{
            control: 'items-stretch',
            chevron: 'self-stretch flex items-start justify-center',
          }}
          styles={{
            label: {
              padding: '16px',
            },
          }}
          w="100%"
          mx="auto"
          chevronSize={28}
          value={openedItems}
          onChange={setOpenedItems}
        >
          <Flex direction="column" gap={24}>
            {timelineResponseObject?.items
              ?.filter((timeline) => !bookmarkOnly || timeline.bookmarked)
              ?.map((timeline) => (
                <TimelineCard
                  key={timeline.id}
                  timeline={timeline}
                  toggleBookmark={toggleBookmark}
                  isOpen={openedItems.includes(String(timeline.id))}
                />
              ))}
          </Flex>
        </Accordion>
      </Flex>
      <Flex
        hidden={!(paginationObject.totalPages > 1)}
        mt={32}
        className="mb-[32px] md:mb-0"
        justify="center"
        align="center"
        w="100%"
      >
        <Pagination
          total={paginationObject.totalPages}
          value={paginationObject.page}
          onChange={handlePageNavigation}
          siblings={0}
          boundaries={1}
        />
      </Flex>
    </Flex>
  );
};

export default DisplayTimelines;
