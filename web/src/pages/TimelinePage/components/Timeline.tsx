import { Flex, Loader, Tabs } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { PaginationObjectProps } from '@/types/globalTypes';
import { PaginatedResponse } from '@/types/paginatedTypes';
import { FilterForm, TimelineProps } from '@/types/timelineTypes';
import DisplayTimelines from './DisplayTimelines';

interface TimelineSectionProps {
  timelineResponseObject: PaginatedResponse<TimelineProps>;
  paginationObject: PaginationObjectProps;
  filterForm: UseFormReturnType<FilterForm>;
  loading: boolean;

  handlePageNavigation: (page: number) => void;
  toggleBookmark: (timelineId: number) => Promise<void>;
}

const Timeline = ({
  timelineResponseObject,
  paginationObject,
  filterForm,
  loading,

  handlePageNavigation,
  toggleBookmark,
}: TimelineSectionProps) => {
  return (
    <Flex direction="column" className="relative" w="100%">
      <Flex direction="row" gap={24} align="center">
        <Tabs
          defaultValue="all"
          w="100%"
          h="100%"
          color="violet"
          p={0}
          onChange={(value) => {
            filterForm.setFieldValue('bookmarkedOnly', value === 'bookmark');
            handlePageNavigation(1);
          }}
        >
          <Tabs.List>
            <Tabs.Tab value="all">All</Tabs.Tab>
            <Tabs.Tab value="bookmark">Bookmarks</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all" data-testid="timeline-cards-section">
            {loading ? (
              <Flex w="100%" mih={300} align="center" justify="center">
                <Loader color="violet" />
              </Flex>
            ) : (
              <DisplayTimelines
                timelineResponseObject={timelineResponseObject}
                paginationObject={paginationObject}
                handlePageNavigation={handlePageNavigation}
                toggleBookmark={toggleBookmark}
              />
            )}
          </Tabs.Panel>

          <Tabs.Panel value="bookmark">
            {loading ? (
              <Flex w="100%" mih={300} align="center" justify="center">
                <Loader color="violet" />
              </Flex>
            ) : (
              <DisplayTimelines
                timelineResponseObject={timelineResponseObject}
                paginationObject={paginationObject}
                bookmarkOnly
                handlePageNavigation={handlePageNavigation}
                toggleBookmark={toggleBookmark}
              />
            )}
          </Tabs.Panel>
        </Tabs>
      </Flex>
    </Flex>
  );
};

export default Timeline;
