import {
  Accordion,
  Flex,
  Input,
  Select,
  Stack,
  Text,
  TextInput,
  useMantineTheme,
} from '@mantine/core';
import { authGuardHOC } from '@/components/authGuardHOC';
import Button from '@/components/Button/Button';
import { sentimentOptions } from '@/components/Filters/consts';
import { DateRangeFilter } from '@/components/Filters/DateRangeFilter';
import { Icon } from '@/components/icon/Icon';
import { LazyRangeSlider } from '@/components/Input/RangeSlider';
import { Layout } from '@/components/Layout/Layout';
import SearchableSelect from '@/components/SearchableSelect/SearchableSelect';
import { Heading } from '@/components/Texts/Heading/Heading';
import HelpText from '@/components/Texts/HelpText/HelpText';
import { TeamTypeProps } from '@/types/teamType';
import { SentimentOptions, SourceOption } from '@/types/timelineTypes';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import Timeline from './components/Timeline';
import { useTimeline } from './useTimeline';

const sourceOptions: SourceOption[] = [
  { id: 'Youtube Video', name: 'Youtube Video' },
  { id: 'Reddit Post', name: 'Reddit Post' },
  { id: 'Youtube Comment', name: 'Youtube Comment' },
  { id: 'Reddit Comment', name: 'Reddit Comment' },
  { id: 'AVR Freaks', name: 'AVR Freaks' },
  { id: 'Microchip Community Forum', name: 'Microchip Community Forum' },
  { id: 'All About Circuits', name: 'All About Circuits' },
];

const TimelinePage = () => {
  const {
    groupOptions,
    tagOptions,
    topicOptions,
    timelineResponseObject,
    filterForm,
    paginationObject,
    loading,

    handlePageNavigation,
    toggleBookmark,
  } = useTimeline();

  const theme = useMantineTheme();

  const sentimentPillTheme = theme.other.pill.sentimentPill;
  const tagPillTheme = theme.other.pill.tagPill;
  const groupPillTheme = theme.other.pill.groupPill;
  const sourcePillTheme = { backgroundColor: '#fd7e14', color: 'white' };

  const selectedSources = filterForm.values.multiSelectSources ?? [];

  return (
    <Layout>
      <Flex
        direction="column"
        align="start"
        w="100%"
        maw="1280px"
        h="100%"
        className="py-[24px] px-[12px] md:py-[32px] md:px-[40px]"
      >
        <Heading tagLevel="2">Timeline</Heading>
        <HelpText
          text="The Timeline is a feed displaying all analyzed content. Here you can view engagements, bookmark or share important items, and view detailed analysis for each engagement."
          modalPage="timeline"
          className="mt-2 mb-4"
        />
        <form className="w-full">
          <div className="items-end gap-[12px] w-full grid md:grid-cols-3">
            <TextInput
              type="search"
              label="Search"
              placeholder="Search timeline..."
              leftSection={<Icon iconCategory="input" iconType="search" />}
              {...filterForm.getInputProps('q')}
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
              className="md:col-span-3"
              maxLength={100}
            />
            <DateRangeFilter filterForm={filterForm} />
            <SearchableSelect<TeamTypeProps>
              label="Teams"
              placeholder="Select teams"
              options={groupOptions}
              value={filterForm.values.groups ?? []}
              onChange={(group) => {
                filterForm.setFieldValue('groups', [...(filterForm.values.groups ?? []), group]);
                filterForm.setFieldValue('personalTopicGroupsOnly', false);
                if (group) {
                  if ('multiSelectTopicIds' in filterForm.values)
                    filterForm.setFieldValue('multiSelectTopicIds', []);
                  if ('topicNames' in filterForm.values)
                    filterForm.setFieldValue('topicNames', null);
                }
              }}
              remove={(id) => {
                filterForm.setFieldValue(
                  'groups',
                  (filterForm.values.groups ?? []).filter((group: TeamTypeProps) => group.id !== id)
                );
              }}
              pillStyle={{
                ...groupPillTheme.root,
              }}
              pillIcon={
                <img src="/icons/pills/pillUserIcon.svg" alt="User Icon" className="mr-[10.5px]" />
              }
              PillsInputStyles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
            <Select
              label="Sort By"
              type="dropdown"
              data={[
                {
                  label: 'Relevance',
                  value: 'RELEVANCE',
                },
                {
                  label: 'Date',
                  value: 'DATE',
                },
                {
                  label: 'Engagement',
                  value: 'ENGAGEMENT',
                },
                { value: 'POSITIVE_SENTIMENT', label: 'Most positive' },
                { value: 'NEGATIVE_SENTIMENT', label: 'Most negative' },
              ]}
              placeholder="Relevance (default)"
              aria-label="timeline-sort-filter"
              {...filterForm.getInputProps('sort')}
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
          </div>

          <Accordion
            className="w-full"
            styles={{
              control: { padding: '8px 0', width: 'fit-content' },
              content: { padding: 0 },
              item: { border: 'none' },
              chevron: { marginLeft: 8 },
            }}
          >
            <Accordion.Item value="advanced-filters">
              <Accordion.Control>
                <span className="text-sm font-medium">More Filters</span>
              </Accordion.Control>
              <Accordion.Panel>
                <div className="w-full grid md:grid-cols-3 gap-[12px] items-end">
                  <Select
                    label="Topic"
                    searchable
                    data={[
                      {
                        label: 'Favorited Topics',
                        value: 'Favorited Topics',
                      },
                      ...(topicOptions?.map((topic) => ({
                        label: topic.name,
                        value: `${topic.id}`,
                      })) ?? []),
                    ]}
                    placeholder="Select a topic"
                    {...filterForm.getInputProps('topicId')}
                    aria-label="timeline-topic-filter"
                    styles={{
                      input: {
                        backgroundColor: '#F1F3F5',
                        borderColor: '#F1F3F5',
                      },
                    }}
                    renderOption={({ option }) => (
                      <div
                        style={{
                          fontWeight: option.label === 'Favorited Topics' ? 'bold' : 'normal',
                        }}
                      >
                        {option.label}
                      </div>
                    )}
                    onChange={(value) => {
                      filterForm.setFieldValue('topicId', value);
                      filterForm.setFieldValue('groups', []);
                    }}
                  />
                  <SearchableSelect<SourceOption>
                    label="Sources"
                    placeholder="Select sources"
                    options={sourceOptions}
                    value={selectedSources}
                    onChange={(source) =>
                      filterForm.setFieldValue('multiSelectSources', [...selectedSources, source])
                    }
                    remove={(id) => {
                      filterForm.setFieldValue(
                        'multiSelectSources',
                        selectedSources.filter((source: SourceOption) => source.id !== id)
                      );
                    }}
                    pillStyle={sourcePillTheme}
                    PillsInputStyles={{
                      input: {
                        backgroundColor: '#F1F3F5',
                        borderColor: '#F1F3F5',
                      },
                    }}
                  />
                  <SearchableSelect<TopicGroupProps>
                    label="Topic Groups"
                    placeholder="Search topic groups"
                    options={tagOptions}
                    value={filterForm.values.tags ?? []}
                    onChange={(tag) =>
                      filterForm.setFieldValue('tags', [...(filterForm.values.tags ?? []), tag])
                    }
                    remove={(id) => {
                      filterForm.setFieldValue(
                        'tags',
                        filterForm.values.tags?.filter((t: { id: number }) => t.id !== id)
                      );
                    }}
                    pillStyle={{ ...tagPillTheme.root }}
                    ariaLabel="timeline-topic-groups-filter"
                    PillsInputStyles={{
                      input: {
                        backgroundColor: '#F1F3F5',
                        borderColor: '#F1F3F5',
                      },
                    }}
                  />
                  <SearchableSelect<SentimentOptions>
                    label="Sentiments"
                    placeholder="Select sentiments"
                    options={sentimentOptions}
                    value={filterForm.values.sentiments ?? []}
                    onChange={(sentiment) =>
                      filterForm.setFieldValue('sentiments', [
                        ...(filterForm.values.sentiments ?? []),
                        sentiment,
                      ])
                    }
                    remove={(id) => {
                      filterForm.setFieldValue(
                        'sentiments',
                        filterForm.values.sentiments?.filter(
                          (sentiment: SentimentOptions) => sentiment.id !== id
                        )
                      );
                    }}
                    pillStyle={{ ...sentimentPillTheme.root }}
                    pillIndividualStyle={{
                      POSITIVE: sentimentPillTheme.positive,
                      NEGATIVE: sentimentPillTheme.negative,
                      UNKNOWN: sentimentPillTheme.unknown,
                    }}
                    PillsInputStyles={{
                      input: {
                        backgroundColor: '#F1F3F5',
                        borderColor: '#F1F3F5',
                      },
                    }}
                  />
                  <Stack>
                    <Input.Label htmlFor="range-slider">Relevance</Input.Label>
                    <LazyRangeSlider
                      id="range-slider"
                      mx={8}
                      min={1}
                      max={5}
                      step={0.5}
                      minRange={0.5}
                      marks={Array.from({ length: 6 }).map((_, i) => ({ value: i }))}
                      {...filterForm.getInputProps('relevance')}
                      color="violet"
                    />
                  </Stack>
                  <Button variant="secondary" onClick={() => filterForm.reset()}>
                    Clear Filters
                  </Button>
                </div>
              </Accordion.Panel>
            </Accordion.Item>
          </Accordion>
        </form>
        <Flex w="100%" h="100%" direction="column">
          <Timeline
            timelineResponseObject={timelineResponseObject}
            paginationObject={paginationObject}
            filterForm={filterForm}
            loading={loading}
            handlePageNavigation={handlePageNavigation}
            toggleBookmark={toggleBookmark}
          />
          <Flex
            hidden={loading || timelineResponseObject.items.length > 0}
            mih={300}
            justify="center"
            align="center"
            direction="column"
            gap={4}
          >
            <Text>No results found</Text>
            <Text size="sm" c="dimmed">
              Try searching for something else or adjusting your filters
            </Text>
          </Flex>
        </Flex>
      </Flex>
    </Layout>
  );
};

export default authGuardHOC(TimelinePage);
