import { useEffect, useRef, useState } from 'react';
import qs from 'qs';
import { useForm } from '@mantine/form';
import { useDebouncedValue } from '@mantine/hooks';
import { groupService } from '@/api/group/groupService';
import { tagService } from '@/api/tags/tagService';
import { timelineService } from '@/api/timeline/timelineService';
import { topicService } from '@/api/topics/topicService';
import { useAppContext } from '@/context/AppContext';
import { sourceDictionary } from '@/dictionary/sourceDictionary';
import { getDateRangeParams } from '@/helpers/dateUtils';
import { notifications } from '@/helpers/notifications';
import { PaginationObjectProps } from '@/types/globalTypes';
import { PaginatedResponse } from '@/types/paginatedTypes';
import { TeamTypeProps } from '@/types/teamType';
import { FilterForm, TimelineProps } from '@/types/timelineTypes';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicProps } from '@/types/topicType';

export const useTimeline = ({
  analysisId,
  narrativeIds,
  formInitialValues,
}: {
  analysisId?: string;
  narrativeIds?: string[];
  formInitialValues?: FilterForm;
} = {}) => {
  const [loadingOptions, setLoadingOptions] = useState(true);
  const [loadingTimeline, setLoadingTimeline] = useState(true);

  const [groupOptions, setGroupOptions] = useState<TeamTypeProps[]>([]);
  const [tagOptions, setTagOptions] = useState<TopicGroupProps[]>([]);
  const [topicOptions, setTopicOptions] = useState<TopicProps[]>([]);

  const abortControllerRef = useRef<AbortController | null>(null);

  const [timelineResponseObject, setTimelineResponseObject] = useState<
    PaginatedResponse<TimelineProps>
  >({} as PaginatedResponse<TimelineProps>);

  const [paginationObject, setPaginationObject] = useState<PaginationObjectProps>({
    page: 1,
    size: 10,
    total: 0,
    totalPages: 0,
  } as PaginationObjectProps);

  const { userInfo } = useAppContext();
  const userTeams = new Set(userInfo?.groups?.map((group) => group.id));

  const filterForm = useForm<FilterForm>({
    initialValues: {
      dateRange: 'Last week',
      timeZone: 'America/Phoenix',
      topicId: null,
      groups: [],
      tags: [],
      sentiments: [],
      multiSelectSources: [],
      bookmarkedOnly: false,
      sort: 'RELEVANCE',
      relevance: [4, 5],
      q: '',
      ...formInitialValues,
    },
  });

  // Compute query string here to help with triggering effects
  const [q] = useDebouncedValue(filterForm.values.q, 500);
  const queryStringNoPagination = getQueryString({ ...filterForm.values, narrativeIds, q });
  const queryString = `${queryStringNoPagination}&page=${paginationObject.page}&size=${paginationObject.size}`;
  const relativeFetchURL = analysisId ? `/${analysisId}?${queryString}` : `?${queryString}`;
  const fetchURL = `/insights/timeline${relativeFetchURL}`;

  const initialize = async () => {
    setLoadingOptions(true);
    filterForm.reset();
    Promise.all([
      topicService.fetchTopics(),
      groupService.fetchGroups(),
      tagService.fetchUserTags(),
    ])
      .then(([topicResponse, groupResponse, tagResponse]) => {
        setTopicOptions(topicResponse.data.items as TopicProps[]);
        setGroupOptions(groupResponse.data.items as TeamTypeProps[]);
        setTagOptions(tagResponse);

        if (groupResponse?.data?.items?.length) {
          filterForm.setFieldValue(
            'groups',
            groupResponse.data.items.filter(
              (g: { id: number | undefined }) => g.id && userTeams.has(g.id)
            )
          );
        }
      })
      .catch((e) => {
        notifications.show(
          {
            title: 'Error fetching data',
            message: 'There was an error fetching teams or topic group',
          },
          e
        );
      })
      .finally(() => {
        setLoadingOptions(false);
      });
  };

  const fetchTimelineItems = async () => {
    let requestAborted = false;
    try {
      setLoadingTimeline(true);

      // Abort any pending request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      const response = await timelineService.fetchTimelineByRelativeURL(
        fetchURL,
        abortControllerRef.current.signal
      );

      setTimelineResponseObject(response as PaginatedResponse<TimelineProps>);
      setPaginationObject((prev) => ({
        ...prev,
        total: response.total,
        totalPages: response.totalPages,
      }));
    } catch (error: any) {
      requestAborted = error?.code === 'ERR_CANCELED';
      if (!requestAborted) {
        notifications.show(
          {
            title: 'Error fetching timeline',
            message: 'There was an error fetching timeline',
          },
          error
        );
      }
    } finally {
      if (!requestAborted) setLoadingTimeline(false);
    }
  };

  const toggleBookmark = async (timelineId: number) => {
    const timelineToUpdate = timelineResponseObject.items.find(
      (timeline) => timeline.id === timelineId
    );
    if (timelineToUpdate) {
      const newBookmarkedState = !timelineToUpdate.bookmarked;
      const externalUrl = timelineToUpdate.externalUrl;
      setTimelineResponseObject((prev) => ({
        ...prev,
        items: prev.items.map((timeline) =>
          timeline.id === timelineId
            ? {
                ...timeline,
                bookmarked: newBookmarkedState,
              }
            : timeline
        ),
      }));

      try {
        if (newBookmarkedState) {
          await timelineService.bookmarkTimeline(timelineToUpdate.id);
        } else {
          await timelineService.unBookmarkTimeline(timelineToUpdate.id);
        }

        notifications.show({
          title: newBookmarkedState ? 'Bookmarked!' : 'Removed Bookmark',
          message: externalUrl,
          autoClose: 3000,
          color: 'teal',
        });
      } catch (e) {
        setTimelineResponseObject((prev) => ({
          ...prev,
          items: prev.items.map((timeline) =>
            timeline.id === timelineId
              ? {
                  ...timeline,
                  bookmarked: !timeline.bookmarked,
                }
              : timeline
          ),
        }));
        notifications.show(
          {
            title: 'Error updating bookmark',
            message: 'Please try again later.',
          },
          e
        );
      }
    }
  };

  const handlePageNavigation = (page: number) => {
    setPaginationObject((prev) => ({
      ...prev,
      page,
    }));
  };

  useEffect(() => {
    if (userInfo) {
      initialize();
    }
  }, [userInfo]);

  useEffect(() => {
    setPaginationObject((prev) => ({
      ...prev,
      page: 1,
    }));
  }, [queryStringNoPagination]);

  useEffect(() => {
    if (!loadingOptions) {
      fetchTimelineItems();
    }
  }, [loadingOptions, queryString]);

  return {
    groupOptions,
    tagOptions,
    topicOptions,
    loadingTimeline,
    loading: loadingOptions || loadingTimeline,
    timelineResponseObject,
    filterForm,
    paginationObject,

    handlePageNavigation,
    toggleBookmark,

    fetchURL,
  };
};

function getQueryString(params: FilterForm & { narrativeIds?: string[] }) {
  return qs.stringify(
    {
      ...getDateRangeParams(params),
      groupIds: params.groups?.map((g) => g.id),
      topicIds: params.topicId ? [params.topicId] : undefined,
      tagIds: params.tags?.map((t) => t.id),
      sentiments: params.sentiments?.map((s) => s.id),
      sources: params.multiSelectSources?.length
        ? params.multiSelectSources.map(
            (source) => sourceDictionary[source.id as keyof typeof sourceDictionary]
          )
        : undefined,
      bookmarkedOnly: params.bookmarkedOnly ? params.bookmarkedOnly : undefined,
      minRelevance: ((params.relevance?.[0] ?? 0) - 1) * 25,
      maxRelevance: ((params.relevance?.[1] ?? 5) - 1) * 25,
      sort: params.sort || undefined,
      narrativeIds: params.narrativeIds,
      onlyFavourited: params.topicId === 'Favorited Topics',
      q: params.q || undefined,
    },
    { arrayFormat: 'repeat' }
  );
}
