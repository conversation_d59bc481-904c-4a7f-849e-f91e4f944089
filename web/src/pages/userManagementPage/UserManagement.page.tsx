import { Checkbox, Flex, Loader, Button as MantineButton, Pagination } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { authGuardHOC } from '@/components/authGuardHOC';
import Button from '@/components/Button/Button';
import { Layout } from '@/components/Layout/Layout';
import AddNewUserModal from '@/components/Modal/AddNewUserModal';
import { AreYouSureModal as DeleteUserModal } from '@/components/Modal/AreYouSureModal/AreYouSureModal';
import EditUserModal from '@/components/Modal/EditUserModal';
import Pill from '@/components/Pill/Pill';
import UserManagementMobileTile from '@/components/Tables/UserManagementTable/UserManagementMobileTile';
import UserManagementTable from '@/components/Tables/UserManagementTable/UserManagementTable';
import { Heading } from '@/components/Texts/Heading/Heading';
import HelpText from '@/components/Texts/HelpText/HelpText';
import { Text } from '@/components/Texts/Text/Text';
import { useUserManagement } from './useUserManagement';

const UserManagementPage = () => {
  const {
    users,
    selectedUsers,
    showAdminsOnly,
    paginationObject,
    openedModalId,
    selectedUserToEdit,
    loading,

    initialize,
    deleteSelectedUsers,
    setShowAdminsOnly,
    setSelectedUsers,
    handlePageNavigation,
    setOpenedModalId,
    setSelectedUserToEdit,
    toggleUserAdmin,
  } = useUserManagement();

  return (
    <Layout>
      {openedModalId === 'newUserModal' && (
        <AddNewUserModal
          onClose={() => {
            setOpenedModalId('');
          }}
          onSubmit={async () => {
            setOpenedModalId('');
            await initialize();
          }}
        />
      )}
      <EditUserModal
        user={selectedUserToEdit}
        deleteSelectedUser={async () => {
          await deleteSelectedUsers([selectedUserToEdit!]);
          notifications.show({
            title: 'User Deleted Successfully!',
            autoClose: 3000,
            color: 'teal',
            message: '',
          });
          await initialize();
          setSelectedUserToEdit(null);
        }}
        onClose={() => {
          setSelectedUserToEdit(null);
        }}
        onSubmit={async () => {
          await initialize();
          setSelectedUserToEdit(null);
        }}
        opened={!!selectedUserToEdit}
      />
      <DeleteUserModal
        onClose={() => setOpenedModalId('')}
        onConfirm={async () => {
          await deleteSelectedUsers(selectedUsers);
          notifications.show({
            title: `User${selectedUsers.length > 1 ? 's' : ''} Deleted Successfully!`,
            autoClose: 3000,
            color: 'teal',
            message: '',
          });
          await initialize();
          setOpenedModalId('');
        }}
        title={`Delete User${selectedUsers.length > 1 ? 's' : ''}`}
        heading={`Are you sure you want to delete ${selectedUsers.length > 1 ? 'these' : 'this'} user${selectedUsers.length > 1 ? 's' : ''}?`}
        targetObject={
          <Flex wrap="wrap" justify="center" align="center" gap={8}>
            {selectedUsers.map((user) => {
              return (
                <Pill key={user.id} variant="secondary" withRemoveButton={false}>
                  <Text size="lg" bold>
                    {user.firstName} {user.lastName}
                  </Text>
                </Pill>
              );
            })}
          </Flex>
        }
        opened={openedModalId === 'deleteUserModal'}
      />

      <Flex
        direction="column"
        align="start"
        w="100%"
        maw="1280px"
        h="100%"
        className="py-[24px] px-[12px] md:py-[32px] md:px-[40px]"
      >
        <Heading tagLevel="2">User Management</Heading>
        <HelpText
          text="User Management allows administrators to manage user accounts, permissions, and access controls. You can create new users, edit existing user information, assign roles, and manage user teams to ensure proper access control across the platform."
          modalPage="user-management"
          className="mt-2 mb-8"
        />
        <Flex direction="row" align="start" justify="space-between" w="100%" mt={32}>
          <Heading tagLevel="3">Users</Heading>
          <Flex direction="row" gap={12}>
            {selectedUsers.length > 0 && (
              <Button
                variant="secondaryDanger"
                size="auto"
                onClick={() => {
                  setOpenedModalId('deleteUserModal');
                }}
              >
                Delete Selected ({selectedUsers.length})
              </Button>
            )}
            <Button
              variant="primary"
              size="auto"
              onClick={() => {
                setOpenedModalId('newUserModal');
              }}
            >
              Add New User
            </Button>
          </Flex>
        </Flex>
        <Flex justify="space-between" w="100%" align="center" my={16}>
          <Checkbox
            label="View Admins only"
            checked={showAdminsOnly}
            onChange={async (event) => {
              const checked = event.currentTarget.checked;
              setShowAdminsOnly(checked);
              await initialize(checked);
            }}
            color="violet"
          />
          {selectedUsers.length > 0 && (
            <Flex justify="end">
              <MantineButton
                size="compact-sm"
                variant="outline"
                color="#495057"
                onClick={() => setSelectedUsers([])}
              >
                Unselect Rows
              </MantineButton>
            </Flex>
          )}
        </Flex>
        {loading ? (
          <Flex w="100%" mih={300} align="center" justify="center">
            <Loader color="violet" />
          </Flex>
        ) : (
          <Flex className="flex-col-reverse md:flex-col" justify="space-between" w="100%">
            <UserManagementMobileTile
              users={users}
              selectedUsers={selectedUsers}
              setSelectedUsers={setSelectedUsers}
              setSelectedUserToEdit={setSelectedUserToEdit}
              toggleUserAdmin={toggleUserAdmin}
            />
            <UserManagementTable
              users={users}
              selectedUsers={selectedUsers}
              setSelectedUsers={setSelectedUsers}
              setSelectedUserToEdit={setSelectedUserToEdit}
              toggleUserAdmin={toggleUserAdmin}
            />
            <Flex
              mt={32}
              className="mb-[32px] md:mb-0"
              justify="center"
              align="center"
              w="100%"
              hidden={paginationObject.totalPages <= 1}
            >
              <Pagination
                total={paginationObject.totalPages}
                value={paginationObject.page}
                onChange={handlePageNavigation}
              />
            </Flex>
          </Flex>
        )}
      </Flex>
    </Layout>
  );
};

export default authGuardHOC(UserManagementPage, true);
