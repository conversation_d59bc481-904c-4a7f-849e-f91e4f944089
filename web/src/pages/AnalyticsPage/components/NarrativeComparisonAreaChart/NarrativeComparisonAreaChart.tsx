import { useEffect, useState } from 'react';
import { Flex, Space } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { analyticsService } from '@/api/analytics/analyticsService';
import Filters from '@/components/Filters/Filters';
import NarrativeBreakdownModal from '@/components/NarrativeBreakdown/NarrativeBreakdownModal';
import HelpText from '@/components/Texts/HelpText/HelpText';
import { Text } from '@/components/Texts/Text/Text';
import TopTopicBreakdownModal from '@/components/TopTopicBreakdown/TopTopicBreakdownModal';
import { useAppContext } from '@/context/AppContext';
import { formatEnum } from '@/helpers/enumUtils';
import { getMonthRange } from '@/helpers/getMonthRange';
import { scopeFormHelper } from '@/helpers/scopeFormHelper';
import useNarratives from '@/hooks/useNarratives';
import { NarrativeAspectTrendsComparisonProps } from '@/types/analyticsTypes';
import { TeamTypeProps } from '@/types/teamType';
import { FilterForm } from '@/types/timelineTypes';
import { TopicProps } from '@/types/topicType';
import Chart from './Chart';

const COLORS = [
  'violet.06',
  'green.06',
  'blue.06',
  'yellow.06',
  'red.06',
  'purple.06',
  'orange.06',
  'pink.06',
  'brown.06',
];

export default function NarrativeComparisonAreaChart({
  topicOptions,
  groupOptions,
}: {
  topicOptions: TopicProps[];
  groupOptions: TeamTypeProps[];
}) {
  const { userInfo, sessionTenant } = useAppContext();
  const isMicrochipTenantSession = sessionTenant?.id === 'DEFAULT';

  const DEFAULT_STATUS = isMicrochipTenantSession ? 'Microchip Technology Inc' : '';

  const filterForm = useForm<FilterForm>({
    initialValues: {
      multiSelectTopicIds: [],
      aspect: null,
      groups: [],
      dateRange: 'custom',
      customDateRange: '',
    },
  });

  const [mentionsLoading, setMentionsLoading] = useState(true);
  const [mentionsComparison, setMentionsComparison] =
    useState<NarrativeAspectTrendsComparisonProps>({
      topics: [],
      data: [],
    });

  const [sentimentLoading, setSentimentLoading] = useState(true);
  const [sentimentComparison, setSentimentComparison] =
    useState<NarrativeAspectTrendsComparisonProps>({
      topics: [],
      data: [],
    });
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const [modalToOpen, setModalToOpen] = useState<'topic' | 'narrative' | null>(null);

  const { narrativeAspects } = useNarratives();

  useEffect(() => {
    setMentionsComparison({
      topics: [],
      data: [],
    });
    setSentimentComparison({ topics: [], data: [] });

    const searchParams = {
      topicIds: filterForm?.values?.multiSelectTopicIds?.map(Number),
      aspects: [filterForm.values.aspect].filter((aspect): aspect is string => aspect !== null),
      groupIds: filterForm?.values?.groups?.map((g) => g.id),
    };

    if (!searchParams?.topicIds?.length && !searchParams?.groupIds?.length) {
      return;
    }
    setMentionsLoading(true);
    analyticsService
      .getNarrativeMentionsComparison(searchParams)
      .then((response) => {
        setMentionsComparison(response);
      })
      .catch(() => {
        notifications.show({
          title: 'Error loading narrative comparison data',
          message: 'Could not fetch narrative comparison data.',
          color: 'red',
          autoClose: 3000,
        });
      })
      .finally(() => {
        setMentionsLoading(false);
      });

    setSentimentLoading(true);
    analyticsService
      .getNarrativeSentimentComparison(searchParams)
      .then((response) => {
        // Hide -1 sentiment scores
        for (const item of response.data) {
          for (const key in item) {
            if (key === 'date') continue;
            if (item[key] === -1) {
              delete item[key];
            }
          }
        }
        setSentimentComparison(response);
      })
      .catch(() => {
        notifications.show({
          title: 'Error loading narrative comparison data',
          message: 'Could not fetch narrative comparison data.',
          color: 'red',
          autoClose: 3000,
        });
      })
      .finally(() => {
        setSentimentLoading(false);
      });
  }, [filterForm?.values?.multiSelectTopicIds, filterForm.values.aspect, filterForm.values.groups]);

  const series = mentionsComparison.topics.map((topic, i) => ({
    name: topic,
    color: COLORS[i % COLORS.length],
  }));

  const topicData = topicOptions.map((topic) => ({
    value: `${topic.id}`,
    label: topic.name,
  }));

  const setDefaultFilters = () => {
    const groups = groupOptions.filter(
      (group) => group?.id === userInfo?.groups?.[0]?.id && (group?.topics?.length ?? 0) > 0
    );
    if (groups.length) {
      filterForm.setValues({
        groups,
      });
    } else {
      const defaultTrendsComparisonStatus = topicData?.find(
        (status) => status.label === DEFAULT_STATUS
      );
      filterForm.setFieldValue(
        'multiSelectTopicIds',
        defaultTrendsComparisonStatus
          ? [defaultTrendsComparisonStatus.value]
          : topicData[0]?.value
            ? [topicData[0].value]
            : []
      );
    }
  };

  const handleTopicModalOpen = (dp: any) => {
    filterForm.setValues({
      customDateRange: getMonthRange(dp.payload.date),
    });
    setModalToOpen('topic');
    const id = topicOptions.find((o) => dp?.dataKey === o.name)?.id;
    if (id != null) setSelectedId(id);
  };

  const handleNarrativeModalOpen = async (dp: any) => {
    if (!filterForm.getValues().aspect) {
      notifications.show({
        title: 'Please select a narrative from the dropdown to view the monthly breakdown.',
        message: '',
        color: 'yellow',
        autoClose: 3000,
      });
      return;
    }

    filterForm.setValues({
      customDateRange: getMonthRange(dp.payload.date),
    });
    const topicId = topicOptions.find((o) => dp?.dataKey === o.name)?.id;
    const { aspect } = filterForm.values;
    setModalToOpen('narrative');

    analyticsService
      .getTopNarratives({
        topicIds: [Number(topicId)],
        aspects: aspect ? [aspect] : undefined,
      })
      .then((resp) => {
        setSelectedId(resp.items[0].id);
      })
      .catch(() => {
        notifications.show({
          title: 'Error loading narrative breakdown modal data',
          message: '',
          color: 'red',
          autoClose: 3000,
        });
      });
  };

  const handleCloseModal = () => {
    setModalToOpen(null);
    setSelectedId(null);
  };

  useEffect(setDefaultFilters, [topicOptions]);

  const formattedAspect = filterForm.values.aspect ? formatEnum(filterForm.values.aspect) : '';

  const isSentimentMissingFilters = !sentimentLoading && sentimentComparison.topics.length === 0;
  const isMentionsMissingFilters = !mentionsLoading && mentionsComparison.topics.length === 0;

  return (
    <Flex direction="column" gap={18}>
      <TopTopicBreakdownModal
        topicId={selectedId ?? 0}
        initialFilterForm={scopeFormHelper(filterForm, ['dateRange', 'customDateRange'] as const)}
        opened={modalToOpen === 'topic'}
        onClose={handleCloseModal}
        onExitTransitionEnd={handleCloseModal}
      />
      <NarrativeBreakdownModal
        opened={modalToOpen === 'narrative'}
        onClose={handleCloseModal}
        onExitTransitionEnd={handleCloseModal}
        filterForm={scopeFormHelper(filterForm, ['dateRange', 'customDateRange'] as const)}
        narrativeId={selectedId}
      />
      <div>
        <Text size="2lg" bold>
          Trends Comparison
        </Text>
        <HelpText
          text="View narrative trends over time to understand how the number of mentions and sentiment are evolving over time. The trends analysis compares changes across different topics for a narrative, helping you identify shifts in public perception."
          modalPage="analytics"
          className="mb-4"
        />
      </div>
      <Filters
        filterForm={scopeFormHelper(filterForm, [
          'multiSelectTopicIds',
          'aspect',
          'groups',
        ] as const)}
        topicOptions={topicOptions}
        groupOptions={groupOptions}
        narrativeAspectOptions={narrativeAspects}
      />

      <Text size="2lg" bold className="text-center">
        {formattedAspect && `${formattedAspect} - `}
        Mentions
      </Text>
      <Chart
        loading={mentionsLoading}
        data={mentionsComparison.data}
        dataKey="date"
        series={series}
        isMissingFilters={isMentionsMissingFilters}
        onPointClick={handleTopicModalOpen}
      />
      <Space />
      <Text size="2lg" bold className="text-center">
        {formattedAspect && `${formattedAspect} - `}
        Sentiment
      </Text>
      <Chart
        loading={sentimentLoading}
        data={sentimentComparison.data}
        dataKey="date"
        series={series}
        isMissingFilters={isSentimentMissingFilters}
        onPointClick={handleNarrativeModalOpen}
      />
    </Flex>
  );
}
