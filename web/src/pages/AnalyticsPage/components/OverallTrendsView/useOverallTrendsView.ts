import { useEffect, useMemo, useState } from 'react';
import { useForm } from '@mantine/form';
import { analyticsService } from '@/api/analytics/analyticsService';
import { timelineService } from '@/api/timeline/timelineService';
import { getDateRangeParams } from '@/helpers/dateUtils';
import { notifications } from '@/helpers/notifications';
import { AnalyticsRequestProps, TrendsHistogramAnalyticsItemProps } from '@/types/analyticsTypes';
import { FilterForm, TimelineProps } from '@/types/timelineTypes';

export const useOverallTrendsView = () => {
  const [topicTrends, setTopicTrends] = useState<TrendsHistogramAnalyticsItemProps[]>([]);
  const [topNarratives, setTopNarratives] = useState<TrendsHistogramAnalyticsItemProps[]>([]);
  const [selectedTopicId, setSelectedTopicId] = useState<number | null>(null);
  const [isTopTopicModalOpen, setIsTopTopicModalOpen] = useState(false);
  const [selectedNarrativeId, setSelectedNarrativeId] = useState<number | null>(null);
  const [isTopNarrativeModalOpen, setIsTopNarrativeModalOpen] = useState(false);
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({
    tenantAnalytics: false,
    tenantNarratives: false,
    tenantTopConversations: false,
    industryTrendingTopics: false,
    industryTopNarratives: false,
  });

  const [tenantTrends, setTenantTrends] = useState<TrendsHistogramAnalyticsItemProps>({
    id: 0,
    name: '',
    summary: '',
    count: 0,
    sentiment: 0,
    sentimentDistribution: [],
  } as TrendsHistogramAnalyticsItemProps);

  const [tenantNarratives, setTenantNarratives] = useState<TrendsHistogramAnalyticsItemProps[]>([]);
  const [tenantTopConversations, setTenantTopConversatons] = useState<TimelineProps[]>([]);

  const filterForm = useForm<FilterForm>({
    initialValues: {
      dateRange: 'All time',
      customDateRange: '',
      sort: 'RELEVANCE',
    },
  });

  const sortedTrends = useMemo(
    () => [...topicTrends].sort((a, b) => b.count - a.count),
    [topicTrends]
  );
  const sortedNarratives = useMemo(
    () => [...topNarratives].sort((a, b) => b.count - a.count),
    [topNarratives]
  );

  const handleLoadingStates = (updates: Record<string, boolean>) => {
    setLoadingStates((prev) => ({
      ...prev,
      ...updates,
    }));
  };

  const fetchTopicAndTenantNarratives = async () => {
    try {
      handleLoadingStates({ tenantTrends: true, industryTrendingTopics: true });
      const { dateFrom, dateTo } = getDateRangeParams(filterForm.values);
      const params = {
        dateFrom,
        dateTo,
      } as AnalyticsRequestProps;

      const [topicTrendsResponse, tenantTrendsResponse] = await Promise.allSettled([
        analyticsService.getTopicsTrends(params).then((response) => response.items),
        analyticsService.getTenantTopicAnalytics(params),
      ]);

      if (topicTrendsResponse.status === 'fulfilled') setTopicTrends(topicTrendsResponse.value);
      if (tenantTrendsResponse.status === 'fulfilled') setTenantTrends(tenantTrendsResponse.value);

      if (topicTrendsResponse.status === 'rejected') throw topicTrendsResponse.reason;
      if (tenantTrendsResponse.status === 'rejected') throw tenantTrendsResponse.reason;
    } catch (error) {
      notifications.show(
        {
          title: 'Error fetching narratives',
          message: 'There was an error fetching top narratives',
        },
        error
      );
    } finally {
      handleLoadingStates({ tenantTrends: false, industryTrendingTopics: false });
    }
  };

  const fetchTopAndTenantNarratives = async () => {
    handleLoadingStates({ tenantNarratives: true, industryTopNarratives: true });
    try {
      const { dateFrom, dateTo } = getDateRangeParams(filterForm.values);
      const params = {
        dateFrom,
        dateTo,
      } as AnalyticsRequestProps;
      setTopNarratives([]);
      setTenantNarratives([]);
      const topNarrativesResponse = await analyticsService.getTopNarratives(params);
      setTopNarratives(topNarrativesResponse.items);

      const tenantNarrativesResponse = await analyticsService.getTenantNarrativeAnalytics({
        ...params,
      });
      setTenantNarratives(tenantNarrativesResponse);
    } catch (error) {
      notifications.show({
        title: 'Error fetching top narratives',
        message: 'There was an error fetching top narratives',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      handleLoadingStates({ tenantNarratives: false, industryTopNarratives: false });
    }
  };

  const fetchTopTenantConversations = async () => {
    const { sort } = filterForm.values;
    const { dateFrom, dateTo } = getDateRangeParams(filterForm.values);
    setTenantTopConversatons([]);
    handleLoadingStates({ tenantTopConversations: true });
    timelineService
      .fetchTopTenantConversations({
        dateFrom,
        dateTo,
        sort,
      })
      .then((response) => {
        setTenantTopConversatons(response?.data?.items);
      })
      .catch(() => {
        notifications.show({
          title: 'Error fetching data',
          message: 'There was an error fetching top tenant conversations data.',
          autoClose: 3000,
          color: 'red',
        });
      })
      .finally(() => handleLoadingStates({ tenantTopConversations: false }));
  };

  const initialize = async () => {
    await Promise.all([fetchTopicAndTenantNarratives(), fetchTopAndTenantNarratives()]);
  };

  const handleOpenTopTopicModal = (topicId: number) => {
    setSelectedTopicId(topicId);
    setIsTopTopicModalOpen(true);
  };

  const handleOpenTopNarrativeModal = (narrativeId: number) => {
    setSelectedNarrativeId(narrativeId);
    setIsTopNarrativeModalOpen(true);
  };

  useEffect(() => {
    initialize();
  }, [filterForm.values.dateRange]);

  useEffect(() => {
    fetchTopTenantConversations();
  }, [filterForm.values]);

  return {
    selectedTopicId,
    isTopTopicModalOpen,
    selectedNarrativeId,
    isTopNarrativeModalOpen,
    tenantTrends,
    tenantNarratives,
    tenantTopConversations,
    filterForm,
    sortedTrends,
    sortedNarratives,
    loadingStates,

    setIsTopNarrativeModalOpen,
    setSelectedNarrativeId,
    setIsTopTopicModalOpen,
    setSelectedTopicId,
    handleOpenTopTopicModal,
    handleOpenTopNarrativeModal,
  };
};
