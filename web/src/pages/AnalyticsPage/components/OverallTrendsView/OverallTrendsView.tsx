import { Flex, Loader } from '@mantine/core';
import ExportToCSVButton from '@/components/CSVExport/ExportToCSVButton';
import Filters from '@/components/Filters/Filters';
import NarrativeBreakdownModal from '@/components/NarrativeBreakdown/NarrativeBreakdownModal';
import { Text } from '@/components/Texts/Text/Text';
import TopTopicBreakdownModal from '@/components/TopTopicBreakdown/TopTopicBreakdownModal';
import { scopeFormHelper } from '@/helpers/scopeFormHelper';
import TenantAnalytics from './components/TenantAnalytics';
import TenantConversations from './components/TenantConversatons';
import TrendsViewHistogram from './TrendsViewHistogram';
import { useOverallTrendsView } from './useOverallTrendsView';

const OverallTrendsView = () => {
  const {
    selectedTopicId,
    isTopTopicModalOpen,
    selectedNarrativeId,
    isTopNarrativeModalOpen,
    tenantTrends,
    tenantNarratives,
    tenantTopConversations,
    filterForm,
    sortedTrends,
    sortedNarratives,
    loadingStates,

    setIsTopNarrativeModalOpen,
    setSelectedNarrativeId,
    setIsTopTopicModalOpen,
    setSelectedTopicId,
    handleOpenTopTopicModal,
    handleOpenTopNarrativeModal,
  } = useOverallTrendsView();

  return (
    <>
      <Flex direction="column" w="100%" gap={28}>
        <Filters
          filterForm={scopeFormHelper(filterForm, ['dateRange', 'customDateRange'] as const)}
          showResetButton={false}
        />
        <Flex
          direction="column"
          w="100%"
          gap={28}
          className="mt-[6px]"
          component="section"
          align={{ base: 'center', sm: 'start' }}
        >
          <TenantAnalytics
            tenantTrends={tenantTrends}
            tenantNarratives={tenantNarratives}
            loadingStates={loadingStates}
            handleOpenTopNarrativeModal={handleOpenTopNarrativeModal}
          />
          <TenantConversations
            filterForm={filterForm}
            tenantTopConversations={tenantTopConversations}
            loadingStates={loadingStates}
          />
          <Flex
            direction="column"
            w="fit-content"
            gap={7}
            align={{ base: 'center', sm: 'start' }}
            aria-labelledby="industry-trending-topics-section"
          >
            <Flex gap={12} align="center" justify="space-between" w="100%">
              <Text size="2lg" bold id="industry-trending-topics-heading">
                Industry Trending Topics
              </Text>
              <ExportToCSVButton href="/api/insights/analytics/trends/topics.csv" />
            </Flex>
            {loadingStates.industryTrendingTopics ? (
              <Flex w="100%" mih={150} align="center" justify="center">
                <Loader color="violet" />
              </Flex>
            ) : (
              <div className="max-w-[800px] w-max grid grid-cols-2 gap-[12px] lg:w-full lg:flex lg:flex-row">
                {sortedTrends.slice(0, 4).map((trend) => (
                  <button
                    type="button"
                    className="cursor-pointer group flex flex-col gap-1 max-w-[160px]"
                    key={trend.id}
                    onClick={() => handleOpenTopTopicModal(trend.id)}
                  >
                    <TrendsViewHistogram trendHistogramItem={trend} />
                    <Text
                      size="2lg"
                      className="wrap text-start group-hover:font-semibold inline"
                      component="h5"
                    >
                      {trend.name}
                    </Text>
                  </button>
                ))}
              </div>
            )}
          </Flex>
        </Flex>
        <Flex
          direction="column"
          w="100%"
          gap={7}
          align={{ base: 'center', sm: 'start' }}
          aria-labelledby="top-narratives-section"
        >
          <Flex gap={12} align="center">
            <Text size="2lg" bold id="top-narratives-heading" component="h2">
              Industry Top Narratives
            </Text>
            <ExportToCSVButton href="/api/insights/analytics/trends/narratives.csv" />
          </Flex>
          {loadingStates.industryTopNarratives ? (
            <Flex w="100%" mih={150} align="center" justify="center">
              <Loader color="violet" />
            </Flex>
          ) : (
            <div className="max-w-[800px] w-max grid grid-cols-2 gap-[12px] lg:w-full lg:flex lg:flex-row">
              {sortedNarratives.slice(0, 4).map((narrative) => (
                <button
                  type="button"
                  className="cursor-pointer group flex flex-col gap-1 max-w-[160px]"
                  key={narrative.id}
                  onClick={() => handleOpenTopNarrativeModal(narrative.id)}
                >
                  <TrendsViewHistogram trendHistogramItem={narrative} />
                  <Text
                    size="2lg"
                    className="wrap text-start group-hover:font-semibold inline"
                    component="h5"
                  >
                    {narrative.summary}
                  </Text>
                </button>
              ))}
            </div>
          )}
        </Flex>
      </Flex>
      <TopTopicBreakdownModal
        topicId={selectedTopicId ?? 0}
        initialFilterForm={scopeFormHelper(filterForm, ['dateRange', 'customDateRange'] as const)}
        opened={isTopTopicModalOpen}
        onClose={() => setIsTopTopicModalOpen(false)}
        onExitTransitionEnd={() => setSelectedTopicId(null)}
      />
      <NarrativeBreakdownModal
        narrativeId={selectedNarrativeId ?? 0}
        filterForm={scopeFormHelper(filterForm, ['dateRange', 'customDateRange'] as const)}
        opened={isTopNarrativeModalOpen}
        onClose={() => setIsTopNarrativeModalOpen(false)}
        onExitTransitionEnd={() => setSelectedNarrativeId(null)}
      />
    </>
  );
};

export default OverallTrendsView;
