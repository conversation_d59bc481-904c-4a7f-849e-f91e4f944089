import { useEffect, useRef } from 'react';
import { EmblaCarouselType } from 'embla-carousel';
import { Carousel } from '@mantine/carousel';
import { Flex, Loader } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import Button from '@/components/Button/Button';
import Filters from '@/components/Filters/Filters';
import { Text } from '@/components/Texts/Text/Text';
import OverviewCards from '@/pages/AnalyticsPage/components/OverallTrendsView/components/OverviewCards';
import { TimelineProps } from '@/types/timelineTypes';

import '@mantine/carousel/styles.css';

import { UseFormReturnType } from '@mantine/form';
import { useAppContext } from '@/context/AppContext';
import { scopeFormHelper } from '@/helpers/scopeFormHelper';

const TenantConversations = ({
  tenantTopConversations,
  filterForm,
  loadingStates,
}: {
  tenantTopConversations: TimelineProps[];
  filterForm: UseFormReturnType<Record<string, any>>;
  loadingStates: Record<string, boolean>;
}) => {
  const { sessionTenant } = useAppContext();
  const carouselRef = useRef<EmblaCarouselType | null>(null);
  const isSmallScreen = useMediaQuery('(max-width: 992px)');

  const sortOnlyForm = scopeFormHelper(filterForm, ['sort'] as const);

  useEffect(() => {
    if (!carouselRef.current) return;
    carouselRef.current.reInit?.();
    requestAnimationFrame(() => carouselRef.current?.scrollTo(0));
  }, [tenantTopConversations]);

  return (
    <Flex direction="column" w="100%" gap={14} align="start">
      <Flex
        direction="row"
        justify="space-between"
        align="center"
        hidden={tenantTopConversations.length === 0}
        w="100%"
      >
        <Text size="2lg" bold id="top-tenant-conversations-heading">
          Top {sessionTenant?.name} Conversations
        </Text>
        <Flex direction="row" gap={16}>
          <Button
            className="bg-[#868E961A] cursor-pointer rounded-sm py-[10px] px-[9px]"
            onClick={() => carouselRef.current?.scrollPrev()}
          >
            <img
              className="min-w-[21.33px] min-h-[18.67px]"
              src="/icons/timelineIcons/navigateArrowIcon.svg"
              alt="top convo left icon"
            />
          </Button>
          <Button
            className="bg-[#868E961A] cursor-pointer rounded-sm py-[10px] px-[9px]"
            onClick={() => carouselRef.current?.scrollNext()}
          >
            <img
              className="rotate-180 min-w-[21.33px] min-h-[18.67px]"
              src="/icons/timelineIcons/navigateArrowIcon.svg"
              alt="top convo right icon"
            />
          </Button>
        </Flex>
      </Flex>
      <Filters filterForm={sortOnlyForm} showResetButton={false} />
      {loadingStates.tenantTopConversations ? (
        <Flex w="100%" mih={200} align="center" justify="center">
          <Loader color="violet" />
        </Flex>
      ) : (
        <div className="w-full" hidden={tenantTopConversations.length === 0}>
          <Carousel
            slideSize={{ base: '100%', md: '50%' }}
            slideGap="md"
            emblaOptions={{
              loop: false,
              align: 'start',
              slidesToScroll: isSmallScreen ? 1 : 2,
            }}
            getEmblaApi={(api) => {
              carouselRef.current = api;
            }}
            withControls={false}
          >
            {isSmallScreen
              ? tenantTopConversations
                  ?.reduce((acc: TimelineProps[][], _, idx, arr: TimelineProps[]) => {
                    if (idx % 2 === 0) {
                      acc.push(arr.slice(idx, idx + 2));
                    }
                    return acc;
                  }, [])
                  .map((pair, index) => (
                    <Carousel.Slide key={`mobile-slide-${index}`}>
                      <Flex direction="column" gap={12}>
                        {pair.map((timeline) => (
                          <OverviewCards timeline={timeline} key={timeline.id} />
                        ))}
                      </Flex>
                    </Carousel.Slide>
                  ))
              : tenantTopConversations?.map((timeline) => (
                  <Carousel.Slide key={timeline.id}>
                    <OverviewCards timeline={timeline} />
                  </Carousel.Slide>
                ))}
          </Carousel>
        </div>
      )}
    </Flex>
  );
};

export default TenantConversations;
