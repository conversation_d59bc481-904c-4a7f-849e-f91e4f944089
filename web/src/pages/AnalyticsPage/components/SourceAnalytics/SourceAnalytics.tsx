import React, { useEffect, useState } from 'react';
import { Flex, Loader } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { analyticsService } from '@/api/analytics/analyticsService';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';
import { SubredditSourceAnalyticsProps } from '@/types/analyticsTypes';
import SourceAnalyticsHistogramCard from './SourceAnalyticsHistogramCard';

const SourceAnalytics = () => {
  const { sessionTenant } = useAppContext();
  const isMicrochipTenantSession = sessionTenant?.id === 'DEFAULT';
  const [sourceAnalyticsData, setSourceAnalyticsData] = useState<SubredditSourceAnalyticsProps[]>(
    []
  );
  const [loading, setLoading] = useState(false);

  const initialize = async () => {
    try {
      setLoading(true);
      const response = await analyticsService.getSubredditsSentiment();
      setSourceAnalyticsData(response);
    } catch {
      notifications.show({
        title: 'Error fetching source analytics',
        message: 'There was an error fetching source analytics data.',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    initialize();
  }, []);

  return loading ? (
    <Flex w="100%" mih={300} align="center" justify="center">
      <Loader color="violet" />
    </Flex>
  ) : (
    <Flex direction="column" w="100%" gap={8}>
      <Text size="2lg" bold>
        {isMicrochipTenantSession
          ? 'Top subreddits by positive and negative sentiment toward Microchip'
          : 'Top subreddits by positive and negative sentiment'}
      </Text>
      <Flex direction="column" gap={8}>
        {sourceAnalyticsData?.map((data, index) => {
          return <SourceAnalyticsHistogramCard key={index} sourceAnalyticsData={data} />;
        })}
      </Flex>
    </Flex>
  );
};

export default SourceAnalytics;
