import { useEffect, useState } from 'react';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { groupService } from '@/api/group/groupService';
import { tagService } from '@/api/tags/tagService';
import { topicService } from '@/api/topics/topicService';
import { TeamTypeProps } from '@/types/teamType';
import { FilterForm } from '@/types/timelineTypes';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicProps } from '@/types/topicType';

export const useAnalytics = () => {
  const [tagOptions, setTagOptions] = useState<TopicGroupProps[]>([]);
  const [topicOptions, setTopicOptions] = useState<TopicProps[]>([]);
  const [groupOptions, setGroupOptions] = useState<TeamTypeProps[]>([]);

  const [loading, setLoading] = useState(false);

  const filterForm = useForm<FilterForm>({
    initialValues: {
      dateRange: null,
      timeZone: 'America/Phoenix',
      topicId: null,
      groups: [],
      tags: [],
      sentiments: [],
      sources: null,
    },
  });

  const initialize = async () => {
    setLoading(true);
    filterForm.reset();
    Promise.all([
      topicService.fetchTopics(),
      tagService.fetchUserTags(),
      groupService.fetchGroups(),
    ])
      .then(([topicResponse, tagResponse, groupResponse]) => {
        setTopicOptions(topicResponse.data.items as TopicProps[]);
        setTagOptions(tagResponse);
        setGroupOptions(groupResponse.data.items as TeamTypeProps[]);
      })
      .catch(() => {
        notifications.show({
          title: 'Initialization Error',
          message: 'There was an error initializing the analytics page.',
          autoClose: 3000,
          color: 'red',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    initialize();
  }, []);

  return {
    tagOptions,
    topicOptions,
    groupOptions,
    filterForm,
    loading,
    initialize,
  };
};
