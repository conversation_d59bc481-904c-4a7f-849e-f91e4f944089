import { useCallback, useEffect, useMemo, useState } from 'react';
import { useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { tagService } from '@/api/tags/tagService';
import { useAppContext } from '@/context/AppContext';
import { notifications } from '@/helpers/notifications';
import { ModalTypeId } from '@/types/globalTypes';
import { TeamTypeProps } from '@/types/teamType';
import { FilterForm } from '@/types/timelineTypes';
import { TopicGroupProps } from '@/types/topicGroupTypes';

export const useTopicGroupsTab = (teamOptions: TeamTypeProps[]) => {
  const theme = useMantineTheme();
  const topicGroupPillTheme = theme.other.pill.tagPill;

  const { userInfo } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [topicGroups, setTopicGroups] = useState<TopicGroupProps[]>([]);
  const [openedModalId, setOpenedModalId] = useState<ModalTypeId>('');
  const [selectedTopicGroup, setSelectedTopicGroup] = useState<TopicGroupProps | null>(null);
  const [userTeamResolved, setUserTeamResolved] = useState(false);

  const selectedTopicGroups = useMemo(
    () => topicGroups.filter((t) => !!t.isSelected),
    [topicGroups]
  );

  const filterForm = useForm<FilterForm>({
    initialValues: {
      multiSelectTopicIds: [],
      personalTopicGroupsOnly: false,
      groups: [],
    },
  });

  const initialize = useCallback(async () => {
    setLoading(true);
    try {
      const { groups, multiSelectTopicIds, personalTopicGroupsOnly } = filterForm.values;

      const personalTagsPromise = tagService.fetchTags({ personalTagsOnly: true });
      const filteredTagsPromise = personalTopicGroupsOnly
        ? { data: { items: [] } }
        : tagService.fetchTags({
            topicIds: multiSelectTopicIds,
            groupIds: groups?.map((t) => t.id),
            excludePersonalTags: true,
          });

      const resp = await Promise.all([personalTagsPromise, filteredTagsPromise]);
      const items = [...resp[0].data.items, ...resp[1].data.items] as TopicGroupProps[];
      setTopicGroups(items.map((t) => ({ ...t, isSelected: false })));
    } catch {
      notifications.show({
        title: 'Error fetching data',
        message: 'There was an error fetching topic groups.',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  }, [filterForm.values]);

  const deleteSelectedTopicGroups = useCallback(
    async (toDelete: TopicGroupProps[]) => {
      try {
        await Promise.all(toDelete.map((t) => tagService.deleteTags(String(t?.id))));
        setSelectedTopicGroup(null);
        notifications.show({
          title: 'Selected topic groups were deleted successfully!',
          autoClose: 3000,
          color: 'teal',
          message: '',
        });
      } catch (error) {
        notifications.show(
          {
            title: 'Error deleting topic groups',
            message: 'There was an error deleting topic groups',
          },
          error
        );
      } finally {
        await initialize();
        setOpenedModalId('');
        setSelectedTopicGroup(null);
      }
    },
    [initialize]
  );

  useEffect(() => {
    if (userTeamResolved) {
      initialize();
    } else if (userInfo && teamOptions.length) {
      const userTeamIds = (userInfo?.groups ?? []).map((g) => g.id);
      const teams = teamOptions.filter((t): t is TeamTypeProps =>
        userTeamIds.includes(t.id as number)
      );
      filterForm.setValues({ groups: teams });
      setUserTeamResolved(true);
    }
  }, [filterForm.values, userInfo, teamOptions]);

  return {
    loading,
    topicGroups,
    openedModalId,
    selectedTopicGroup,
    selectedTopicGroups,
    topicGroupPillTheme,
    filterForm,

    setTopicGroups,
    setOpenedModalId,
    setSelectedTopicGroup,

    initialize,
    deleteSelectedTopicGroups,
  };
};
