import { Flex, Loader, Pill } from '@mantine/core';
import Button from '@/components/Button/Button';
import Filters from '@/components/Filters/Filters';
import AddTagModal from '@/components/Modal/AddTagModal';
import { AreYouSureModal as DeleteTagModal } from '@/components/Modal/AreYouSureModal/AreYouSureModal';
import EditTagModal from '@/components/Modal/EditTagModal';
import SingleObjectNotificationSettingModal from '@/components/Modal/SingleObjectNotificationSettingModal';
import TopicGroupsTable from '@/components/Tables/TopicGroupsTable';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { TeamTypeProps } from '@/types/teamType';
import { TopicProps } from '@/types/topicType';
import { useTopicGroupsTab } from './useTopicGroupsTab';

interface TopicGroupsTabProps {
  teamOptions: TeamTypeProps[];
  topicOptions: TopicProps[];
  optionsLoading: boolean;
}

const TopicGroupsTab = ({ teamOptions, topicOptions, optionsLoading }: TopicGroupsTabProps) => {
  const {
    loading,
    topicGroups,
    openedModalId,
    selectedTopicGroup,
    selectedTopicGroups,
    topicGroupPillTheme,
    filterForm,

    setTopicGroups,
    setOpenedModalId,
    setSelectedTopicGroup,

    initialize,
    deleteSelectedTopicGroups,
  } = useTopicGroupsTab(teamOptions);

  return (
    <Flex direction="column" align="start" className="mt-[32px]">
      <AddTagModal
        onClose={async () => {
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          await initialize();
        }}
        opened={openedModalId === 'newTopicGroupModal'}
        topicOptions={topicOptions}
        groupOptions={teamOptions}
      />
      <EditTagModal
        tag={selectedTopicGroup}
        onClose={async () => {
          setSelectedTopicGroup(null);
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          await initialize();
          setSelectedTopicGroup(null);
          setOpenedModalId('');
        }}
        deleteSelectedTag={async () => {
          await deleteSelectedTopicGroups([selectedTopicGroup!]);
        }}
        opened={!!selectedTopicGroup && openedModalId === 'editTopicGroupModal'}
        topicOptions={topicOptions}
        groupOptions={teamOptions}
      />
      <DeleteTagModal
        onClose={() => setOpenedModalId('')}
        onConfirm={async () => {
          await deleteSelectedTopicGroups(selectedTopicGroups);
        }}
        title="Delete Topic Group"
        heading={`Are you sure you want to delete ${selectedTopicGroups.length > 1 ? 'these topic groups?' : 'this topic group?'}`}
        targetObject={
          <Flex wrap="wrap" justify="center" align="center" gap={8}>
            {selectedTopicGroups.map((tg) => (
              <Pill
                variant="secondary"
                withRemoveButton={false}
                style={{
                  backgroundColor: topicGroupPillTheme.root.backgroundColor,
                  color: topicGroupPillTheme.root.color,
                  height: 'auto',
                  overflow: 'visible',
                }}
              >
                <Flex direction="row" align="center" className="py-[4px] px-[8px]">
                  <Text size="xl">{tg?.name}</Text>
                </Flex>
              </Pill>
            ))}
          </Flex>
        }
        description="This action cannot be undone."
        opened={openedModalId === 'deleteTopicGroupModal' && selectedTopicGroups.length > 0}
      />
      <SingleObjectNotificationSettingModal
        opened={openedModalId === 'singleObjectNotificationSettingModal'}
        onClose={() => {
          setOpenedModalId('');
          setSelectedTopicGroup(null);
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          setSelectedTopicGroup(null);
          await initialize();
        }}
        object={selectedTopicGroup}
        type="TOPIC_GROUP"
      />
      <Flex direction="row" align="start" justify="space-between" w="100%" gap={24} mb={16}>
        <Heading tagLevel="3">Topic Groups</Heading>
        <Flex direction="row" gap={12} wrap="wrap">
          {selectedTopicGroups.length > 0 && (
            <div className="!gap-[12px] flex-wrap hidden md:flex">
              <Button
                variant="secondaryDanger"
                size="auto"
                onClick={() => {
                  setOpenedModalId('deleteTopicGroupModal');
                }}
              >
                Delete Selected ({selectedTopicGroups.length})
              </Button>
            </div>
          )}
          <Button
            variant="primary"
            size="auto"
            onClick={() => {
              setOpenedModalId('newTopicGroupModal');
            }}
          >
            Add Topic Group
          </Button>
        </Flex>
      </Flex>
      <Filters
        filterForm={filterForm}
        topicOptions={topicOptions}
        groupOptions={teamOptions}
        showResetButton={false}
        showFiltersHeading
        allSpaceEqual
      />
      {loading || optionsLoading ? (
        <Flex w="100%" mih={300} align="center" justify="center">
          <Loader color="violet" />
        </Flex>
      ) : (
        <Flex className="flex-col-reverse md:flex-col" justify="space-between" h="100%" w="100%">
          <TopicGroupsTable
            topicGroups={topicGroups}
            setTopicGroups={setTopicGroups}
            setSelectedTopicGroup={setSelectedTopicGroup}
            setOpenedModalId={setOpenedModalId}
          />
        </Flex>
      )}
    </Flex>
  );
};

export default TopicGroupsTab;
