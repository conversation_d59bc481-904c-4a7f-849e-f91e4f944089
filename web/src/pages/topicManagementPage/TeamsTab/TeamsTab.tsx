import { Flex, Loader, Pill } from '@mantine/core';
import Filters from '@/components/Filters/Filters';
import AddGroupModal from '@/components/Modal/AddGroupModal';
import { AreYouSureModal as DeleteGroupModal } from '@/components/Modal/AreYouSureModal/AreYouSureModal';
import EditGroupModal from '@/components/Modal/EditGroupModal';
import SingleObjectNotificationSettingModal from '@/components/Modal/SingleObjectNotificationSettingModal';
import TeamsTable from '@/components/Tables/TeamsTable';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { useIsAdmin } from '@/context/AppContext';
import { TopicProps } from '@/types/topicType';
import Button from '../../../components/Button/Button';
import { useTeamsTab } from './useTeamsTab';

interface TeamsTabProps {
  topicOptions: TopicProps[];
  optionsLoading: boolean;
}

const TeamsTab = ({ topicOptions, optionsLoading }: TeamsTabProps) => {
  const {
    loading,
    teams,
    tagOptions,
    selectedTeam,
    selectedTeams,
    openedModalId,
    groupPillTheme,
    filterForm,

    setTeams,
    setSelectedTeam,
    setOpenedModalId,

    initialize,
    deleteSelectedTeams,
    toggleTeamMembership,
  } = useTeamsTab();

  const isAdmin = useIsAdmin();

  return (
    <Flex direction="column" align="start" className="mt-[32px]">
      <EditGroupModal
        group={selectedTeam}
        topicGroupOptions={tagOptions}
        onClose={() => {
          setSelectedTeam(null);
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          setSelectedTeam(null);
          setOpenedModalId('');
          await initialize();
        }}
        deleteSelectedGroup={async () => {
          await deleteSelectedTeams([selectedTeam!]);
        }}
        opened={!!selectedTeam && openedModalId === 'editTeamModal'}
      />
      <AddGroupModal
        onClose={async () => {
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          await initialize();
        }}
        opened={openedModalId === 'addTeamModal'}
        topicGroupOptions={tagOptions}
      />
      <DeleteGroupModal
        onClose={() => setOpenedModalId('')}
        onConfirm={async () => {
          await deleteSelectedTeams(selectedTeams);
        }}
        title="Delete Team"
        heading={`Are you sure you want to delete ${selectedTeams.length > 1 ? 'these teams?' : 'this team?'}`}
        targetObject={
          <Flex wrap="wrap" justify="center" align="center" gap={8}>
            {selectedTeams.map((t) => (
              <Pill
                variant="secondary"
                withRemoveButton={false}
                style={{
                  backgroundColor: groupPillTheme.root.backgroundColor,
                  border: groupPillTheme.root.border,
                  height: 'auto',
                  overflow: 'visible',
                }}
              >
                <Flex direction="row" align="center" className="py-[4px] px-[8px]">
                  <img
                    src="/icons/pills/pillUserIcon.svg"
                    alt="User Icon"
                    className="min-w-[14px] min-h-[20px] mr-[13px]"
                  />
                  <Text size="xl">{t?.name}</Text>
                </Flex>
              </Pill>
            ))}
          </Flex>
        }
        description="This action cannot be undone."
        opened={openedModalId === 'deleteTeamModal' && selectedTeams.length > 0}
      />
      <SingleObjectNotificationSettingModal
        opened={openedModalId === 'singleObjectNotificationSettingModal'}
        onClose={() => {
          setOpenedModalId('');
          setSelectedTeam(null);
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          setSelectedTeam(null);
          await initialize();
        }}
        object={selectedTeam}
        type="TEAM"
      />
      <Flex direction="row" align="start" justify="space-between" w="100%" gap={24} mb={16}>
        <Heading tagLevel="3">Teams</Heading>
        <Flex direction="row" gap={12} wrap="wrap">
          {selectedTeams.length > 0 && (
            <div className="!gap-[12px] flex-wrap hidden md:flex">
              <Button
                variant="secondaryDanger"
                size="auto"
                onClick={() => {
                  setOpenedModalId('deleteTeamModal');
                }}
              >
                Delete Selected ({selectedTeams.length})
              </Button>
            </div>
          )}
          <Button
            hidden={!isAdmin}
            variant="primary"
            size="auto"
            onClick={() => {
              setOpenedModalId('addTeamModal');
            }}
          >
            Add Team
          </Button>
        </Flex>
      </Flex>
      <Filters
        filterForm={filterForm}
        topicOptions={topicOptions}
        tagOptions={tagOptions}
        showResetButton={false}
        showFiltersHeading
        allSpaceEqual
      />

      {loading || optionsLoading ? (
        <Flex w="100%" mih={300} align="center" justify="center">
          <Loader color="violet" />
        </Flex>
      ) : (
        <Flex className="flex-col-reverse md:flex-col" justify="space-between" h="100%" w="100%">
          <TeamsTable
            teams={teams}
            setTeams={setTeams}
            setSelectedTeam={setSelectedTeam}
            setOpenedModalId={setOpenedModalId}
            toggleTeamMembership={toggleTeamMembership}
          />
        </Flex>
      )}
    </Flex>
  );
};

export default TeamsTab;
