import { useCallback, useEffect, useMemo, useState } from 'react';
import { useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { groupService } from '@/api/group/groupService';
import { tagService } from '@/api/tags/tagService';
import { useAppContext } from '@/context/AppContext';
import { notifications } from '@/helpers/notifications';
import { ModalTypeId } from '@/types/globalTypes';
import { TeamTypeProps } from '@/types/teamType';
import { FilterForm } from '@/types/timelineTypes';
import { TopicGroupProps } from '@/types/topicGroupTypes';

export const useTeamsTab = () => {
  const theme = useMantineTheme();
  const groupPillTheme = theme.other.pill.groupPill;

  const [loading, setLoading] = useState(true);
  const [teams, setTeams] = useState<TeamTypeProps[]>([]);
  const [tagOptions, setTagOptions] = useState<TopicGroupProps[]>([]);
  const [openedModalId, setOpenedModalId] = useState<ModalTypeId>('');
  const [selectedTeam, setSelectedTeam] = useState<TeamTypeProps | null>(null);
  const { fetchUserProfile } = useAppContext();

  const selectedTeams = useMemo(() => teams.filter((t) => !!t.isSelected), [teams]);

  const filterForm = useForm<FilterForm>({
    initialValues: {
      tags: [],
      myTeamsOnly: false,
      multiSelectTopicIds: [],
    },
  });

  const fetchTeams = useCallback(async () => {
    try {
      const { tags, multiSelectTopicIds, myTeamsOnly } = filterForm.values;
      const resp = await groupService.fetchGroups({
        tagIds: tags?.map((t) => t.id),
        topicIds: multiSelectTopicIds,
        ...(myTeamsOnly && { myTeamsOnly }),
      });
      const items = (resp?.data?.items ?? []) as TeamTypeProps[];
      setTeams(items.map((t) => ({ ...t, isSelected: false })));
    } catch (error) {
      notifications.show({
        title: 'Error fetching data',
        message: 'There was an error fetching teams.',
        autoClose: 3000,
        color: 'red',
      });
      throw error;
    }
  }, [filterForm.values]);

  const fetchTags = useCallback(async () => {
    const resp = await tagService.fetchTags({ excludePersonalTags: true });
    const items = (resp?.data?.items ?? []) as TopicGroupProps[];
    setTagOptions(items.map((t) => ({ ...t, isSelected: false })));
  }, []);

  const initialize = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([fetchTeams(), fetchTags()]);
    } finally {
      setLoading(false);
    }
  }, [filterForm.values]);

  const deleteSelectedTeams = useCallback(
    async (toDelete: TeamTypeProps[]) => {
      try {
        await Promise.all(toDelete.map((t) => groupService.deleteGroup(String(t?.id))));
        setSelectedTeam(null);
        notifications.show({
          title: 'Selected teams were deleted successfully!',
          autoClose: 3000,
          color: 'teal',
          message: '',
        });
      } catch (error) {
        notifications.show(
          {
            title: 'Error deleting teams',
            message: 'There was an error deleting teams',
          },
          error
        );
      } finally {
        await initialize();
        setSelectedTeam(null);
        setOpenedModalId('');
      }
    },
    [initialize]
  );

  const toggleTeamMembership = useCallback(
    async (teamId: number) => {
      const team = teams.find((t) => t.id === teamId);
      const isMember = team?.isUserMember;

      if (isMember) {
        await groupService.leaveGroup(teamId);
      } else {
        await groupService.joinGroup(teamId);
      }
      await Promise.all([fetchTeams(), fetchUserProfile()]);
    },
    [fetchTeams, teams]
  );

  useEffect(() => {
    void initialize();
  }, [initialize, filterForm.values]);

  return {
    loading,
    teams,
    openedModalId,
    selectedTeam,
    selectedTeams,
    groupPillTheme,
    tagOptions,
    filterForm,

    setTeams,
    setOpenedModalId,
    setSelectedTeam,

    initialize,
    deleteSelectedTeams,
    toggleTeamMembership,
  };
};
