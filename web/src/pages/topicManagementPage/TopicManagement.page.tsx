import { useLocation, useNavigate } from 'react-router-dom';
import { Flex, Tabs } from '@mantine/core';
import { authGuardHOC } from '@/components/authGuardHOC';
import { Layout } from '@/components/Layout/Layout';
import { Heading } from '@/components/Texts/Heading/Heading';
import HelpText from '@/components/Texts/HelpText/HelpText';
import { useAnalytics } from '../AnalyticsPage/useAnalytics';
import TeamsTab from './TeamsTab/TeamsTab';
import TopicGroupsTab from './TopicGroupsTab/TopicGroupsTab';
import TopicsTab from './TopicsTab/TopicsTab';

const TopicManagementPage = () => {
  const {
    tagOptions: topicGroupOptions,
    topicOptions,
    groupOptions: teamOptions,
    loading: optionsLoading,
  } = useAnalytics();
  const activeTab = useLocation().hash?.slice(1) || 'topics';
  const navigate = useNavigate();

  return (
    <Layout>
      <Flex
        direction="column"
        align="start"
        w="100%"
        maw="1280px"
        h="100%"
        gap={32}
        className=" flex-grow py-[24px] px-[12px] md:py-[32px] md:px-[40px]"
      >
        <Flex direction="column" gap={8}>
          <Heading tagLevel="2">Topic & Team Management</Heading>
          <HelpText
            text={
              <>
                Topic & Team Management is where you manage the terms and topics you want to
                monitor. Create and edit topics, assign topic groups, and set up teams to streamline
                your workflow. This page also allows you to set up notification preferences for
                important topics.
              </>
            }
            modalPage="topics"
          />
        </Flex>
        <Tabs
          value={activeTab}
          onChange={(value) => navigate(`#${value}`, { replace: true })}
          defaultValue="topics"
          w="100%"
          h="100%"
          color="violet"
        >
          <Tabs.List>
            <Tabs.Tab value="topics">Topics</Tabs.Tab>
            <Tabs.Tab value="teams">Teams</Tabs.Tab>
            <Tabs.Tab value="topic-groups">Topic Groups</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="topics">
            <TopicsTab
              teamOptions={teamOptions}
              topicGroupOptions={topicGroupOptions}
              optionsLoading={optionsLoading}
            />
          </Tabs.Panel>
          <Tabs.Panel value="teams">
            <TeamsTab topicOptions={topicOptions} optionsLoading={optionsLoading} />
          </Tabs.Panel>
          <Tabs.Panel value="topic-groups">
            <TopicGroupsTab
              teamOptions={teamOptions}
              topicOptions={topicOptions}
              optionsLoading={optionsLoading}
            />
          </Tabs.Panel>
        </Tabs>
      </Flex>
    </Layout>
  );
};

export default authGuardHOC(TopicManagementPage);
