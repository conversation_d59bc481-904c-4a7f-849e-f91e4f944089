import { useEffect, useState } from 'react';
import { useForm } from '@mantine/form';
import { topicService } from '@/api/topics/topicService';
import { useAppContext } from '@/context/AppContext';
import { notifications } from '@/helpers/notifications';
import { ModalTypeId } from '@/types/globalTypes';
import { TeamTypeProps } from '@/types/teamType';
import { FilterForm } from '@/types/timelineTypes';
import { TopicProps } from '@/types/topicType';

export const useTopicsTab = (teamOptions: TeamTypeProps[]) => {
  const { userInfo } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [topics, setTopics] = useState<TopicProps[]>([]);
  const [localTopics, setLocalTopics] = useState<TopicProps[]>([]);
  const [selectedTopics, setSelectedTopics] = useState<TopicProps[]>([]);
  const [selectedTopicToEdit, setSelectedTopicToEdit] = useState<TopicProps | null>(null);
  const [userTeamResolved, setUserTeamResolved] = useState(false);

  const [openedModalId, setOpenedModalId] = useState<ModalTypeId>('');

  const filterForm = useForm<FilterForm>({
    initialValues: {
      tags: [],
      onlyFavourited: false,
      groups: [],
    },
  });

  const fetchTopics = async () => {
    try {
      setLoading(true);
      const { groups, tags, onlyFavourited } = filterForm.values;
      const response = await topicService.fetchTopics({
        groupIds: groups?.map((g) => g.id),
        tagIds: tags?.map((t) => t.id),
        ...(onlyFavourited && { onlyFavourited }),
      });
      setTopics(response.data.items);
    } catch (error) {
      notifications.show({
        title: `There was an error fetching topics`,
        autoClose: 3000,
        color: 'red',
        message: '',
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteSelectedTopics = async (selectedTopics: TopicProps[]) => {
    try {
      await topicService.deleteTopics(selectedTopics.map((topic) => String(topic.id)));
      setSelectedTopics([]);
      notifications.show({
        title: 'Topic Deleted Successfully!',
        autoClose: 3000,
        color: 'teal',
        message: '',
      });
      await fetchTopics();
    } catch (error) {
      notifications.show(
        {
          title: `There was an error deleting topics`,
        },
        error
      );
    }
  };

  async function toggleFavourite(topicId: number, newValue: boolean) {
    setLocalTopics((prev) =>
      prev.map((topic) =>
        topic.id === topicId
          ? {
              ...topic,
              userTopicPreference: { ...topic.userTopicPreference, isFavourite: newValue },
            }
          : topic
      )
    );
    try {
      await topicService.patchFavouriteStatus([topicId], newValue);
      notifications.show({
        title: 'Favorite status updated',
        message: '',
        color: 'teal',
        autoClose: 3000,
      });
    } catch (err) {
      setLocalTopics((prev) =>
        prev.map((topic) =>
          topic.id === topicId
            ? {
                ...topic,
                userTopicPreference: { ...topic.userTopicPreference, isFavourite: !newValue },
              }
            : topic
        )
      );
      notifications.show({
        title: 'Failed to update favorite',
        message: '',
        color: 'red',
        autoClose: 3000,
      });
    }
  }

  useEffect(() => {
    setLocalTopics(topics);
  }, [topics]);

  useEffect(() => {
    if (userTeamResolved) {
      fetchTopics();
    } else if (userInfo && teamOptions.length) {
      const userTeamIds = (userInfo?.groups ?? []).map((g) => g.id);
      const teams = teamOptions.filter((t): t is TeamTypeProps =>
        userTeamIds.includes(t.id as number)
      );
      filterForm.setValues({ groups: teams });
      setUserTeamResolved(true);
    }
  }, [filterForm.values, userInfo, teamOptions]);

  return {
    selectedTopics,
    selectedTopicToEdit,
    openedModalId,
    loading,
    localTopics,
    filterForm,

    deleteSelectedTopics,
    setSelectedTopics,
    setSelectedTopicToEdit,
    setOpenedModalId,
    fetchTopics,
    toggleFavourite,
  };
};
