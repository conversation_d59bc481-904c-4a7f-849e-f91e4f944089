import { Flex, Loader } from '@mantine/core';
import Button from '@/components/Button/Button';
import Filters from '@/components/Filters/Filters';
import AddTopicModal from '@/components/Modal/addTopicModal/AddTopicModal';
import { AreYouSureModal as DeleteTopicModal } from '@/components/Modal/AreYouSureModal/AreYouSureModal';
import AssignTagsToTopicModal from '@/components/Modal/AssignTagsToTopicModal';
import EditTopicModal from '@/components/Modal/editTopicModal/EditTopicModal';
import NotificationPreferencesModal from '@/components/Modal/NotificationPreferencesModal';
import SingleObjectNotificationSettingModal from '@/components/Modal/SingleObjectNotificationSettingModal';
import Pill from '@/components/Pill/Pill';
import TopicManagementMobileTile from '@/components/Tables/TopicManagementTable/TopicManagementMobileTile';
import TopicManagementTable from '@/components/Tables/TopicManagementTable/TopicManagementTable';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { TeamTypeProps } from '@/types/teamType';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import { useTopicsTab } from './useTopicsTab';

interface TopicsTabProps {
  teamOptions: TeamTypeProps[];
  topicGroupOptions: TopicGroupProps[];
  optionsLoading: boolean;
}

const TopicsTab = ({ teamOptions, topicGroupOptions, optionsLoading }: TopicsTabProps) => {
  const {
    selectedTopics,
    selectedTopicToEdit,
    openedModalId,
    loading,
    localTopics,
    filterForm,

    deleteSelectedTopics,
    setSelectedTopics,
    setSelectedTopicToEdit,
    setOpenedModalId,
    fetchTopics,
    toggleFavourite,
  } = useTopicsTab(teamOptions);

  return (
    <Flex direction="column" align="start" className="mt-[32px]">
      <AddTopicModal
        onClose={() => {
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          await fetchTopics();
        }}
        opened={openedModalId === 'newTopicModal'}
      />
      <EditTopicModal
        topic={selectedTopicToEdit}
        deleteSelectedTopic={async () => {
          await deleteSelectedTopics([selectedTopicToEdit!]);
          setSelectedTopicToEdit(null);
        }}
        onClose={() => {
          setSelectedTopicToEdit(null);
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          setSelectedTopicToEdit(null);
          await fetchTopics();
        }}
        opened={!!selectedTopicToEdit && openedModalId === 'editTopicModal'}
      />
      <DeleteTopicModal
        onClose={() => setOpenedModalId('')}
        onConfirm={async () => {
          await deleteSelectedTopics(selectedTopics);
          await fetchTopics();
          setOpenedModalId('');
        }}
        title={`Delete Topic${selectedTopics.length > 1 ? 's' : ''}`}
        heading={`Are you sure you want to delete ${selectedTopics.length > 1 ? 'these' : 'this'} topic${selectedTopics.length > 1 ? 's' : ''}?`}
        targetObject={
          <Flex wrap="wrap" justify="center" align="center" gap={8}>
            {selectedTopics.map((topic) => {
              return (
                <Pill key={topic.id} variant="secondary" withRemoveButton={false}>
                  <Text size="lg" bold>
                    {topic.name}
                  </Text>
                </Pill>
              );
            })}
          </Flex>
        }
        description="This action cannot be undone."
        opened={openedModalId === 'deleteTopicModal'}
      />
      <AssignTagsToTopicModal
        opened={openedModalId === 'assignTagToTopicModal'}
        onClose={() => {
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          await fetchTopics();
        }}
        topics={selectedTopics}
      />

      <NotificationPreferencesModal
        opened={openedModalId === 'notificationPreferencesModal'}
        onClose={() => {
          setOpenedModalId('');
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          setSelectedTopics([]);
          await fetchTopics();
        }}
        topics={selectedTopics}
        deleteSelectedTopics={async (localSelectedTopics) => {
          await deleteSelectedTopics(localSelectedTopics);
        }}
      />
      <SingleObjectNotificationSettingModal
        opened={openedModalId === 'singleObjectNotificationSettingModal'}
        onClose={() => {
          setOpenedModalId('');
          setSelectedTopicToEdit(null);
        }}
        onSubmit={async () => {
          setOpenedModalId('');
          setSelectedTopicToEdit(null);
          await fetchTopics();
        }}
        object={selectedTopicToEdit}
        type="TOPIC"
      />
      <Flex direction="row" align="start" justify="space-between" w="100%" gap={24} mb={16}>
        <Heading tagLevel="3">Topics</Heading>
        <Flex direction="row" gap={12} wrap="wrap">
          {selectedTopics.length > 0 && (
            <div className="!gap-[12px] flex-wrap hidden md:flex">
              <Button
                variant="secondaryDanger"
                size="auto"
                onClick={() => {
                  setOpenedModalId('deleteTopicModal');
                }}
              >
                Delete Selected ({selectedTopics.length})
              </Button>
              <Button
                variant="secondary"
                size="auto"
                onClick={() => {
                  setOpenedModalId('assignTagToTopicModal');
                }}
              >
                Assign Topic Group ({selectedTopics.length})
              </Button>
              <Button
                variant="secondary"
                size="auto"
                onClick={() => {
                  setOpenedModalId('notificationPreferencesModal');
                }}
              >
                Edit Notification Preferences ({selectedTopics.length})
              </Button>
            </div>
          )}
          <Button
            variant="primary"
            size="auto"
            onClick={() => {
              setOpenedModalId('newTopicModal');
            }}
          >
            Add Topic
          </Button>
        </Flex>
      </Flex>
      <Filters
        filterForm={filterForm}
        tagOptions={topicGroupOptions}
        groupOptions={teamOptions}
        showResetButton={false}
        showFiltersHeading
        allSpaceEqual
      />
      {loading || optionsLoading ? (
        <Flex w="100%" mih={300} align="center" justify="center">
          <Loader color="violet" />
        </Flex>
      ) : (
        <Flex
          className="flex-col-reverse md:flex-col"
          justify="space-between"
          h="100%"
          w="100%"
          mt={16}
        >
          <TopicManagementTable
            selectedTopics={selectedTopics}
            localTopics={localTopics}
            setSelectedTopics={setSelectedTopics}
            setSelectedTopicToEdit={setSelectedTopicToEdit}
            toggleFavourite={toggleFavourite}
            setOpenedModalId={setOpenedModalId}
          />
          <TopicManagementMobileTile
            selectedTopics={selectedTopics}
            localTopics={localTopics}
            setSelectedTopics={setSelectedTopics}
            setSelectedTopicToEdit={setSelectedTopicToEdit}
            setOpenedModalId={setOpenedModalId}
            toggleFavourite={toggleFavourite}
          />
        </Flex>
      )}
    </Flex>
  );
};

export default TopicsTab;
