import qs from 'qs';
import { PaginationRequestParams } from '@/types/globalTypes';
import { PaginatedResponse } from '@/types/paginatedTypes';
import {
  NotificationCadence,
  TopicBulkPatchRequestProps,
  TopicPayload,
  TopicProps,
  TopicRequestQueryProps,
} from '@/types/topicType';
import apiClient from '../apiClient';

export const topicService = {
  fetchTopics: async (
    params: PaginationRequestParams & TopicRequestQueryProps = {
      tagIds: [],
      groupIds: [],
      onlyFavourited: false,
    }
  ) => {
    return await apiClient.get<PaginatedResponse<TopicProps>>('/topics', {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    });
  },

  createTopic: async (topicData: TopicPayload) => {
    return await apiClient.post('/topics', topicData);
  },

  editTopic: async (topicId: number, topicData: TopicPayload) => {
    return await apiClient.put(`/topics/${topicId}`, topicData);
  },

  deleteTopics: async (ids: string[]) => {
    return await apiClient.delete('/topics', {
      data: { topicIds: ids },
    });
  },

  assignTags: async (topicId: number, tagIds: number[]) => {
    return await apiClient.put(`/topics/${topicId}/tags`, tagIds);
  },

  patchPreferences: (payload: TopicBulkPatchRequestProps) =>
    apiClient.patch<void>('/topics/preferences', payload),

  patchFavouriteStatus: (ids: number[], isFavourite: boolean) => {
    const payload = { items: ids.map((id) => ({ id, patch: { isFavourite } })) };
    return topicService.patchPreferences(payload);
  },

  patchNotificationPreferences: (
    items: {
      id: number;
      negativeSentimentNotifications: NotificationCadence;
      notificationCadence: NotificationCadence;
      highRelevanceNotifications: NotificationCadence;
    }[]
  ) => {
    const payload = {
      items: items.map(({ id, ...patch }) => ({
        id,
        patch,
      })),
    };
    return topicService.patchPreferences(payload);
  },
};
