import qs from 'qs';
import { NotificationCadence } from '@/types/topicType';
import apiClient from '../apiClient';

export const tagService = {
  fetchTags: async (params?: Record<string, any>) => {
    return await apiClient.get('/tags', {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    });
  },

  async fetchUserTags() {
    const [personalTags, myTeamsTags] = await Promise.all([
      this.fetchTags({ personalTagsOnly: true }),
      this.fetchTags({ myTeamsOnly: true }),
    ]);
    return [...personalTags.data.items, ...myTeamsTags.data.items];
  },

  createTags: async (tagData: {
    name: string;
    description: string;
    isPersonal: boolean;
    topics?: number[];
    groups?: number[];
  }) => {
    return await apiClient.post('/tags', tagData);
  },

  deleteTags: async (tagId: string) => {
    return await apiClient.delete(`/tags/${tagId}`);
  },

  editTags: async (
    tagId: string,
    tagData: { description: string; topics?: number[]; groups?: number[] }
  ) => {
    return await apiClient.put(`/tags/${tagId}`, tagData);
  },

  patchNotificationPreferences: (
    items: {
      id: number;
      negativeSentimentNotifications: NotificationCadence;
      notificationCadence: NotificationCadence;
      highRelevanceNotifications: NotificationCadence;
    }[]
  ) => {
    const payload = {
      items: items.map(({ id, ...patch }) => ({
        id,
        patch,
      })),
    };
    return apiClient.patch('tags/preferences', payload);
  },
};
