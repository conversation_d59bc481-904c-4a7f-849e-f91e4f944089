import qs from 'qs';
import { TimelineFilterParam } from '@/types/timelineTypes';
import apiClient from '../apiClient';

export const timelineService = {
  fetchTimeline: async ({ analysisId, ...params }: TimelineFilterParam) => {
    const endpoint = analysisId ? `/insights/timeline/${analysisId}` : '/insights/timeline';
    const response = await apiClient.get(endpoint, {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    });
    return response.data;
  },

  fetchTimelineByRelativeURL: async (relativeURL: string, signal?: AbortSignal) => {
    const endpoint = `/insights/timeline${relativeURL}`;
    const response = await apiClient.get(endpoint, { signal });
    return response.data;
  },

  fetchTimelineItem: async (id: number) => apiClient.get(`/insights/timeline/${id}`),

  bookmarkTimeline: async (timelineId: number) =>
    apiClient.post(`/insights/timeline/${timelineId}/bookmark`),

  unBookmarkTimeline: async (timelineId: number) =>
    apiClient.delete(`/insights/timeline/${timelineId}/bookmark`),

  shareAnalysis: async (timelineId: number, groupId: number) =>
    apiClient.post(`/insights/timeline/${timelineId}/groups/${groupId}`),

  fetchTopTenantConversations: async (params?: TimelineFilterParam) =>
    apiClient.get('/insights/timeline/tenant', {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    }),
};
