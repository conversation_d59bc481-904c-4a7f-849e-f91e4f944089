import apiClient from '../apiClient';

interface TruthkeepAnalyticsParams {
  dateFrom?: string;
  dateTo?: string;
}

export interface UserAnalytics {
  firstName: string;
  lastName: string;
  lastAccess: string;
  timeline: number;
  analytics: number;
  topicManagement: number;
}

export interface TruthkeepAnalyticsData {
  users: UserAnalytics[];
}

export const truthkeepAnalyticsService = {
  trackPageAccess: async (): Promise<void> => {
    await apiClient.post('/truthkeep-analytics');
  },

  getAnalytics: async (params?: TruthkeepAnalyticsParams): Promise<TruthkeepAnalyticsData> => {
    const response = await apiClient.get<TruthkeepAnalyticsData>('/truthkeep-analytics', {
      params,
    });
    return response.data;
  },

  getUsers: async (): Promise<string> => {
    const response = await apiClient.get<string>('/truthkeep-analytics/users', {
      responseType: 'text',
    });
    return response.data;
  },
};
