import qs from 'qs';
import {
  AnalyticsRequestProps,
  NarrativeAspectTrendsComparisonProps,
  SubredditSourceAnalyticsProps,
  TopicScatterPlotProps,
  TrendsHistogramAnalyticsItemProps,
} from '@/types/analyticsTypes';
import apiClient from '../apiClient';

export const analyticsService = {
  getTopicsTrends: async (
    params?: AnalyticsRequestProps
  ): Promise<TrendsHistogramAnalyticsItemProps[]> => {
    const response = await apiClient.get<TrendsHistogramAnalyticsItemProps[]>(
      '/insights/analytics/trends/topics',
      {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      }
    );
    return response.data;
  },

  getTopNarratives: async (
    params?: AnalyticsRequestProps
  ): Promise<TrendsHistogramAnalyticsItemProps[]> => {
    const response = await apiClient.get<TrendsHistogramAnalyticsItemProps[]>(
      '/insights/analytics/trends/narratives',
      {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      }
    );
    return response.data;
  },

  getNarrativeMentionsComparison: async (
    params?: AnalyticsRequestProps
  ): Promise<NarrativeAspectTrendsComparisonProps> => {
    const response = await apiClient.get<NarrativeAspectTrendsComparisonProps>(
      '/insights/analytics/comparison/aspects/mentions',
      {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      }
    );
    return response.data;
  },

  getNarrativeSentimentComparison: async (
    params?: AnalyticsRequestProps
  ): Promise<NarrativeAspectTrendsComparisonProps> => {
    const response = await apiClient.get<NarrativeAspectTrendsComparisonProps>(
      '/insights/analytics/comparison/aspects/sentiment',
      {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      }
    );
    return response.data;
  },

  getNarrativeScatterPlot: async (
    params?: AnalyticsRequestProps
  ): Promise<TopicScatterPlotProps[]> => {
    const response = await apiClient.get<TopicScatterPlotProps[]>(
      '/insights/analytics/comparison/narratives',
      {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      }
    );
    return response.data;
  },

  getNarrativeComparisonPopover: async (
    narrativeId: number,
    params?: AnalyticsRequestProps
  ): Promise<TrendsHistogramAnalyticsItemProps> => {
    const response = await apiClient.get<TrendsHistogramAnalyticsItemProps>(
      `/insights/analytics/narratives/${narrativeId}`,
      {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      }
    );
    return response.data;
  },

  getSubredditsSentiment: async (
    params?: AnalyticsRequestProps
  ): Promise<SubredditSourceAnalyticsProps[]> => {
    const response = await apiClient.get<SubredditSourceAnalyticsProps[]>(
      '/insights/analytics/sources/reddit',
      {
        params,
        paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
      }
    );
    return response.data;
  },

  getTopicAnalytics: async (
    topicId: number,
    params: AnalyticsRequestProps
  ): Promise<TrendsHistogramAnalyticsItemProps> => {
    const response = await apiClient(`/insights/analytics/topics/${topicId}`, {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    });
    return response.data;
  },

  getTenantTopicAnalytics: async (
    params: Omit<AnalyticsRequestProps, 'topicIds'>
  ): Promise<TrendsHistogramAnalyticsItemProps> => {
    const response = await apiClient('/insights/analytics/topics/tenant', {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    });
    return response.data;
  },

  getTenantNarrativeAnalytics: async (
    params: Omit<AnalyticsRequestProps, 'topicIds'>
  ): Promise<TrendsHistogramAnalyticsItemProps[]> => {
    const response = await apiClient('/insights/analytics/trends/narratives/tenant', {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    });
    return response.data;
  },
};
