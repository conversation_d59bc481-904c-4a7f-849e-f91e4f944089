import qs from 'qs';
import { TeamPayload, TeamRequestQueryProps } from '@/types/teamType';
import { NotificationCadence } from '@/types/topicType';
import apiClient from '../apiClient';

export const groupService = {
  fetchGroups: async (params?: TeamRequestQueryProps) => {
    return await apiClient.get('/groups', {
      params,
      paramsSerializer: (obj) => qs.stringify(obj, { arrayFormat: 'repeat' }),
    });
  },

  createGroups: async (groupData: TeamPayload) => {
    return await apiClient.post('/groups', groupData);
  },

  deleteGroup: async (groupId: string) => {
    return await apiClient.delete(`/groups/${groupId}`);
  },

  editGroup: async (groupId: string, groupData: TeamPayload) => {
    return await apiClient.put(`/groups/${groupId}`, groupData);
  },

  patchNotificationPreferences: (
    items: {
      id: number;
      negativeSentimentNotifications: NotificationCadence;
      notificationCadence: NotificationCadence;
      highRelevanceNotifications: NotificationCadence;
    }[]
  ) => {
    const payload = {
      items: items.map(({ id, ...patch }) => ({
        id,
        patch,
      })),
    };
    return apiClient.patch<void>('/groups/preferences', payload);
  },

  joinGroup: async (groupId: number) => {
    return await apiClient.put(`/groups/${groupId}/users/me`);
  },

  leaveGroup: async (groupId: number) => {
    return await apiClient.delete(`/groups/${groupId}/users/me`);
  },
};
