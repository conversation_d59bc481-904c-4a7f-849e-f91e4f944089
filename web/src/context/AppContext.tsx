import { createContext, useContext, useEffect, useState } from 'react';
import { authService } from '../api/auth/authService';
import { userService } from '../api/user/userService';

type AppProviderProps = {
  children: React.ReactNode;
};

type UserInfoProps = {
  email: string;
  id: string;
  username: string;
  isAdmin: boolean;
  firstName: string;
  lastName: string;
  groups: Array<{
    id: number;
    name: string;
  }>;
  tenants: Array<{
    id: string;
    name: string;
  }>;
  sessionTenantId: string;
};

const AppContext = createContext<
  | {
      initialized: boolean;
      userInfo: UserInfoProps | null;
      sessionTenant?: {
        id: string;
        name: string;
      };
      initialize: () => void;
      fetchUserProfile: () => void;
    }
  | undefined
>(undefined);

export const AppProvider = ({ children }: AppProviderProps) => {
  const [initialized, setInitialized] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfoProps | null>(null);

  const fetchUserProfile = async () => {
    const response = await userService.fetchUserProfile();
    setUserInfo(response.data);
  };

  const initialize = async () => {
    setInitialized(false);
    try {
      await authService.refreshToken();
      await fetchUserProfile();
    } catch (error) {
      setUserInfo(null);
    }
    setInitialized(true);
  };

  useEffect(() => {
    initialize();
  }, []);

  const sessionTenant = userInfo?.tenants.find((tenant) => tenant.id === userInfo?.sessionTenantId);

  return (
    <AppContext.Provider
      value={{
        initialized,
        userInfo,
        sessionTenant,
        initialize,
        fetchUserProfile,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

export const useIsAdmin = () => {
  const { userInfo } = useAppContext();
  return userInfo?.isAdmin;
};
