import { HasNumberId } from './globalTypes';
import { TeamTypeProps } from './teamType';
import { TopicGroupProps } from './topicGroupTypes';

export type NotificationCadence = 'WEEKLY' | 'OFF' | 'DAILY' | 'MONTHLY';

export interface TopicProps extends HasNumberId {
  description: string;
  groups: TeamTypeProps[];
  name: string;
  tags: TopicGroupProps[];
  createdAt: string;
  createdBy: CreatedByUserProps;
  userTopicPreference: UserTopicPreferenceProps;
}

export interface UserTopicPreferenceProps {
  isFavourite: boolean;
  negativeSentimentNotifications: NotificationCadence;
  notificationCadence: NotificationCadence;
  highRelevanceNotifications: NotificationCadence;
}

export interface TopicPayload {
  name: string;
  description: string;
  groups: string[];
  tags: number[];
  userTopicPreference: UserTopicPreferenceProps;
}

export interface TopicFormProps {
  name: string;
  description: string;
  groups: TeamTypeProps[];
  tags: TopicGroupProps[];
  userTopicPreference: UserTopicPreferenceProps;
}

export type CreatedByUserProps = {
  id: string;
  firstName: string;
  lastName: string;
};

export interface TopicBulkPatchRequestProps {
  items: Array<{
    id: number;
    isFavourite?: boolean;
    notifications?: NotificationCadence;
    highRel?: NotificationCadence;
    negSentiment?: NotificationCadence;
  }>;
}
export interface NotificationPreferencesProps {
  highRelevanceNotifications: NotificationCadence;
  negativeSentimentNotifications: NotificationCadence;
  notificationCadence: NotificationCadence;
}

export interface TopicRequestQueryProps {
  tagIds?: number[];
  groupIds?: number[];
  onlyFavourited?: boolean;
}
