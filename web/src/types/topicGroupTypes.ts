import { HasNumberId, Selectable } from './globalTypes';
import { TeamTypeProps } from './teamType';
import { NotificationPreferencesProps, TopicProps } from './topicType';

export interface TopicGroupProps extends HasNumberId, Selectable {
  description: string;
  name: string;
  isActive: boolean;
  updatedAt: string;
  createdAt: string;
  isPersonal: boolean;
  userTagPreferences: NotificationPreferencesProps;
  topics?: { id: number; name: string }[];
  groups?: { id: number; name: string }[];
}

export interface TopicGroupFormProps {
  name: string;
  description: string;
  isPersonal: boolean;
  userTagPreferences: NotificationPreferencesProps;
  topics?: TopicProps[];
  groups?: TeamTypeProps[];
}

export interface EditTopicGroupFormProps {
  name: string;
  description: string;
  isPersonal: boolean;
  topics?: { id: number; name: string }[];
  groups?: { id: number; name: string }[];
}
