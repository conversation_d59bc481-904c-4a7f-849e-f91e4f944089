import { HasNumberId, Selectable } from './globalTypes';
import { TopicGroupProps } from './topicGroupTypes';
import { NotificationPreferencesProps, UserTopicPreferenceProps } from './topicType';

export interface TeamTypeProps extends HasNumberId, Selectable {
  description: string | null;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  topics?: { id: number; name: string }[];
  memberCount?: number;
  isUserMember: boolean;
  tags?: { id: number; name: string }[];
  userGroupPreference?: UserTopicPreferenceProps;
}

export interface TeamFormProps {
  name: string;
  description: string;
  tags?: TopicGroupProps[];
  userGroupPreference: NotificationPreferencesProps;
}

export interface TeamPayload {
  name: string;
  description: string;
  tags?: number[];
}

export interface TeamRequestQueryProps {
  topicIds?: string[];
  tagIds?: number[];
  myTeamsOnly?: boolean;
}
