import { NotificationCadence } from './topicType';

export interface HasNumberId {
  id: number;
}

export interface Selectable {
  isSelected?: boolean;
}

export interface PaginationRequestParams {
  page?: number;
  size?: number;
}

export interface PaginationObjectProps {
  page: number;
  size: number;
  total: number;
  totalPages: number;
}

export type ModalTypeId =
  | ''
  | 'newUserModal'
  | 'deleteUserModal'
  | 'editUserModal'
  | 'newTopicModal'
  | 'deleteTopicModal'
  | 'editTopicModal'
  | 'newGroupModal'
  | 'deleteGroupModal'
  | 'editGroupModal'
  | 'assignTagToTopicModal'
  | 'notificationPreferencesModal'
  | 'addTeamModal'
  | 'deleteTeamModal'
  | 'editTeamModal'
  | 'deleteTopicGroupModal'
  | 'newTopicGroupModal'
  | 'editTopicGroupModal'
  | 'teamNotificationPreferencesModal'
  | 'singleObjectNotificationSettingModal';

export type Sentiment = 'POSITIVE' | 'NEGATIVE' | 'MIXED' | 'OTHER' | 'UNKNOWN';
export type DateRangeOptions =
  | 'Last week'
  | 'Last month'
  | 'Last 6 months'
  | 'Last year'
  | 'custom'
  | 'All time'
  | null;
export type Sources = 'REDDIT_POST' | 'YOUTUBE_VIDEO' | 'REDDIT_COMMENT' | 'YOUTUBE_COMMENT';

export interface NotificationPreferencesProps {
  negativeSentimentNotifications: NotificationCadence;
  notificationCadence: NotificationCadence;
  highRelevanceNotifications: NotificationCadence;
}
