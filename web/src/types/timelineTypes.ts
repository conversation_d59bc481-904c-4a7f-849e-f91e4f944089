import { TeamTypeProps } from './teamType';
import { TopicGroupProps } from './topicGroupTypes';

export interface SourceOption {
  id: string;
  name: string;
}

export interface TimelineProps {
  id: number;
  sourceId: string;
  type:
    | 'REDDIT_POST'
    | 'YOUTUBE_VIDEO'
    | 'REDDIT_COMMENT'
    | 'YOUTUBE_COMMENT'
    | 'AVR_FREAKS'
    | 'MICROCHIP_CLASSIC'
    | 'ALL_ABOUT_CIRCUITS';
  topics: {
    id: number;
    name: string;
  }[];
  tags: {
    id: number;
    name: string;
  }[];
  summary: string;
  longSummary: string;
  tipsAndActions: string;
  sentiment: number;
  sentimentReasoning: string;
  relevance: {
    score: number;
    reasoning: string;
  };
  narratives: {
    id: number;
    aspect: string;
    topic: {
      id: number;
      name: string;
    };
    summary: string;
    sentiment: number;
    sentimentReasoning: string;
  }[];
  title: string;
  commentCount: number;
  voteScore: number;
  publishedAt: string;
  externalUrl: string;
  bookmarked: boolean;
  replies?: {
    items: {
      id: string;
      body: string;
      externalUrl: string;
    }[];
    count: number;
  };
  subredditName?: string;
  channelHandle?: string;
}

export type Sentiment = 'POSITIVE' | 'NEGATIVE' | 'MIXED' | 'OTHER' | 'UNKNOWN';
export type DateRangeOptions =
  | 'Last week'
  | 'Last month'
  | 'Last 6 months'
  | 'Last year'
  | 'All time'
  | 'custom'
  | null;
export type Sources =
  | 'REDDIT_POST'
  | 'YOUTUBE_VIDEO'
  | 'REDDIT_COMMENT'
  | 'YOUTUBE_COMMENT'
  | 'AVR_FREAKS'
  | 'MICROCHIP_CLASSIC'
  | 'ALL_ABOUT_CIRCUITS';

export type SortKey =
  | 'RELEVANCE'
  | 'DATE'
  | 'ENGAGEMENT'
  | 'POSITIVE_SENTIMENT'
  | 'NEGATIVE_SENTIMENT';

export interface TimelineFilterParam {
  dateFrom?: string;
  dateTo?: string;
  timeZone?: string;
  groupIds?: number[];
  topicIds?: number[];
  tagIds?: number[];
  sentiments?: Sentiment[];
  bookmarkedOnly?: boolean;
  sources?: Sources[];
  minRelevance?: number;
  maxRelevance?: number;
  sort?: SortKey;
  analysisId?: number;
  narrativeIds?: number[];
  size?: number;
  onlyFavourited?: boolean;
  q?: string;
}

export interface FilterForm {
  dateRange?: DateRangeOptions;
  timeZone?: string;
  topicId?: string | null;
  groups?: TeamTypeProps[];
  tags?: TopicGroupProps[];
  sentiments?: SentimentOptions[];
  bookmarkedOnly?: boolean;
  sources?: string | null;
  multiSelectSources?: SourceOption[];
  relevance?: [number, number];
  sort?: SortKey;
  customDateRange?: string;
  multiSelectTopicIds?: string[];
  multiSelectNarrativeAspects?: string[];
  aspect?: string | null;
  onlyFavourited?: boolean;
  myTeamsOnly?: boolean;
  personalTopicGroupsOnly?: boolean;
  q?: string;
}
export interface SentimentOptions {
  id: Sentiment;
  name: string;
}

export interface NarrativesProps {
  id: number;
  summary: string;
  count: number;
  topicId: number;
  topicName: string;
  aspect: string;
}
