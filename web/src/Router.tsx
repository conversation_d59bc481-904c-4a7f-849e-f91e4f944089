import { useEffect } from 'react';
import { createBrowser<PERSON><PERSON>er, Outlet, RouterProvider, useLocation } from 'react-router-dom';
import { COMPANY_NAME, TITLE_MAP } from '@/consts';
import AnalyticsPage from './pages/AnalyticsPage/Analytics.page';
import ChangeTenantPage from './pages/ChangeTenant.page';
import { ForgotPasswordCodePage } from './pages/ForgotPasswordCode.page';
import { ForgotPasswordPage } from './pages/ForgotPasswordPage/ForgotPassword.page';
import { LoginPage } from './pages/Login.page';
import OnboardingPage from './pages/onboardingPage/Onboarding.page';
import SettingsPage from './pages/SettingsPage/Settings.page';
import TimelineDetailPage from './pages/TimelineDetailPage/TimelineDetail.page';
import TimelineNarrativePage from './pages/TimelineNarrativePage/TimelineNarrative.page';
import TimelinePage from './pages/TimelinePage/Timeline.page';
import TopicManagementPage from './pages/topicManagementPage/TopicManagement.page';
import TruthkeepAnalyticsPage from './pages/TruthkeepAnalyticsPage/TruthkeepAnalytics.page';
import TruthkeepAnalyticsUsersPage from './pages/TruthkeepAnalyticsPage/TruthkeepAnalyticsUsers.page';
import UserManagementPage from './pages/userManagementPage/UserManagement.page';

function PageTitleProvider() {
  const { pathname } = useLocation();

  useEffect(() => {
    // Update document title
    const pageTitle = TITLE_MAP[pathname] ?? '';
    document.title = pageTitle ? `${COMPANY_NAME} | ${pageTitle}` : COMPANY_NAME;
  }, [pathname]);

  return <Outlet />;
}

const router = createBrowserRouter([
  {
    element: <PageTitleProvider />,
    children: [
      // user protected
      { path: 'settings', element: <SettingsPage /> },
      { path: 'timeline', element: <TimelinePage /> },
      { path: 'timeline/narrative/:id', element: <TimelineNarrativePage /> },
      { path: 'timeline/:id', element: <TimelineDetailPage /> },
      { path: 'analytics', element: <AnalyticsPage /> },
      { path: 'change-tenant', element: <ChangeTenantPage /> },

      // admin protected
      { path: 'user-management', element: <UserManagementPage /> },
      { path: 'topic-management', element: <TopicManagementPage /> },
      { path: 'truthkeep-analytics', element: <TruthkeepAnalyticsPage /> },
      { path: 'truthkeep-analytics/users', element: <TruthkeepAnalyticsUsersPage /> },

      // public
      { index: true, element: <TimelinePage /> },
      { path: 'login', element: <LoginPage /> },
      { path: 'forgot-password', element: <ForgotPasswordPage /> },
      { path: 'forgot-password/code', element: <ForgotPasswordCodePage /> },
      { path: 'onboarding/', element: <OnboardingPage /> },

      // catch all
      { path: '*', element: <TimelinePage /> },
    ],
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
