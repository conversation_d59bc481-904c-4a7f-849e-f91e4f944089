// Shared content definitions
const timelineContent = {
  title: 'Timeline',
  description:
    'The Timeline is a feed displaying all analyzed content. Here you can view engagements, bookmark or share important items, and view detailed analysis for each engagement.',
  image: '/tutorial-images/mc-timeline.png',
};

const analyticsContent = {
  title: 'Analytics',
  description:
    'The Analytics page provides comprehensive insights and trends from your data. You can explore narrative trends over time, identify key themes, analyze source distribution, and compare sentiment and narratives across different topics. These tools help you understand patterns and make data-driven decisions.',
  image: '/tutorial-images/mc-analytics.png',
};

const topicManagementContent = {
  title: 'Topic & Team Management',
  description:
    'Topic & Team Management is where you manage the terms and topics you want to monitor. Create and edit topics, assign topic groups, and set up teams to streamline your workflow. This page also allows you to set up notification preferences for important topics.',
  image: '/tutorial-images/mc-topicmanagement.png',
};

const aiChatbotContent = {
  title: 'AI Chatbot',
  description:
    'The AI chatbot in the bottom right corner is your intelligent assistant for getting insights and answers about your data. You can ask questions about trends, share posts for analysis, and get recommendations based on your data. The chatbot provides instant access to AI-powered insights and explanations.',
  image: '/tutorial-images/mc-aichat.png',
};

const userManagementContent = {
  title: 'User Management',
  description:
    'User Management allows administrators to manage user accounts, permissions, and access controls. You can create new users, edit existing user information, assign roles, and manage user teams to ensure proper access control across the platform.',
};

export const helpContent = {
  gettingStarted: {
    title: 'Getting Started with Truthkeep AI',
    description:
      'Welcome to Truthkeep! This platform helps monitor and analyze social media content to understand consumer sentiment and narrative trends. Our AI processes posts from Reddit, Youtube, and Microchip Community Forums + AVRFreaks and provides insights to help you save time and make better decisions.',
    sections: {
      timeline: timelineContent,
      analytics: analyticsContent,
      topicManagement: topicManagementContent,
      aiChatbot: aiChatbotContent,
      needHelp: {
        title: 'Need More Help?',
        description:
          'If you need additional assistance, please send feedback from within the Help modal, contact your system administrator or reach out to the support <NAME_EMAIL>.',
      },
    },
  },
  timeline: {
    ...timelineContent,
    sections: {
      filtering: {
        title: 'Filtering',
        description:
          'Use the filter panel to narrow down content by topics, topic groups, and teams. The filtering system allows you to focus on specific content types or sources, making it easier to find relevant information quickly. You can combine multiple filters to create precise searches across your data.',
        image: '/tutorial-images/mc-timeline-filters.png',
      },
      analysis: {
        title: 'Analysis',
        description:
          "Click on posts to view detailed sentiment scores and analysis. Each post includes comprehensive analysis including sentiment scores, key themes, and narrative insights. The analysis provides context and understanding of the content's impact and meaning.",
      },
      bookmarks: {
        title: 'Bookmarks',
        description:
          'Save important posts for quick access by clicking the bookmark icon. Bookmarked posts are stored separately and can be accessed through the "Bookmarks" tab. This feature helps you maintain a curated collection of the most relevant content for your analysis.',
        image: '/tutorial-images/mc-timeline-bookmarks.png',
      },
      sharing: {
        title: 'Sharing',
        description:
          'Click the share icon to copy a link to the post, or click the repost button to share a post with a specific team. Sharing a post with a team will also notify those users by email.',
        image: '/tutorial-images/mc-timeline-share.png',
      },
    },
  },
  analytics: {
    ...analyticsContent,
    sections: {
      trendingTopics: {
        title: 'Trending Topics & Narratives',
        description:
          'The Trending Topics & Narratives section shows the most discussed topics and narratives based on the analyzed data. Use the date range dropdown to filter the top topics and narratives by a custom date range.',
        image: '/tutorial-images/mc-analytics-trendingtopicsandnarratives.png',
      },
      trendsComparison: {
        title: 'Trends Comparison',
        description:
          'View narrative trends over time to understand how the number of mentions and sentiment are evolving over time. The trends analysis compares changes across different topics for a narrative, helping you identify shifts in public perception.',
        image: '/tutorial-images/mc-analytics-trendscomparison.png',
      },
      narrativeSentimentComparison: {
        title: 'Narrative & Sentiment Comparison',
        description:
          'Compare sentiment across different topics and narratives to understand public perception. Use the filters to compare different sources or filter across a custom date range.',
        image: '/tutorial-images/mc-analytics-narrativesentimentcomparison.png',
      },
      sourceAnalytics: {
        title: 'Source Analytics',
        description:
          'Analyze distribution across different sources to understand where your brand and products are being mentioned positively or negatively.',
        image: '/tutorial-images/mc-analytics-sourceanalytics.png',
      },
    },
  },
  topics: {
    ...topicManagementContent,
    sections: {
      assignTags: {
        title: 'Assign Topic Groups',
        description:
          'Add topic groups to topics for better categorization and filtering. Topic groups provide additional granularity in your content organization, allowing you to create more specific filters and searches. Topic groups can be applied to multiple topics and help create analytics comparisons.',
        image: '/tutorial-images/mc-topics-topic-groups.png',
      },
      setNotifications: {
        title: 'Set Notifications',
        description:
          "Configure notification preferences for topics to stay informed about important developments. You can set up alerts for new content, sentiment changes, or specific keywords within your topics, ensuring you don't miss critical updates.",
        image: '/tutorial-images/mc-topics-notifications.png',
      },
      manageGroups: {
        title: 'Manage Teams',
        description:
          'Organize users into teams to control access and permissions. Teams allow you to manage who can view and interact with specific topics and content. This feature is particularly useful for teams with different roles and responsibilities in content monitoring and analysis.',
        image: '/tutorial-images/mc-topics-teams.png',
      },
    },
  },
  chat: {
    ...aiChatbotContent,
    sections: {
      askQuestions: {
        title: 'Ask Questions',
        description:
          'Get answers about your data and trends by asking natural language questions. The AI can interpret your questions and provide insights about sentiment, themes, patterns, and other aspects of your data. Simply type your question and receive detailed, contextual responses.',
      },
      sharePosts: {
        title: 'Share Posts',
        description:
          'Send posts to the AI for detailed analysis and discussion. By sharing specific posts with the chatbot, you can get personalized insights about that content, including sentiment analysis, key themes, and contextual information about why the post might be significant.',
        image: '/tutorial-images/mc-ai-addposts.png',
      },
      getInsights: {
        title: 'Get Insights',
        description:
          'Receive detailed analysis and explanations about your data. The AI can provide comprehensive insights about trends, patterns, anomalies, and relationships in your content. These insights help you understand the deeper meaning and implications of your data.',
      },
    },
  },
  userManagement: {
    ...userManagementContent,
  },
  definitions: {
    title: 'Definitions',
    description: 'Understanding key terms and concepts used throughout the platform.',
    terms: {
      topic: {
        title: 'Topic',
        description:
          'A topic is a specific product, subject or theme you want to monitor. Topics help organize and categorize content by grouping related engagements together. Topics serve as the primary organizational structure for filtering and analyzing content.',
      },
      tags: {
        title: 'Topic Groups',
        description:
          'Topic groups are labels that provide additional categorization for content. They offer more granular organization than topics and can be applied across multiple topics. Topic Groups help create cross-cutting themes and enable more specific filtering and analytics comparisons.',
      },
      groups: {
        title: 'Teams',
        description:
          'Teams are collections of users who are part of the same team. They help manage who can view and interact with shared content.',
      },
      narrative: {
        title: 'Narrative',
        description:
          'A narrative represents the overall story or perspective being communicated about a topic. Narratives capture the broader context and themes in how people are discussing and perceiving your brand, products, or industry. They help understand the big picture of public sentiment and discourse.',
      },
      sentiment: {
        title: 'Sentiment',
        description:
          'Sentiment measures the emotional tone and attitude expressed in content. It indicates whether mentions are positive, negative, or neutral. Sentiment analysis helps you understand how your brand and products are being perceived, allowing you to identify opportunities and address concerns proactively.',
      },
    },
  },
  faq: {
    title: 'Frequently Asked Questions',
    questions: [
      {
        id: 'how-to-filter',
        question: 'How do I filter content in the Timeline?',
        answer:
          'Use the filter panel on the top of the Timeline page. You can filter by topics, topic groups, teams, date ranges, and sources. Multiple filters can be applied simultaneously to narrow down your results.',
      },
      {
        id: 'bookmark-posts',
        question: 'How do I bookmark important posts?',
        answer:
          'Click the bookmark icon on any post to save it for quick access. Bookmarked posts are stored separately and can be accessed through the "Bookmarks" tab in the Timeline view.',
      },
      {
        id: 'create-topics',
        question: 'How do I create a new topic?',
        answer:
          'Go to the Topic & Team Management page and click "Add Topic." Enter a descriptive name and optional description. You can also assign topic groups and set notification preferences during creation.',
      },
      {
        id: 'ai-chatbot',
        question: 'How do I use the AI chatbot?',
        answer:
          'Click the chat icon in the bottom right corner to open the AI chatbot. You can ask questions about your data, share posts for analysis, or request insights about trends and patterns in your content.',
      },
      {
        id: 'analytics-interpretation',
        question: 'How do I interpret the analytics data?',
        answer:
          'The Analytics page shows trend visualizations and comparisons. Use the date range filters to focus on specific periods, and compare different topics to understand how sentiment and narratives are evolving over time.',
      },
      {
        id: 'notifications',
        question: 'How do I set up notifications?',
        answer:
          'In Topic & Team Management, select a topic and click "Edit." You can configure notification preferences including frequency and conditions for when you want to be alerted about new content or sentiment changes.',
      },
      {
        id: 'sharing-posts',
        question: 'How do I share posts with my team?',
        answer:
          'Click the share icon on any post to copy a link, or use the repost button to share with a specific team. Sharing with a team will notify those users by email and make the post visible in their shared content.',
      },
      {
        id: 'data-sources',
        question: 'What data sources does Truthkeep monitor?',
        answer:
          'Truthkeep currently monitors Reddit, YouTube, Microchip Community Forums, and AVRFreaks. The platform analyzes posts, comments, and video content to provide comprehensive insights across these platforms.',
      },
    ],
  },
};
