import { Dispatch, SetStateAction, useState } from 'react';
import { Checkbox, Flex, Button as MantineButton, Pill, useMantineTheme } from '@mantine/core';
import Button from '@/components/Button/Button';
import GroupPill from '@/components/Pill/GroupPill';
import { Text } from '@/components/Texts/Text/Text';
import { cadenceToLabel } from '@/dictionary/notificationCadenceDictionary';
import { ModalTypeId } from '@/types/globalTypes';
import { TopicProps } from '@/types/topicType';

const NotFavoriteIcon = '/icons/miscIcons/notFavoriteIcon.svg';
const FavoriteIcon = '/icons/miscIcons/favoriteIcon.svg';

type TopicManagementMobileTileProps = {
  localTopics: TopicProps[];
  selectedTopics: TopicProps[];
  setSelectedTopics: Dispatch<SetStateAction<TopicProps[]>>;
  setSelectedTopicToEdit: (topic: TopicProps | null) => void;
  setOpenedModalId: (id: ModalTypeId) => void;
  toggleFavourite: (topicId: number, newValue: boolean) => void;
};

const TopicManagementMobileTile = ({
  localTopics,
  selectedTopics,
  setSelectedTopics,
  setSelectedTopicToEdit,
  setOpenedModalId,
  toggleFavourite,
}: TopicManagementMobileTileProps) => {
  const tableTheme = useMantineTheme().other.table;
  const tagPillTheme = useMantineTheme().other.pill.tagPill;
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null);

  return (
    <div className="block md:hidden mt-[20px]">
      {selectedTopics.length > 0 && (
        <Flex justify="end" mb={20}>
          <MantineButton
            size="compact-sm"
            variant="outline"
            color="#495057"
            onClick={() => setSelectedTopics([])}
          >
            Unselect Rows
          </MantineButton>
        </Flex>
      )}
      {localTopics.map((topic, index) => {
        const isFav = topic?.userTopicPreference?.isFavourite;
        return (
          <Flex direction="column" key={topic.id} className="w-full">
            <Flex
              direction="column"
              p={15}
              gap={28}
              key={topic.id}
              className="border-b border-b-[#E0E0E0]"
              bg={
                selectedTopics.some((selectedTopic) => selectedTopic.id === topic.id)
                  ? `${tableTheme.selectedRowBackgroundColor}`
                  : undefined
              }
            >
              <Flex direction="row" justify="space-between" align="start" w="100%" gap={8}>
                <Flex direction="row" gap={20} align="start">
                  <Checkbox
                    aria-label="Select row"
                    checked={selectedTopics.some((selectedTopic) => selectedTopic.id === topic.id)}
                    onChange={(event) => {
                      setSelectedTopics(
                        event.currentTarget.checked
                          ? [...selectedTopics, topic]
                          : selectedTopics.filter((selectedTopic) => selectedTopic.id !== topic.id)
                      );
                      if (event.currentTarget.checked) {
                        setLastSelectedIndex(index);
                      }
                    }}
                    size="xs"
                    color="violet"
                  />
                  <Button
                    className="min-w-[14px] min-h-[14px] cursor-pointer"
                    onClick={() => toggleFavourite(topic?.id, !isFav)}
                  >
                    <img
                      src={isFav ? FavoriteIcon : NotFavoriteIcon}
                      alt={isFav ? 'favorite' : 'not favorite'}
                    />
                  </Button>
                  <Text size="md" bold>
                    {topic.name}
                  </Text>
                </Flex>
                <Button
                  className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                  onClick={() => setSelectedTopicToEdit(topic)}
                >
                  <img src="/icons/miscIcons/editIcon.svg" alt="edit Icon" />
                </Button>
              </Flex>
              <Flex direction="row" gap={8} wrap="wrap" align="center">
                {topic.tags.length > 0 ? (
                  <>
                    {topic.tags.map((tag) => {
                      return (
                        <Pill
                          key={tag.id}
                          withRemoveButton
                          style={{
                            backgroundColor: tagPillTheme.root.backgroundColor,
                            color: tagPillTheme.root.color,
                          }}
                        >
                          {tag.name}
                        </Pill>
                      );
                    })}
                  </>
                ) : (
                  <Text size="md" className="text-[#868E96] italic">
                    Click the edit icon to add topic groups
                  </Text>
                )}
                <Button
                  className="w-[24px] h-[24px] cursor-pointer"
                  onClick={() => {
                    setSelectedTopicToEdit(topic);
                  }}
                >
                  <img src="/icons/miscIcons/addIcon.svg" alt="Add Icon" />
                </Button>
              </Flex>
              <Flex direction="row" gap={8} wrap="wrap" align="center">
                {topic.groups.length > 0 ? (
                  <>
                    {topic.groups.map((group) => {
                      return <GroupPill key={group.id} group={group} />;
                    })}
                  </>
                ) : (
                  <Text size="md" className="text-[#868E96] italic">
                    Click the edit icon to add teams
                  </Text>
                )}
                <Button
                  className="w-[24px] h-[24px] cursor-pointer"
                  onClick={() => {
                    setSelectedTopicToEdit(topic);
                  }}
                >
                  <img src="/icons/miscIcons/addIcon.svg" alt="Add Icon" />
                </Button>
              </Flex>
              <Flex direction="row" gap={16} align="start">
                <Text size="sm" bold className="text-nowrap">
                  Created by
                </Text>
                <Text size="sm">
                  {topic.createdBy?.firstName} {topic.createdBy?.lastName} On{' '}
                  {new Date(topic?.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </Text>
              </Flex>
              <Flex direction="row" gap={16} align="start">
                <Text size="sm" bold className="text-nowrap">
                  Notifications
                </Text>
                <Text size="sm">
                  {cadenceToLabel[topic?.userTopicPreference?.notificationCadence] === 'Weekly'
                    ? 'Weekly'
                    : cadenceToLabel[topic?.userTopicPreference?.notificationCadence]}
                </Text>
              </Flex>
            </Flex>
            {lastSelectedIndex === index && selectedTopics.length > 0 && (
              <Flex
                direction="column"
                className="py-[12px] px-[24px] bg-black"
                align="start"
                gap={12}
              >
                <Text size="lg" className="text-nowrap text-white">
                  Items selected: {selectedTopics.length}
                </Text>
                <Flex direction="row" gap={12} wrap="wrap">
                  <Button
                    onClick={() => setOpenedModalId('notificationPreferencesModal')}
                    size="compact-sm"
                    className="bg-[#868E96] px-[16px] rounded-[4px] cursor-pointer"
                  >
                    <Text size="md" bold className="text-white">
                      Notification Preferences
                    </Text>
                  </Button>
                  <Button
                    variant="danger"
                    size="compact-sm"
                    onClick={() => setOpenedModalId('deleteTopicModal')}
                  >
                    <Text size="md" bold>
                      Delete
                    </Text>
                  </Button>
                  <Button
                    onClick={() => setOpenedModalId('assignTagToTopicModal')}
                    size="compact-sm"
                    className="bg-[#868E96] px-[16px] rounded-[4px] cursor-pointer min-h-[32px]"
                  >
                    <Text size="md" bold className="text-white">
                      Assign Topic Groups
                    </Text>
                  </Button>
                </Flex>
              </Flex>
            )}
          </Flex>
        );
      })}
    </div>
  );
};

export default TopicManagementMobileTile;
