import { Dispatch, SetStateAction } from 'react';
import {
  Checkbox,
  Flex,
  Button as MantineButton,
  Pill,
  Table,
  useMantineTheme,
} from '@mantine/core';
import { cadenceToLabel } from '@/dictionary/notificationCadenceDictionary';
import { ModalTypeId } from '@/types/globalTypes';
import { TopicProps } from '@/types/topicType';
import Button from '../../Button/Button';
import GroupPill from '../../Pill/GroupPill';

const NotFavoriteIcon = '/icons/miscIcons/notFavoriteIcon.svg';
const FavoriteIcon = '/icons/miscIcons/favoriteIcon.svg';

interface TopicManagementTableProps {
  selectedTopics: TopicProps[];
  localTopics: TopicProps[];
  setSelectedTopics: Dispatch<SetStateAction<TopicProps[]>>;
  setSelectedTopicToEdit: (topic: TopicProps | null) => void;
  toggleFavourite: (topicId: number, newValue: boolean) => void;
  setOpenedModalId: Dispatch<SetStateAction<ModalTypeId>>;
}

const TopicManagementTable = ({
  selectedTopics,
  localTopics,
  setSelectedTopics,
  setSelectedTopicToEdit,
  toggleFavourite,
  setOpenedModalId,
}: TopicManagementTableProps) => {
  const tableTheme = useMantineTheme().other.table;
  const tagPillTheme = useMantineTheme().other.pill.tagPill;

  return !localTopics.length ? (
    <Flex className="w-full min-h-[300px]" align="center" justify="center">
      No topics found
    </Flex>
  ) : (
    <Table.ScrollContainer minWidth={768}>
      <Table
        mt={16}
        className="hidden md:block"
        classNames={{
          td: 'align-top',
        }}
      >
        {selectedTopics.length > 0 && (
          <Flex justify="end" mb={7}>
            <MantineButton
              size="compact-sm"
              variant="outline"
              color="#495057"
              onClick={() => setSelectedTopics([])}
            >
              Unselect Rows
            </MantineButton>
          </Flex>
        )}
        <Table.Thead>
          <Table.Tr>
            <Table.Th w="4%" />
            <Table.Th w="4%" />
            <Table.Th w="10%">Topic</Table.Th>
            <Table.Th w="29%">Topic Groups</Table.Th>
            <Table.Th w="38%">Teams</Table.Th>
            <Table.Th w="11%">Notifications</Table.Th>
            <Table.Th w="4%">Edit</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {localTopics.map((topic) => {
            const isSelected = selectedTopics.some((t) => t.id === topic?.id);
            const isFav = topic?.userTopicPreference?.isFavourite;
            return (
              <Table.Tr
                key={topic?.id}
                bg={isSelected ? tableTheme.selectedRowBackgroundColor : undefined}
              >
                <Table.Td w="4%">
                  <Checkbox
                    aria-label="Select row"
                    checked={isSelected}
                    onChange={(e) =>
                      setSelectedTopics((prev) =>
                        e.currentTarget.checked
                          ? [...prev, topic]
                          : prev.filter((t) => t.id !== topic?.id)
                      )
                    }
                    size="xs"
                    color="violet"
                    id={`topic-${topic?.id}`}
                  />
                </Table.Td>
                <Table.Td w="4%">
                  <Button
                    className="min-w-[14px] min-h-[14px] cursor-pointer"
                    onClick={() => toggleFavourite(topic?.id, !isFav)}
                  >
                    <img
                      src={isFav ? FavoriteIcon : NotFavoriteIcon}
                      alt={isFav ? 'favorite' : 'not favorite'}
                    />
                  </Button>
                </Table.Td>
                <Table.Td w="10%">
                  <label htmlFor={`topic-${topic?.id}`}>{topic?.name}</label>
                </Table.Td>
                <Table.Td w="29%">
                  <Flex direction="row" gap={8} wrap="wrap">
                    {topic?.tags.map((tag) => (
                      <Pill
                        key={tag.id}
                        withRemoveButton
                        style={{
                          backgroundColor: tagPillTheme.root.backgroundColor,
                          color: tagPillTheme.root.color,
                        }}
                      >
                        {tag.name}
                      </Pill>
                    ))}
                    <Button
                      className="w-[24px] h-[24px] cursor-pointer"
                      onClick={() => {
                        setSelectedTopicToEdit(topic);
                        setOpenedModalId('editTopicModal');
                      }}
                    >
                      <img src="/icons/miscIcons/addIcon.svg" alt="Add Icon" />
                    </Button>
                  </Flex>
                </Table.Td>
                <Table.Td w="38%">
                  <Flex direction="row" gap={8} wrap="wrap">
                    {topic?.groups.map((group) => <GroupPill key={group.id} group={group} />)}
                    <Button
                      className="w-[24px] h-[24px] cursor-pointer"
                      onClick={() => {
                        setSelectedTopicToEdit(topic);
                        setOpenedModalId('editTopicModal');
                      }}
                    >
                      <img
                        src="/icons/miscIcons/addIcon.svg"
                        alt="Add Icon"
                        className="w-[24px] h-[24px] cursor-pointer"
                      />
                    </Button>
                  </Flex>
                </Table.Td>
                <Table.Td w="11%">
                  <Button
                    className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                    onClick={() => {
                      setOpenedModalId('singleObjectNotificationSettingModal');
                      setSelectedTopicToEdit(topic);
                    }}
                  >
                    <Flex direction="row" gap={8}>
                      {topic?.userTopicPreference?.notificationCadence
                        ? cadenceToLabel[topic?.userTopicPreference?.notificationCadence]
                        : cadenceToLabel.OFF}
                      <img
                        src="/icons/miscIcons/editIcon.svg"
                        alt="Edit notification preferences"
                      />
                    </Flex>
                  </Button>
                </Table.Td>
                <Table.Td w="4%">
                  <Button
                    className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                    onClick={() => {
                      setOpenedModalId('editTopicModal');
                      setSelectedTopicToEdit(topic);
                    }}
                  >
                    <img src="/icons/miscIcons/editIcon.svg" alt="Edit topic" />
                  </Button>
                </Table.Td>
              </Table.Tr>
            );
          })}
        </Table.Tbody>
      </Table>
    </Table.ScrollContainer>
  );
};

export default TopicManagementTable;
