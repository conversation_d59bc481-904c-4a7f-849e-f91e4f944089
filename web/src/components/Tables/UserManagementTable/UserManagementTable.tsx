import { useState } from 'react';
import { Checkbox, Flex, Switch, Table, useMantineTheme } from '@mantine/core';
import { useAppContext } from '@/context/AppContext';
import { UserProps } from '@/types/userType';
import Button from '../../Button/Button';
import GroupPill from '../../Pill/GroupPill';
import AdminConfirmationModal from './AdminConfirmationModal';

type UserManagementTableProps = {
  users: UserProps[];
  selectedUsers: UserProps[];
  setSelectedUsers: React.Dispatch<React.SetStateAction<UserProps[]>>;
  setSelectedUserToEdit: React.Dispatch<React.SetStateAction<UserProps | null>>;
  toggleUserAdmin: (user: UserProps) => void;
};

const UserManagementTable = ({
  users,
  selectedUsers,
  setSelectedUsers,
  setSelectedUserToEdit,
  toggleUserAdmin,
}: UserManagementTableProps) => {
  const tableTheme = useMantineTheme().other.table;
  const { userInfo } = useAppContext();
  const [selectedUserForAdminChange, setSelectedUserForAdminChange] = useState<UserProps | null>(
    null
  );

  return (
    <Table.ScrollContainer minWidth={768}>
      <AdminConfirmationModal
        user={selectedUserForAdminChange!}
        opened={selectedUserForAdminChange !== null}
        toggleUserAdmin={toggleUserAdmin}
        onClose={() => setSelectedUserForAdminChange(null)}
      />
      <Table mt={16} className="hidden md:table">
        <Table.Thead>
          <Table.Tr>
            <Table.Th w="4%" />
            <Table.Th w="23%">Name</Table.Th>
            <Table.Th w="23%">Email</Table.Th>
            <Table.Th w="29%">Teams</Table.Th>
            <Table.Th w="17%">Admin Permissions</Table.Th>
            <Table.Th w="4%" />
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {users.map((user) => (
            <Table.Tr
              key={user.id}
              bg={
                selectedUsers.some((selectedUser) => selectedUser.id === user.id)
                  ? `${tableTheme.selectedRowBackgroundColor}`
                  : undefined
              }
            >
              <Table.Td w="4%" style={{ verticalAlign: 'top' }}>
                <Checkbox
                  aria-label="Select row"
                  checked={selectedUsers.some((selectedUser) => selectedUser.id === user.id)}
                  onChange={(event) =>
                    setSelectedUsers(
                      event.currentTarget.checked
                        ? [...selectedUsers, user]
                        : selectedUsers.filter((selectedUser) => selectedUser.id !== user.id)
                    )
                  }
                  size="xs"
                  disabled={userInfo?.id === user.id}
                  color="violet"
                />
              </Table.Td>
              <Table.Td
                w="23%"
                style={{ verticalAlign: 'top' }}
              >{`${user.firstName} ${user.lastName}`}</Table.Td>
              <Table.Td w="23%" style={{ verticalAlign: 'top' }}>
                {user.email}
              </Table.Td>
              <Table.Td w="29%" style={{ verticalAlign: 'top' }}>
                <Flex direction="row" gap={8} wrap="wrap">
                  {user.groups.map((group) => {
                    return <GroupPill key={group.id} group={group} />;
                  })}
                  <Button
                    className="w-[24px] h-[24px] cursor-pointer"
                    onClick={() => {
                      setSelectedUserToEdit(user);
                    }}
                  >
                    <img
                      src="/icons/miscIcons/addIcon.svg"
                      alt="Add Icon"
                      className="w-[24px] h-[24px] cursor-pointer"
                    />
                  </Button>
                </Flex>
              </Table.Td>
              <Table.Td w="17%" style={{ verticalAlign: 'top' }}>
                <Button
                  unstyled
                  onClick={() => {
                    setSelectedUserForAdminChange(user);
                  }}
                >
                  <Switch checked={user.isAdmin} role="presentation" color="#12B886" />
                </Button>
              </Table.Td>
              <Table.Td w="4%" style={{ verticalAlign: 'top' }}>
                <Button
                  className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                  onClick={() => setSelectedUserToEdit(user)}
                >
                  <img src="/icons/miscIcons/editIcon.svg" alt="edit Icon" />
                </Button>
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </Table.ScrollContainer>
  );
};

export default UserManagementTable;
