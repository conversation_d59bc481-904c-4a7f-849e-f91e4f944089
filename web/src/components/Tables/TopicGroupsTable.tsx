import { Dispatch, SetStateAction } from 'react';
import {
  Checkbox,
  Flex,
  Button as MantineButton,
  Pill,
  Table,
  useMantineTheme,
} from '@mantine/core';
import { useIsAdmin } from '@/context/AppContext';
import { cadenceToLabel } from '@/dictionary/notificationCadenceDictionary';
import { ModalTypeId } from '@/types/globalTypes';
import { TeamTypeProps } from '@/types/teamType';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import Button from '../Button/Button';
import GroupPill from '../Pill/GroupPill';

const MAX_TOPICS = 10;

interface TopicGroupsTableProps {
  topicGroups: TopicGroupProps[];
  setTopicGroups: Dispatch<SetStateAction<TopicGroupProps[]>>;
  setSelectedTopicGroup: Dispatch<SetStateAction<TopicGroupProps | null>>;
  setOpenedModalId: Dispatch<SetStateAction<ModalTypeId>>;
}

const TopicGroupsTable = ({
  topicGroups,
  setTopicGroups,
  setSelectedTopicGroup,
  setOpenedModalId,
}: TopicGroupsTableProps) => {
  const theme = useMantineTheme();

  const tableTheme = theme.other.table;
  const topicPillTheme = theme.other.pill.topicPill;
  const isAdmin = useIsAdmin();

  return (
    <Flex direction="column" mt={24}>
      {topicGroups.filter((t) => t.isSelected).length > 0 && (
        <Flex justify="end">
          <MantineButton
            size="compact-sm"
            variant="outline"
            color="#495057"
            onClick={() =>
              setTopicGroups((prev) =>
                prev.map((t) => ({
                  ...t,
                  isSelected: false,
                }))
              )
            }
          >
            Unselect Rows
          </MantineButton>
        </Flex>
      )}
      <Table.ScrollContainer minWidth={768}>
        <Table
          classNames={{
            td: 'align-top',
          }}
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th w="4%" />
              <Table.Th w="10%">Topic Group</Table.Th>
              <Table.Th w="31%">Topics</Table.Th>
              <Table.Th w="31%">Teams</Table.Th>
              <Table.Th w="10%">Notifications</Table.Th>
              <Table.Th w="4%">Edit</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {topicGroups.map((topicGroup) => {
              const isSelected = topicGroup?.isSelected;
              return (
                <Table.Tr
                  key={topicGroup?.id}
                  bg={isSelected ? tableTheme.selectedRowBackgroundColor : undefined}
                >
                  <Table.Td w="4%">
                    <Checkbox
                      id={`topic-group-${topicGroup?.id}`}
                      aria-label="Select row"
                      checked={isSelected}
                      onChange={({ currentTarget }) => {
                        const checked = !!currentTarget?.checked;
                        setTopicGroups((prev) =>
                          prev.map((t) =>
                            t.id === topicGroup?.id ? { ...t, isSelected: checked } : t
                          )
                        );
                      }}
                      size="xs"
                      color="violet"
                      disabled={!isAdmin && !topicGroup.isPersonal}
                    />
                  </Table.Td>
                  <Table.Td w="20%">
                    <label htmlFor={`topic-group-${topicGroup?.id}`}>{topicGroup?.name}</label>
                  </Table.Td>
                  <Table.Td w="31%">
                    <Flex
                      direction="row"
                      gap={8}
                      wrap="wrap"
                      title={topicGroup?.topics?.map((topic) => topic.name).join('\n')}
                    >
                      {topicGroup?.topics?.slice(0, MAX_TOPICS).map((topic) => (
                        <Pill
                          key={topic.id}
                          withRemoveButton
                          style={{
                            backgroundColor: topicPillTheme.root.backgroundColor,
                            color: topicPillTheme.root.color,
                          }}
                        >
                          {topic.name}
                        </Pill>
                      ))}
                      <span key="ellipsis" hidden={(topicGroup?.topics?.length ?? 0) <= MAX_TOPICS}>
                        ...
                      </span>
                      <Button
                        className="w-[24px] h-[24px] cursor-pointer"
                        onClick={() => {
                          setSelectedTopicGroup(topicGroup);
                          setOpenedModalId('editTopicGroupModal');
                        }}
                      >
                        <img
                          hidden={!isAdmin && !topicGroup.isPersonal}
                          src="/icons/miscIcons/addIcon.svg"
                          alt="Add Icon"
                        />
                      </Button>
                    </Flex>
                  </Table.Td>
                  <Table.Td w="31%">
                    <Flex direction="row" gap={8} wrap="wrap">
                      {topicGroup?.groups?.map((group) => (
                        <GroupPill key={group.id} group={group as TeamTypeProps} />
                      ))}
                      <Button
                        className="w-[24px] h-[24px] cursor-pointer"
                        onClick={() => {
                          setSelectedTopicGroup(topicGroup);
                          setOpenedModalId('editTopicGroupModal');
                        }}
                        hidden={!isAdmin}
                      >
                        <img
                          src="/icons/miscIcons/addIcon.svg"
                          alt="Add Icon"
                          className="w-[24px] h-[24px] cursor-pointer"
                        />
                      </Button>
                    </Flex>
                  </Table.Td>
                  <Table.Td w="10%">
                    <Button
                      className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                      onClick={() => {
                        setOpenedModalId('singleObjectNotificationSettingModal');
                        setSelectedTopicGroup(topicGroup);
                      }}
                    >
                      <Flex direction="row" gap={8}>
                        {topicGroup?.userTagPreferences?.notificationCadence
                          ? cadenceToLabel[topicGroup?.userTagPreferences?.notificationCadence]
                          : cadenceToLabel.OFF}
                        <img
                          src="/icons/miscIcons/editIcon.svg"
                          alt="Edit notification preferences"
                        />
                      </Flex>
                    </Button>
                  </Table.Td>
                  <Table.Td w="4%">
                    <Button
                      hidden={!isAdmin && !topicGroup.isPersonal}
                      className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                      onClick={() => {
                        setOpenedModalId('editTopicGroupModal');
                        setSelectedTopicGroup(topicGroup);
                      }}
                    >
                      <img src="/icons/miscIcons/editIcon.svg" alt="Edit topic group" />
                    </Button>
                  </Table.Td>
                </Table.Tr>
              );
            })}
          </Table.Tbody>
        </Table>
      </Table.ScrollContainer>
    </Flex>
  );
};

export default TopicGroupsTable;
