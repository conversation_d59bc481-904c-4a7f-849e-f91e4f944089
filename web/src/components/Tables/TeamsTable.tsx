import { Dispatch, SetStateAction } from 'react';
import {
  <PERSON><PERSON>,
  Flex,
  Button as MantineButton,
  Pill,
  Switch,
  Table,
  useMantineTheme,
} from '@mantine/core';
import { useIsAdmin } from '@/context/AppContext';
import { cadenceToLabel } from '@/dictionary/notificationCadenceDictionary';
import { ModalTypeId } from '@/types/globalTypes';
import { TeamTypeProps } from '@/types/teamType';
import Button from '../Button/Button';

interface TeamsTableProps {
  teams: TeamTypeProps[];
  setTeams: Dispatch<SetStateAction<TeamTypeProps[]>>;
  setSelectedTeam: Dispatch<SetStateAction<TeamTypeProps | null>>;
  setOpenedModalId: Dispatch<SetStateAction<ModalTypeId>>;
  toggleTeamMembership: (teamId: number) => void;
}

const TeamsTable = ({
  teams,
  setTeams,
  setSelectedTeam,
  setOpenedModalId,
  toggleTeamMembership,
}: TeamsTableProps) => {
  const theme = useMantineTheme();

  const switchTheme = theme.other.switch;
  const tableTheme = theme.other.table;
  const tagPillTheme = theme.other.pill.tagPill;
  const isAdmin = useIsAdmin();

  return !teams.length ? (
    <Flex className="w-full min-h-[300px]" align="center" justify="center">
      No teams found
    </Flex>
  ) : (
    <Flex direction="column" mt={24}>
      {teams.filter((t) => t.isSelected).length > 0 && (
        <Flex justify="end">
          <MantineButton
            size="compact-sm"
            variant="outline"
            color="#495057"
            onClick={() =>
              setTeams((prev) =>
                prev.map((t) => ({
                  ...t,
                  isSelected: false,
                }))
              )
            }
          >
            Unselect Rows
          </MantineButton>
        </Flex>
      )}
      <Table.ScrollContainer minWidth={768}>
        <Table
          classNames={{
            td: 'align-top',
          }}
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th w="4%" />
              <Table.Th w="10%">Teams</Table.Th>
              <Table.Th w="37%">Topic Groups</Table.Th>
              <Table.Th w="15%">Members</Table.Th>
              <Table.Th w="15%">My Team</Table.Th>
              <Table.Th w="15%">Notifications</Table.Th>
              <Table.Th w="4%" hidden={!isAdmin}>
                Edit
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {teams.map((team) => {
              const isSelected = team?.isSelected;
              return (
                <Table.Tr
                  key={team?.id}
                  bg={isSelected ? tableTheme.selectedRowBackgroundColor : undefined}
                >
                  <Table.Td w="4%">
                    <Checkbox
                      aria-label="Select row"
                      checked={isSelected}
                      onChange={({ currentTarget }) => {
                        const checked = !!currentTarget?.checked;
                        setTeams((prev) =>
                          prev.map((t) => (t.id === team?.id ? { ...t, isSelected: checked } : t))
                        );
                      }}
                      size="xs"
                      color="violet"
                      id={`team-${team?.id}`}
                    />
                  </Table.Td>
                  <Table.Td w="10%">
                    <label htmlFor={`team-${team?.id}`}>{team?.name}</label>
                  </Table.Td>
                  <Table.Td w="37%">
                    <Flex direction="row" gap={8} wrap="wrap">
                      {team?.tags?.map((topicGroup) => (
                        <Pill
                          key={topicGroup.id}
                          withRemoveButton
                          style={{
                            backgroundColor: tagPillTheme.root.backgroundColor,
                            color: tagPillTheme.root.color,
                          }}
                        >
                          {topicGroup.name}
                        </Pill>
                      ))}
                      <Button
                        className="w-[24px] h-[24px] cursor-pointer"
                        hidden={!isAdmin}
                        onClick={() => {
                          setSelectedTeam(team);
                          setOpenedModalId('editTeamModal');
                        }}
                      >
                        <img
                          src="/icons/miscIcons/addIcon.svg"
                          alt="Add Icon"
                          className="w-[24px] h-[24px] cursor-pointer"
                        />
                      </Button>
                    </Flex>
                  </Table.Td>
                  <Table.Td w="15%">{team?.memberCount || 0}</Table.Td>
                  <Table.Td w="15%">
                    <Switch
                      color={switchTheme.backgroundColor}
                      size="sm"
                      checked={team?.isUserMember}
                      onChange={() => toggleTeamMembership(team.id)}
                    />
                  </Table.Td>
                  <Table.Td w="15%">
                    <Button
                      className="cursor-pointer"
                      onClick={() => {
                        setOpenedModalId('singleObjectNotificationSettingModal');
                        setSelectedTeam(team);
                      }}
                    >
                      <Flex direction="row" gap={8}>
                        {team?.userGroupPreference?.notificationCadence
                          ? cadenceToLabel[team?.userGroupPreference?.notificationCadence]
                          : cadenceToLabel.OFF}
                        <img
                          src="/icons/miscIcons/editIcon.svg"
                          alt="Edit notification preferences"
                        />
                      </Flex>
                    </Button>
                  </Table.Td>
                  <Table.Td w="4%">
                    <Button
                      hidden={!isAdmin}
                      className="flex items-center cursor-pointer min-w-[19px] min-h-[19px]"
                      onClick={() => {
                        setSelectedTeam(team);
                        setOpenedModalId('editTeamModal');
                      }}
                    >
                      <img src="/icons/miscIcons/editIcon.svg" alt="Edit team" />
                    </Button>
                  </Table.Td>
                </Table.Tr>
              );
            })}
          </Table.Tbody>
        </Table>
      </Table.ScrollContainer>
    </Flex>
  );
};

export default TeamsTable;
