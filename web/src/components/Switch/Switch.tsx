import {
  Switch as MantineSwitch,
  SwitchProps as MantineSwitchProps,
  useMantineTheme,
} from '@mantine/core';

type SwitchProps = MantineSwitchProps & {
  label?: string;
  color?: string;
  checked?: boolean;
  labelPosition?: 'left' | 'right';
  switchSize?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onChange: (value: boolean) => void;
};

const Switch = ({
  label,
  labelPosition = 'left',
  switchSize = 'sm',
  onChange,
  checked = false,
  disabled = false,
  ...props
}: SwitchProps) => {
  const switchTheme = useMantineTheme().other.switch;
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event.currentTarget.checked);
  };

  return (
    <MantineSwitch
      label={label}
      color={switchTheme.backgroundColor}
      labelPosition={labelPosition}
      onChange={handleChange}
      styles={{
        label: {
          fontWeight: 700,
          textSize: '14px',
          leading: '16px',
        },
      }}
      size={switchSize}
      checked={checked}
      disabled={disabled}
      {...props}
    />
  );
};

export default Switch;
