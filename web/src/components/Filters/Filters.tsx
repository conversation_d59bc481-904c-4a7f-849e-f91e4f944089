import { Flex, Input, MultiSelect, Select, Stack, Switch, useMantineTheme } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import Button from '@/components/Button/Button';
import { LazyRangeSlider } from '@/components/Input/RangeSlider';
import SearchableSelect from '@/components/SearchableSelect/SearchableSelect';
import { useAppContext } from '@/context/AppContext';
import { TeamTypeProps } from '@/types/teamType';
import { SentimentOptions } from '@/types/timelineTypes';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicProps } from '@/types/topicType';
import { Text } from '../Texts/Text/Text';
import { GENERIC_SOURCES, MICROCHIP_SOURCES, sentimentOptions } from './consts';
import { DateRangeFilter } from './DateRangeFilter';

interface FiltersProps {
  topicOptions?: TopicProps[];
  narrativeAspectOptions?: { value: string; label: string }[];
  tagOptions?: TopicGroupProps[];
  groupOptions?: TeamTypeProps[];
  filterForm: UseFormReturnType<Record<string, any>>;
  showResetButton?: boolean;
  placeholderBlockNumber?: number;
  className?: string;
  showFiltersHeading?: boolean;
  allSpaceEqual?: boolean;
}

const Filters = ({
  topicOptions,
  narrativeAspectOptions,
  tagOptions,
  groupOptions,
  filterForm,
  showResetButton = true,
  placeholderBlockNumber = 0,
  className = '',
  showFiltersHeading = false,
  allSpaceEqual = false,
}: FiltersProps) => {
  const theme = useMantineTheme();
  const { sessionTenant } = useAppContext();
  const isMicrochipTenantSession = sessionTenant?.id === 'DEFAULT';

  const sources = isMicrochipTenantSession ? MICROCHIP_SOURCES : GENERIC_SOURCES;

  const sentimentPillTheme = theme.other.pill.sentimentPill;
  const tagPillTheme = theme.other.pill.tagPill;
  const topicPillTheme = theme.other.pill.topicPill;
  const groupPillTheme = theme.other.pill.groupPill;
  const switchTheme = theme.other.switch;

  return (
    <Flex direction="column" gap={9} w="100%">
      {showFiltersHeading && (
        <Text size="lg" bold>
          Filters
        </Text>
      )}
      <Flex direction="column" w="100%" gap={12}>
        <div className={`items-end gap-[12px] w-full grid md:grid-cols-3 ${className ?? ''}`}>
          {'dateRange' in filterForm.values && <DateRangeFilter filterForm={filterForm} />}
          {'multiSelectNarrativeAspects' in filterForm.values && (
            <MultiSelect
              label="Narratives"
              {...filterForm.getInputProps('multiSelectNarrativeAspects')}
              placeholder="Narratives"
              data={narrativeAspectOptions}
              searchable
              clearable
              disabled={!narrativeAspectOptions?.length}
              className={allSpaceEqual ? '' : 'md:col-span-3'}
              comboboxProps={{
                position: 'bottom-start',
                width: '400px',
              }}
              maxValues={3}
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
                pill: {
                  '--pill-height': '26px',
                  backgroundColor: '#228be6',
                  color: 'white',
                  height: '26px',
                },
              }}
            />
          )}
          {'topicId' in filterForm.values && topicOptions && (
            <Select
              label="Topic"
              searchable
              data={[
                {
                  label: 'Favorited Topics',
                  value: 'Favorited Topics',
                },
                ...(topicOptions?.map((topic) => ({
                  label: topic.name,
                  value: `${topic.id}`,
                })) ?? []),
              ]}
              placeholder="Select a topic"
              {...filterForm.getInputProps('topicId')}
              aria-label="timeline-topic-filter"
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
              renderOption={({ option }) => (
                <div
                  style={{ fontWeight: option.label === 'Favorited Topics' ? 'bold' : 'normal' }}
                >
                  {option.label}
                </div>
              )}
              onChange={(value) => {
                filterForm.setFieldValue('topicId', value);
                filterForm.setFieldValue('groups', []);
              }}
            />
          )}
          {'multiSelectTopicIds' in filterForm.values && topicOptions && (
            <MultiSelect
              label="Topic"
              {...filterForm.getInputProps('multiSelectTopicIds')}
              placeholder="Topics"
              data={topicOptions?.map((topic) => ({
                label: topic.name,
                value: `${topic.id}`,
              }))}
              searchable
              clearable
              className={allSpaceEqual ? '' : 'md:col-span-3'}
              comboboxProps={{
                position: 'bottom-start',
                width: '400px',
              }}
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
                pill: {
                  ...topicPillTheme.root,
                  '--pill-height': '26px',
                },
              }}
              onChange={(value) => {
                filterForm.setFieldValue('multiSelectTopicIds', value);
                filterForm.setFieldValue('groups', []);
              }}
            />
          )}
          {'sources' in filterForm.values && (
            <Select
              label="Source"
              type="dropdown"
              data={sources}
              placeholder="Select a source"
              {...filterForm.getInputProps('sources')}
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
          )}
          {'multiSelectSources' in filterForm.values && (
            <MultiSelect
              label="Sources"
              {...filterForm.getInputProps('multiSelectSources')}
              placeholder="Select sources"
              data={sources}
              searchable
              clearable
              comboboxProps={{
                position: 'bottom-start',
                width: '400px',
              }}
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
                pill: {
                  backgroundColor: '#fd7e14',
                  color: 'white',
                  '--pill-height': '26px',
                },
              }}
              maxDropdownHeight={250}
            />
          )}
          {'tags' in filterForm.values && tagOptions && (
            <SearchableSelect<TopicGroupProps>
              label="Topic Groups"
              placeholder="Search topic groups"
              options={tagOptions}
              value={filterForm.values.tags ?? []}
              onChange={(tag) =>
                filterForm.setFieldValue('tags', [tag, ...(filterForm.values.tags ?? [])])
              }
              remove={(id) => {
                filterForm.setFieldValue(
                  'tags',
                  filterForm.values.tags?.filter((t: { id: number }) => t.id !== id)
                );
              }}
              pillStyle={{ ...tagPillTheme.root }}
              ariaLabel="timeline-topic-groups-filter"
              PillsInputStyles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
          )}
          {'sentiments' in filterForm.values && (
            <SearchableSelect<SentimentOptions>
              label="Sentiments"
              placeholder="Select sentiments"
              options={sentimentOptions}
              value={filterForm.values.sentiments ?? []}
              onChange={(sentiment) =>
                filterForm.setFieldValue('sentiments', [
                  sentiment,
                  ...(filterForm.values.sentiments ?? []),
                ])
              }
              remove={(id) => {
                filterForm.setFieldValue(
                  'sentiments',
                  filterForm.values.sentiments?.filter(
                    (sentiment: SentimentOptions) => sentiment.id !== id
                  )
                );
              }}
              pillStyle={{ ...sentimentPillTheme.root }}
              pillIndividualStyle={{
                POSITIVE: sentimentPillTheme.positive,
                NEGATIVE: sentimentPillTheme.negative,
                UNKNOWN: sentimentPillTheme.unknown,
              }}
              PillsInputStyles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
          )}
          {'sort' in filterForm.values && (
            <Select
              label="Sort By"
              type="dropdown"
              data={[
                {
                  label: 'Relevance',
                  value: 'RELEVANCE',
                },
                {
                  label: 'Date',
                  value: 'DATE',
                },
                {
                  label: 'Engagement',
                  value: 'ENGAGEMENT',
                },
                { value: 'POSITIVE_SENTIMENT', label: 'Most positive' },
                { value: 'NEGATIVE_SENTIMENT', label: 'Most negative' },
              ]}
              placeholder="Relevance (default)"
              aria-label="timeline-sort-filter"
              {...filterForm.getInputProps('sort')}
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
          )}
          {'groups' in filterForm.values && groupOptions && (
            <SearchableSelect<TeamTypeProps>
              label="Teams"
              placeholder="Select teams"
              options={groupOptions}
              value={filterForm.values.groups ?? []}
              onChange={(group) => {
                filterForm.setFieldValue('groups', [group, ...(filterForm.values.groups ?? [])]);
                filterForm.setFieldValue('personalTopicGroupsOnly', false);
                if (group) {
                  if ('multiSelectTopicIds' in filterForm.values)
                    filterForm.setFieldValue('multiSelectTopicIds', []);
                  if ('topicNames' in filterForm.values)
                    filterForm.setFieldValue('topicNames', null);
                }
              }}
              remove={(id) => {
                filterForm.setFieldValue(
                  'groups',
                  (filterForm.values.groups ?? []).filter((group: TeamTypeProps) => group.id !== id)
                );
              }}
              pillStyle={{
                ...groupPillTheme.root,
              }}
              pillIcon={
                <img src="/icons/pills/pillUserIcon.svg" alt="User Icon" className="mr-[10.5px]" />
              }
              PillsInputStyles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
          )}
          {'relevance' in filterForm.values && (
            <Stack>
              <Input.Label htmlFor="range-slider">Relevance</Input.Label>
              <LazyRangeSlider
                id="range-slider"
                mx={8}
                min={1}
                max={5}
                step={0.5}
                minRange={0.5}
                marks={Array.from({ length: 6 }).map((_, i) => ({ value: i }))}
                {...filterForm.getInputProps('relevance')}
                color="violet"
              />
            </Stack>
          )}
          {'aspect' in filterForm.values && (
            <Select
              {...filterForm.getInputProps('aspect')}
              label="Narrative"
              placeholder="Narrative"
              data={narrativeAspectOptions ?? []}
              searchable
              styles={{
                input: {
                  backgroundColor: '#F1F3F5',
                  borderColor: '#F1F3F5',
                },
              }}
            />
          )}
          {'onlyFavourited' in filterForm.values && (
            <Switch
              {...filterForm.getInputProps('onlyFavourited')}
              label="Show Favorites Only"
              color={switchTheme.backgroundColor}
              size="sm"
              mb={8}
              labelPosition="left"
              styles={{
                label: { fontWeight: 700 },
              }}
              checked={filterForm.values.onlyFavourited}
            />
          )}
          {'myTeamsOnly' in filterForm.values && (
            <Switch
              {...filterForm.getInputProps('myTeamsOnly')}
              label="Show My Teams Only"
              color={switchTheme.backgroundColor}
              size="sm"
              mb={8}
              labelPosition="left"
              styles={{
                label: { fontWeight: 700 },
              }}
              checked={filterForm.values.myTeamsOnly}
            />
          )}
          {'personalTopicGroupsOnly' in filterForm.values && (
            <Switch
              {...filterForm.getInputProps('personalTopicGroupsOnly')}
              onChange={(event) => {
                filterForm.setFieldValue('personalTopicGroupsOnly', event.currentTarget.checked);
                filterForm.setFieldValue('groups', []);
              }}
              label="Show Personal Topic Groups Only"
              color={switchTheme.backgroundColor}
              size="sm"
              mb={8}
              labelPosition="left"
              styles={{
                label: { fontWeight: 700 },
              }}
              checked={filterForm.values.personalTopicGroupsOnly}
            />
          )}
          {showResetButton && (
            <>
              {placeholderBlockNumber > 0 && (
                <div
                  className="hidden md:block"
                  style={{ gridColumn: `span ${placeholderBlockNumber}` }}
                />
              )}
              <div className="mt-auto">
                <Button variant="secondary" onClick={() => filterForm.reset()}>
                  Clear Filters
                </Button>
              </div>
            </>
          )}
        </div>
      </Flex>
    </Flex>
  );
};

export default Filters;
