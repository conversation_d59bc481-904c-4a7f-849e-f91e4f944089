import { authService } from '../../api/auth/authService';

type ExportToCSVButtonProps = React.ComponentProps<'a'>;

export default function ExportToCSVButton(props: ExportToCSVButtonProps) {
  const children = props.children || '↓ Export to CSV';
  const className = props.className ?? 'cursor-pointer hover:underline text-blue-500';

  const onClick = async (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // Force token refresh to ensure a valid auth cookie before opening the CSV in a new tab
    authService.refreshToken().then(() => {
      window.open(props.href, '_blank');
    });
  };

  return (
    <a {...props} href={props.href} onClick={onClick} className={className}>
      {children}
    </a>
  );
}
