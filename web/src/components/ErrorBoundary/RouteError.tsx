import { Anchor, Container, Stack, Text, Title } from '@mantine/core';
import { Layout } from '../Layout/Layout';

export function RouteError() {
  return (
    <Layout>
      <Container size="sm" style={{ paddingTop: '100px' }}>
        <Stack align="center" gap="lg">
          <Title order={1}>Unexpected Error</Title>
          <Text size="lg" c="dimmed" ta="center">
            We encountered an unexpected error. Please try going back to the home page.
          </Text>
          <Anchor href="/">Go to Home</Anchor>
        </Stack>
      </Container>
    </Layout>
  );
}
