import { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center h-screen">
          <h1 className="text-2xl font-bold">Unexpected Error</h1>
          <p>We encountered an unexpected error. Please try going back to the home page.</p>
          <a className="text-blue-500 underline" href="/">
            Go to Home
          </a>
        </div>
      );
    }

    return this.props.children;
  }
}
