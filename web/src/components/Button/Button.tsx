import { ButtonHTMLAttributes, ReactNode } from 'react';
import {
  Button as MantineButton,
  ButtonProps as MantineButtonProps,
  useMantineTheme,
} from '@mantine/core';

type CustomVariant = 'primary' | 'secondary' | 'danger' | 'secondaryDanger' | 'boarderless';
type CustomSize = 'full' | 'auto' | 'compact-sm';

interface CustomButtonProps
  extends Omit<MantineButtonProps, 'variant' | 'color' | 'unstyled'>,
    Pick<ButtonHTMLAttributes<HTMLButtonElement>, 'type'> {
  children: ReactNode;
  variant?: CustomVariant;
  size?: CustomSize;
  className?: string;
  onClick?: () => void;
  unstyled?: boolean;
  hidden?: boolean;
}

const baseStyle: React.CSSProperties = {
  fontWeight: 700,
  fontSize: '16px',
};

const Button = ({
  children,
  variant = 'primary',
  size = 'full',
  className,
  onClick,
  unstyled = false,
  ...props
}: CustomButtonProps) => {
  const buttonTheme = useMantineTheme().other.button;
  const isCustom = Boolean(className);
  const shouldUnstyle = unstyled || isCustom;

  const combinedStyle: React.CSSProperties | undefined = !shouldUnstyle
    ? {
        ...(size === 'full' ? { width: '100%' } : {}),
        ...baseStyle,
        ...buttonTheme[variant],
      }
    : undefined;

  return (
    <MantineButton
      unstyled={shouldUnstyle}
      className={className}
      style={combinedStyle}
      onClick={onClick}
      {...props}
    >
      {children}
    </MantineButton>
  );
};

export default Button;
