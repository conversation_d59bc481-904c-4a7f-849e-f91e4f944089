import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Combobox,
  Flex,
  Group,
  Text as MantineText,
  Pill,
  PillsInput,
  Tooltip,
  useCombobox,
} from '@mantine/core';
import { Text } from '@/components/Texts/Text/Text';

export interface HasIdName {
  id: string | number;
  name: string;
}

interface SearchableSelectProps<T extends HasIdName> {
  label: string;
  placeholder?: string;
  description?: string;
  options: T[];
  value: T[];
  onChange: (item: T) => void;
  remove: (id: T['id']) => void;
  pillStyle?: React.CSSProperties;
  pillIcon?: React.ReactNode;
  containerStyle?: string;
  ariaLabel?: string;
  pillIndividualStyle?: Record<string, React.CSSProperties>;
  PillsInputStyles?: object;
  nothingFoundMessage?: React.ReactNode;
  externalSearchValue?: string;
  onSearchChange?: (value: string) => void;
}

const SearchableSelect = <T extends HasIdName>({
  label,
  description,
  placeholder = 'Search values',
  options,
  value,
  onChange,
  remove,
  pillStyle,
  pillIcon,
  containerStyle = '',
  ariaLabel = '',
  pillIndividualStyle = {},
  PillsInputStyles = {},
  nothingFoundMessage,
  externalSearchValue,
  onSearchChange,
}: SearchableSelectProps<T>) => {
  const isControlled = externalSearchValue !== undefined && typeof onSearchChange === 'function';

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
    onDropdownOpen: () => combobox.updateSelectedOptionIndex('active'),
  });
  const [search, setSearch] = useState('');
  const hasIndividualPillColors = Object.keys(pillIndividualStyle).length > 0;

  const activeSearch = (isControlled ? externalSearchValue : search).trim().toLowerCase();

  const filtered = useMemo(() => {
    return options.filter(
      (opt) =>
        !value.some((sel) => sel.id === opt.id) && opt.name.toLowerCase().includes(activeSearch)
    );
  }, [options, value, activeSearch]);

  const tooltipLabel = value.length > 2 ? value.map((item) => item.name).join(', ') : '';
  const labelText = value.length > 0 ? `${label} (${value.length})` : label;

  const prevValueRef = useRef<T[]>(value);
  const pillGroupRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    // Scroll to the start of the pills when a new item is added
    const didAddItem = prevValueRef.current?.length < value?.length;
    if (didAddItem)
      pillGroupRef.current?.scrollTo({
        left: 0,
        behavior: 'smooth',
      });
    prevValueRef.current = value;
  }, [value]);

  return (
    <Tooltip label={tooltipLabel} withinPortal={false} disabled={!tooltipLabel} multiline>
      <Flex direction="column" gap={4} className={containerStyle}>
        <Text size="md" bold>
          {labelText}
        </Text>
        <MantineText size="xs" hidden={!description} c="dimmed">
          {description}
        </MantineText>
        <Combobox
          store={combobox}
          onOptionSubmit={(name: string) => {
            const item = options.find((o) => o.name === name);
            if (item) {
              onChange(item);
            }
            if (isControlled) {
              onSearchChange('');
            } else {
              setSearch('');
            }
          }}
        >
          <Combobox.DropdownTarget>
            <PillsInput onClick={() => combobox.openDropdown()} styles={PillsInputStyles}>
              <Pill.Group
                ref={pillGroupRef}
                style={{
                  flexWrap: 'nowrap',
                  overflow: 'scroll',
                  scrollbarWidth: 'none',
                  maskImage: 'linear-gradient(to right, black 90%, rgba(0,0,0,0.3) 100%)',
                }}
              >
                {value.map((item) => (
                  <Pill
                    key={item.id}
                    withRemoveButton
                    onRemove={() => remove(item.id)}
                    style={hasIndividualPillColors ? pillIndividualStyle[item.id] : pillStyle}
                  >
                    <Flex direction="row" align="center" h="100%">
                      {pillIcon && pillIcon}
                      <Text size="sm" className={`${pillIcon ? 'mr-[10.5px]' : ''}`}>
                        {item.name}
                      </Text>
                    </Flex>
                  </Pill>
                ))}
                <Combobox.EventsTarget>
                  <PillsInput.Field
                    value={isControlled ? externalSearchValue : search}
                    placeholder={placeholder}
                    onFocus={() => combobox.openDropdown()}
                    onBlur={() => combobox.closeDropdown()}
                    onChange={(e) => {
                      combobox.updateSelectedOptionIndex();
                      if (isControlled) {
                        onSearchChange(e.currentTarget.value);
                      } else {
                        setSearch(e.currentTarget.value);
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Backspace' && !activeSearch && value.length) {
                        e.preventDefault();
                        remove(value[value.length - 1].id);
                      }
                    }}
                    aria-label={ariaLabel}
                  />
                </Combobox.EventsTarget>
              </Pill.Group>
            </PillsInput>
          </Combobox.DropdownTarget>

          <Combobox.Dropdown mah={250} className="overflow-y-auto">
            <Combobox.Options>
              {filtered.length > 0 ? (
                filtered.map((opt) => (
                  <Combobox.Option key={opt.id} value={opt.name}>
                    <Group gap="sm">
                      <span>{opt.name}</span>
                    </Group>
                  </Combobox.Option>
                ))
              ) : (
                <>
                  <Combobox.Empty>Nothing found…</Combobox.Empty>
                  {nothingFoundMessage}
                </>
              )}
            </Combobox.Options>
          </Combobox.Dropdown>
        </Combobox>
      </Flex>
    </Tooltip>
  );
};

export default SearchableSelect;
