import { useEffect, useState } from 'react';
import { Flex, Select, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { groupService } from '@/api/group/groupService';
import Switch from '@/components/Switch/Switch';
import { NOTIFICATION_PREFERENCE_OPTIONS } from '@/dictionary/notificationCadenceDictionary';
import { notifications } from '@/helpers/notifications';
import { TeamFormProps } from '@/types/teamType';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import Button from '../Button/Button';
import { Input } from '../Input/Input';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import GenericModal from './GenericModal';

type AddGroupModalProps = {
  onClose: () => void;
  onSubmit: () => Promise<void>;
  opened: boolean;
  topicGroupOptions: TopicGroupProps[];
};

const AddGroupModal = ({ onClose, onSubmit, opened, topicGroupOptions }: AddGroupModalProps) => {
  const [loading, setLoading] = useState(false);

  const theme = useMantineTheme();
  const tagPillTheme = theme.other.pill.tagPill;
  const switchTheme = theme.other.switch;

  const form = useForm<TeamFormProps>({
    initialValues: {
      name: '',
      description: '',
      tags: [],
      userGroupPreference: {
        notificationCadence: 'OFF',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Team name is required'),
    },
  });

  const createNewGroup = async (values: TeamFormProps) => {
    setLoading(true);
    try {
      const response = await groupService.createGroups({
        name: values.name,
        description: values.description,
        tags: values.tags?.map((t) => t.id),
      });

      await groupService.patchNotificationPreferences([
        {
          id: response.data.id,
          negativeSentimentNotifications: values.userGroupPreference.negativeSentimentNotifications,
          notificationCadence: values.userGroupPreference.notificationCadence,
          highRelevanceNotifications: values.userGroupPreference.highRelevanceNotifications,
        },
      ]);

      notifications.show({
        title: 'Team Successfully Created!',
        message: 'The team has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
      await onSubmit();
      onClose();
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error creating the team',
        },
        error
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    form.reset();
  }, [opened]);

  return (
    <GenericModal title="Add Team" onClose={onClose} loading={loading} opened={opened}>
      <form onSubmit={form.onSubmit(createNewGroup)} noValidate>
        <Flex direction="column" gap={24}>
          <Input
            label="Team Name"
            description="The name of the team as it will appear throughout the platform."
            placeholder="Enter the team name"
            type="text"
            {...form.getInputProps('name')}
            required
          />
          <Input
            label="Description"
            description="The purpose of the team"
            placeholder="Team description"
            type="textarea"
            {...form.getInputProps('description')}
          />
          <SearchableSelect<TopicGroupProps>
            label="Topic Groups"
            description="Select the topic groups that will be tracked by the team"
            placeholder="Search topic groups"
            options={topicGroupOptions}
            value={form.values.tags ?? []}
            onChange={(tag) => form.setFieldValue('tags', [tag, ...(form.values.tags ?? [])])}
            remove={(id) => {
              form.setFieldValue(
                'tags',
                form?.values?.tags?.filter((t) => t.id !== id)
              );
            }}
            pillStyle={{ ...tagPillTheme.root }}
          />
          <Select
            label="Email Notifications"
            description="Receive email notifications when this team's topics are mentioned"
            type="dropdown"
            data={NOTIFICATION_PREFERENCE_OPTIONS}
            {...form.getInputProps('userTagPreferences.notificationCadence')}
            comboboxProps={{ keepMounted: true }}
          />

          <Switch
            label="Negative Sentiment Notifications"
            description="Receive email notifications when this team's topics are mentioned with negative sentiment"
            checked={form.values.userGroupPreference.negativeSentimentNotifications !== 'OFF'}
            onChange={(value) => {
              form.setFieldValue(
                'userGroupPreference.negativeSentimentNotifications',
                value ? 'MONTHLY' : 'OFF'
              );
            }}
          />
          <div hidden={form.values.userGroupPreference.negativeSentimentNotifications === 'OFF'}>
            <Select
              aria-label="Negative Sentiment Email Notifications"
              data={NOTIFICATION_PREFERENCE_OPTIONS}
              {...form.getInputProps('userGroupPreference.negativeSentimentNotifications')}
              comboboxProps={{ keepMounted: true }}
            />
          </div>

          <Switch
            label="High Relevance Notifications"
            description="Receive email notifications for highly relevant posts that mention this team's topics"
            checked={
              // Same comments as above about types
              form.values.userGroupPreference.highRelevanceNotifications !== 'OFF'
            }
            onChange={(value) => {
              form.setFieldValue(
                'userGroupPreference.highRelevanceNotifications',
                value ? 'MONTHLY' : 'OFF'
              );
            }}
            color={switchTheme.backgroundColor}
          />
          <div hidden={form.values.userGroupPreference.highRelevanceNotifications === 'OFF'}>
            <Select
              label="High Relevance Email Notifications"
              data={NOTIFICATION_PREFERENCE_OPTIONS}
              {...form.getInputProps('userGroupPreference.highRelevanceNotifications')}
              comboboxProps={{ keepMounted: true }}
            />
          </div>
          <Flex gap={8}>
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Save
            </Button>
          </Flex>
        </Flex>
      </form>
    </GenericModal>
  );
};

export default AddGroupModal;
