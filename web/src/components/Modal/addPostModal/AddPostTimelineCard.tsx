import { Dispatch, SetStateAction } from 'react';
import { Checkbox } from '@mantine/core';
import { TimelineProps } from '@/types/timelineTypes';
import SimpleTimelineCard from '../../SimpleTimelineCard/SimpleTimelineCard';

interface TimelineCardProps {
  timeline: TimelineProps;
  isSelected: boolean;
  setSelectedTimelineItems: Dispatch<SetStateAction<TimelineProps[]>>;
}

const AddPostTimelineCard = ({
  timeline,
  isSelected,
  setSelectedTimelineItems,
}: TimelineCardProps) => {
  return (
    <SimpleTimelineCard timeline={timeline}>
      <Checkbox
        size="lg"
        color="violet"
        checked={isSelected}
        onChange={(e) => {
          const isChecked = e.currentTarget.checked;
          setSelectedTimelineItems((prev) =>
            isChecked ? [...prev, timeline] : prev.filter((t) => t.id !== timeline?.id)
          );
        }}
      />
    </SimpleTimelineCard>
  );
};

export default AddPostTimelineCard;
