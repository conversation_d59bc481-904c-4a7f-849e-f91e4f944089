import { useEffect, useState } from 'react';
import { Flex, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { groupService } from '@/api/group/groupService';
import { userService } from '@/api/user/userService';
import Pill from '@/components/Pill/Pill';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';
import { notifications } from '@/helpers/notifications';
import { TeamTypeProps } from '@/types/teamType';
import { UserFormProps, UserProps } from '@/types/userType';
import Button from '../Button/Button';
import { Input } from '../Input/Input';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import Switch from '../Switch/Switch';
import GenericModal from './GenericModal';

type EditUserModalProps = {
  user: UserProps | null;
  opened: boolean;
  deleteSelectedUser: () => Promise<void>;
  onClose: () => void;
  onSubmit: () => void;
};

const EditUserModal = ({
  user,
  opened,
  onClose,
  onSubmit,
  deleteSelectedUser,
}: EditUserModalProps) => {
  const { userInfo, fetchUserProfile } = useAppContext();
  const [groupOptions, setGroupOptions] = useState<TeamTypeProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState<'' | 'DELETE' | 'ADMIN_EDIT'>(
    ''
  );
  const initialAdminStatus = user?.isAdmin;
  const groupPillTheme = useMantineTheme().other.pill.groupPill;

  const form = useForm<UserFormProps>({
    initialValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      groups: user?.groups || [],
      isAdmin: user?.isAdmin || false,
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
      firstName: (value) => (value ? null : 'First name is required'),
      lastName: (value) => (value ? null : 'Last name is required'),
    },
  });

  const initialize = async () => {
    if (opened && user) {
      setLoading(true);
      try {
        const response = await groupService.fetchGroups();
        setGroupOptions(response.data.items as TeamTypeProps[]);

        form.setValues({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          groups: user.groups,
          isAdmin: user.isAdmin,
        });
      } catch (error) {
        notifications.show(
          {
            title: 'Error fetching data',
            message: 'There was an error fetching teams',
          },
          error
        );
      } finally {
        setLoading(false);
      }
    }
  };

  const editUser = async (values: UserFormProps) => {
    if (initialAdminStatus !== values.isAdmin && showConfirmationModal !== 'ADMIN_EDIT') {
      setShowConfirmationModal('ADMIN_EDIT');
      return;
    }
    setLoading(true);
    try {
      await userService.editUser(user!.id, {
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        isAdmin: values.isAdmin,
        groups: values.groups.map((group) => group.id),
      });
      notifications.show({
        title: 'User Successfully Updated!',
        message: `User with email ${values.email} has been updated.`,
        autoClose: 3000,
        color: 'teal',
      });
      onSubmit();
      // If the user editted themselves, we may have stale data in the AppContext
      if (user?.id === userInfo?.id) await fetchUserProfile();
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error updating the user',
        },
        error
      );
    } finally {
      setLoading(false);
      setShowConfirmationModal('');
    }
  };

  useEffect(() => {
    initialize();
  }, [user, opened]);

  return (
    <GenericModal
      title={`${showConfirmationModal === 'DELETE' ? 'Delete' : 'Edit'} User`}
      onClose={() => {
        setShowConfirmationModal('');
        onClose();
      }}
      loading={loading}
      opened={opened}
    >
      {showConfirmationModal === 'DELETE' || showConfirmationModal === 'ADMIN_EDIT' ? (
        <form
          className="w-full h-full flex flex-col items-center justify-center px-[20%] text-center gap-[24px]"
          onSubmit={form.onSubmit(editUser)}
          noValidate
        >
          <Text size="xl">
            {showConfirmationModal === 'DELETE'
              ? 'Are you sure you want to delete this user?'
              : "Are you sure you want to change this user's admin status?"}
          </Text>
          <Pill variant="secondary" withRemoveButton={false}>
            <Text size="lg" bold>
              {user?.firstName} {user?.lastName}
            </Text>
          </Pill>
          <div className="flex flex-row gap-[8px] w-full mt-[24px]">
            <Button
              onClick={() => {
                setShowConfirmationModal('');
              }}
              variant="secondary"
            >
              Cancel
            </Button>
            {showConfirmationModal === 'DELETE' ? (
              <Button
                onClick={async () => {
                  setLoading(true);
                  await deleteSelectedUser();
                  setShowConfirmationModal('');
                  setLoading(false);
                }}
                variant="danger"
              >
                Delete
              </Button>
            ) : (
              <Button variant="primary" type="submit">
                Change
              </Button>
            )}
          </div>
        </form>
      ) : (
        <form onSubmit={form.onSubmit(editUser)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="First Name"
              placeholder="Enter the user's first name"
              type="text"
              {...form.getInputProps('firstName')}
              required
            />
            <Input
              label="Last Name"
              placeholder="Enter the user's last name"
              type="text"
              {...form.getInputProps('lastName')}
              required
            />
            <Input
              label="Email"
              placeholder="Enter the user's email"
              type="email"
              {...form.getInputProps('email')}
              required
            />
            <SearchableSelect<TeamTypeProps>
              label="Teams"
              placeholder="Search teams"
              options={groupOptions}
              value={form.values.groups}
              onChange={(group) => form.setFieldValue('groups', [group, ...form.values.groups])}
              remove={(id) => {
                form.setFieldValue(
                  'groups',
                  form.values.groups.filter((g) => g.id !== id)
                );
              }}
              pillStyle={{
                ...groupPillTheme.root,
              }}
              pillIcon={
                <img src="/icons/pills/pillUserIcon.svg" alt="User Icon" className="mr-[10.5px]" />
              }
            />
            <Flex justify="start">
              <Switch
                label="Admin Permissions"
                {...form.getInputProps('isAdmin', { type: 'checkbox' })}
                disabled={userInfo?.id === user?.id}
              />
            </Flex>
            <Flex direction="column" gap={8}>
              <Flex gap={8}>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowConfirmationModal('');
                    onClose();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="secondaryDanger"
                  type="button"
                  onClick={async () => {
                    setShowConfirmationModal('DELETE');
                  }}
                  disabled={userInfo?.id === user?.id}
                >
                  Delete User
                </Button>
              </Flex>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      )}
    </GenericModal>
  );
};

export default EditUserModal;
