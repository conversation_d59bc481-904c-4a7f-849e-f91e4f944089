import { useEffect, useState } from 'react';
import { Flex, Select } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { topicService } from '@/api/topics/topicService';
import { Text } from '@/components/Texts/Text/Text';
import { cadenceToLabel, labelToCadence } from '@/dictionary/notificationCadenceDictionary';
import { NotificationCadence, NotificationPreferencesProps, TopicProps } from '@/types/topicType';
import Button from '../Button/Button';
import Pill from '../Pill/Pill';
import Switch from '../Switch/Switch';
import GenericModal from './GenericModal';

type NotificationPreferencesModalProps = {
  opened: boolean;
  onClose: () => void;
  onSubmit: () => void;
  topics: TopicProps[];
  deleteSelectedTopics: (localSelectedTopics: TopicProps[]) => Promise<void>;
};

const NotificationPreferencesModal = ({
  opened,
  onClose,
  onSubmit,
  topics,
  deleteSelectedTopics,
}: NotificationPreferencesModalProps) => {
  const [loading, setLoading] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState<'' | 'DELETE'>('');
  const [localSelectedTopics, setLocalSelectedTopics] = useState<TopicProps[]>(topics);

  const isSingleTopic = topics.length === 1;

  const form = useForm<NotificationPreferencesProps>({
    initialValues: {
      highRelevanceNotifications: cadenceToLabel.OFF as NotificationCadence,
      negativeSentimentNotifications: cadenceToLabel.OFF as NotificationCadence,
      notificationCadence: cadenceToLabel.WEEKLY as NotificationCadence,
    },
    validate: {
      notificationCadence: (value) => (value ? null : 'Please select a email notification option'),
    },
  });

  const editTopicsPreference = async (values: NotificationPreferencesProps) => {
    setLoading(true);
    try {
      const items = localSelectedTopics.map((topic) => ({
        id: topic.id,
        negativeSentimentNotifications: labelToCadence[values.negativeSentimentNotifications],
        notificationCadence: labelToCadence[values.notificationCadence],
        highRelevanceNotifications: labelToCadence[values.highRelevanceNotifications],
      }));

      await topicService.patchNotificationPreferences(items);

      notifications.show({
        title: 'Notification preferences updated',
        message: '',
        color: 'teal',
        autoClose: 3000,
      });

      onSubmit();
    } catch (error) {
      notifications.show({
        title: 'There was an error updating topic notification preferences',
        message: undefined,
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
      setShowConfirmationModal('');
    }
  };

  useEffect(() => {
    if (opened) {
      setLocalSelectedTopics(topics);
      form.reset();
    }
  }, [opened, topics]);

  return (
    <GenericModal
      title={showConfirmationModal === 'DELETE' ? 'Delete Topic' : 'Notification Preferences'}
      onClose={() => {
        setShowConfirmationModal('');
        onClose();
      }}
      loading={loading}
      opened={opened}
      onExitTransitionEnd={async () => {
        setShowConfirmationModal('');
        form.reset();
      }}
    >
      {showConfirmationModal === 'DELETE' ? (
        <form
          className="w-full h-full flex flex-col items-center justify-center px-[20%] text-center gap-[24px]"
          noValidate
        >
          <Text size="xl">{`Are you sure you want to delete ${isSingleTopic ? 'this' : 'these'} topic${isSingleTopic ? '' : 's'}?`}</Text>
          <Flex justify="center" align="center" gap={8} wrap="wrap">
            {localSelectedTopics.map((topic) => {
              return (
                <Pill key={topic.id} variant="secondary" withRemoveButton={false}>
                  <Text size="md" bold>
                    {topic.name}
                  </Text>
                </Pill>
              );
            })}
          </Flex>
          <Text size="md">This action cannot be undone.</Text>
          <div className="flex flex-row gap-[8px] w-full">
            <Button
              onClick={() => {
                setShowConfirmationModal('');
              }}
              variant="secondary"
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setLoading(true);
                await deleteSelectedTopics(localSelectedTopics);
                setLoading(false);
                onClose();
              }}
              variant="danger"
            >
              Delete
            </Button>
          </div>
        </form>
      ) : (
        <form onSubmit={form.onSubmit(editTopicsPreference)} noValidate>
          <Flex direction="column" gap={24}>
            <Text size="sm" bold>
              Topics Selected
            </Text>
            <Flex gap={8} direction="row" wrap="wrap">
              {localSelectedTopics?.map((topic) => {
                return (
                  <Pill key={topic.id} variant="secondary" withRemoveButton={false}>
                    <Flex direction="row" align="center" justify="center">
                      <Text size="md" bold>
                        {topic.name}
                      </Text>
                      {localSelectedTopics.length > 1 && (
                        <Button
                          className="flex items-center cursor-pointer min-w-[14px] min-h-[14px] ml-[15px]"
                          onClick={() =>
                            setLocalSelectedTopics((prev) => prev.filter((t) => t.id !== topic.id))
                          }
                        >
                          <img src="/icons/miscIcons/removeIcon.svg" alt="edit Icon" />
                        </Button>
                      )}
                    </Flex>
                  </Pill>
                );
              })}
            </Flex>
            <Select
              label="Email Notifications"
              description="Receive email notifications when this topic is mentioned"
              type="dropdown"
              data={['Off', 'Daily', 'Weekly', 'Monthly']}
              {...form.getInputProps('notificationCadence')}
              comboboxProps={{ keepMounted: true }}
            />
            <Switch
              label="Negative Sentiment Notifications"
              description="Receive email notifications when this topic is mentioned with negative sentiment"
              checked={form.values.negativeSentimentNotifications !== cadenceToLabel.OFF}
              onChange={(value) => {
                form.setFieldValue(
                  'negativeSentimentNotifications',
                  (value ? cadenceToLabel.WEEKLY : cadenceToLabel.OFF) as NotificationCadence
                );
              }}
            />
            <div hidden={form.values.negativeSentimentNotifications === cadenceToLabel.OFF}>
              <Select
                aria-label="Negative Sentiment Email Notifications"
                data={['Off', 'Daily', 'Weekly', 'Monthly']}
                {...form.getInputProps('negativeSentimentNotifications')}
                comboboxProps={{ keepMounted: true }}
              />
            </div>
            <Switch
              label="High Relevance Notifications"
              description="Receive email notifications for highly relevant posts that mention this topic"
              checked={form.values.highRelevanceNotifications !== cadenceToLabel.OFF}
              onChange={(value) => {
                form.setFieldValue(
                  'highRelevanceNotifications',
                  (value ? cadenceToLabel.WEEKLY : cadenceToLabel.OFF) as NotificationCadence
                );
              }}
            />
            <div hidden={form.values.highRelevanceNotifications === cadenceToLabel.OFF}>
              <Select
                aria-label="High Relevance Email Notifications"
                data={['Off', 'Daily', 'Weekly', 'Monthly']}
                {...form.getInputProps('highRelevanceNotifications')}
                comboboxProps={{ keepMounted: true }}
              />
            </div>
            <Flex direction="column" gap={8}>
              <Flex gap={8}>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowConfirmationModal('');
                    onClose();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="secondaryDanger"
                  type="button"
                  onClick={async () => {
                    setShowConfirmationModal('DELETE');
                  }}
                >
                  Delete Topic
                </Button>
              </Flex>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      )}
    </GenericModal>
  );
};

export default NotificationPreferencesModal;
