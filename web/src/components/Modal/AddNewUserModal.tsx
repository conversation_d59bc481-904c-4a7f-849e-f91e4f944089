import { useEffect, useState } from 'react';
import { Flex, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { groupService } from '@/api/group/groupService';
import { userService } from '@/api/user/userService';
import { notifications } from '@/helpers/notifications';
import { TeamTypeProps } from '@/types/teamType';
import { UserFormProps } from '@/types/userType';
import Button from '../Button/Button';
import { Input } from '../Input/Input';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import Switch from '../Switch/Switch';
import GenericModal from './GenericModal';

type AddNewUserModalProps = {
  onSubmit: () => void;
  onClose: () => void;
};

const AddNewUserModal = ({ onClose, onSubmit }: AddNewUserModalProps) => {
  const [groupOptions, setGroupOptions] = useState<TeamTypeProps[]>([]);
  const [loading, setLoading] = useState(false);
  const groupPillTheme = useMantineTheme().other.pill.groupPill;

  const form = useForm<UserFormProps>({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      groups: [],
      isAdmin: false,
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
      firstName: (value) => (value ? null : 'First name is required'),
      lastName: (value) => (value ? null : 'Last name is required'),
    },
  });

  const initialize = async () => {
    setLoading(true);
    try {
      const response = await groupService.fetchGroups();
      setGroupOptions(response.data.items as TeamTypeProps[]);
    } catch (error) {
      notifications.show({
        title: 'Error fetching data',
        message: 'There was an error fetching teams',
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const createNewUser = async (values: UserFormProps) => {
    setLoading(true);
    try {
      await userService.createUser({
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        isAdmin: values.isAdmin,
        groups: values.groups.map((group) => group.id),
      });
      notifications.show({
        title: 'User Successfully Created!',
        message: `A new user has been created with the email ${values.email}`,
        autoClose: 3000,
        color: 'teal',
      });
      onSubmit();
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error creating the user',
        },
        error
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    initialize();
  }, []);

  return (
    <GenericModal title="Add New User" onClose={onClose} loading={loading}>
      <form onSubmit={form.onSubmit(createNewUser)} noValidate>
        <Flex direction="column" gap={24}>
          <Input
            label="First Name"
            placeholder="Enter the user's first name"
            type="text"
            {...form.getInputProps('firstName')}
            required
          />
          <Input
            label="Last Name"
            placeholder="Enter the user's last name"
            type="text"
            {...form.getInputProps('lastName')}
            required
          />
          <Input
            label="Email"
            placeholder="Enter the user's email"
            type="email"
            {...form.getInputProps('email')}
            required
          />
          <SearchableSelect<TeamTypeProps>
            label="Teams"
            placeholder="Search team"
            options={groupOptions}
            value={form.values.groups}
            onChange={(group) => form.setFieldValue('groups', [group, ...form.values.groups])}
            remove={(id) => {
              form.setFieldValue(
                'groups',
                form.values.groups.filter((g) => g.id !== id)
              );
            }}
            pillStyle={{
              ...groupPillTheme.root,
            }}
            pillIcon={
              <img src="/icons/pills/pillUserIcon.svg" alt="User Icon" className="mr-[10.5px]" />
            }
          />
          <Flex justify="start">
            <Switch
              label="Admin Permissions"
              {...form.getInputProps('isAdmin', { type: 'checkbox' })}
            />
          </Flex>
          <Flex gap={8}>
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Save
            </Button>
          </Flex>
        </Flex>
      </form>
    </GenericModal>
  );
};

export default AddNewUserModal;
