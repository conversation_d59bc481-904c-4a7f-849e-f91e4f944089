import { useEffect, useState } from 'react';
import { Flex, Select, TextInput } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { groupService } from '@/api/group/groupService';
import { tagService } from '@/api/tags/tagService';
import { topicService } from '@/api/topics/topicService';
import { cadenceToLabel, labelToCadence } from '@/dictionary/notificationCadenceDictionary';
import { HasNumberId } from '@/types/globalTypes';
import { TeamTypeProps } from '@/types/teamType';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import { NotificationCadence, NotificationPreferencesProps, TopicProps } from '@/types/topicType';
import Button from '../Button/Button';
import Switch from '../Switch/Switch';
import GenericModal from './GenericModal';

const typeToName = {
  TOPIC: 'Topic',
  TOPIC_GROUP: 'Topic Group',
  TEAM: 'Team',
};

type SingleObjectNotificationSettingModalProps = {
  opened: boolean;
  onClose: () => void;
  onSubmit: () => void;
  object: TopicProps | TopicGroupProps | TeamTypeProps | null;
  type: 'TOPIC' | 'TOPIC_GROUP' | 'TEAM';
};

type SingleObjectNotificationPreferencesFormProps = NotificationPreferencesProps & HasNumberId;

const SingleObjectNotificationSettingModal = ({
  opened,
  onClose,
  onSubmit,
  object,
  type,
}: SingleObjectNotificationSettingModalProps) => {
  const [loading, setLoading] = useState(false);

  const form = useForm<SingleObjectNotificationPreferencesFormProps>({
    initialValues: {
      id: object?.id || 0,
      highRelevanceNotifications: cadenceToLabel.OFF as NotificationCadence,
      negativeSentimentNotifications: cadenceToLabel.OFF as NotificationCadence,
      notificationCadence: cadenceToLabel.OFF as NotificationCadence,
    },
    validate: {
      notificationCadence: (value) => (value ? null : 'Please select a email notification option'),
    },
  });

  const editObjectPreferences = async (values: SingleObjectNotificationPreferencesFormProps) => {
    setLoading(true);
    const normalizedPayload = {
      id: object!.id,
      highRelevanceNotifications: labelToCadence[values.highRelevanceNotifications],
      negativeSentimentNotifications: labelToCadence[values.negativeSentimentNotifications],
      notificationCadence: labelToCadence[values.notificationCadence],
    };

    try {
      if (type === 'TOPIC') {
        await topicService.patchNotificationPreferences([normalizedPayload]);
      } else if (type === 'TEAM') {
        await groupService.patchNotificationPreferences([normalizedPayload]);
      } else {
        await tagService.patchNotificationPreferences([normalizedPayload]);
      }

      notifications.show({
        title: 'Notification preferences updated',
        message: '',
        color: 'teal',
        autoClose: 3000,
      });

      onSubmit();
    } catch (error) {
      notifications.show({
        title: 'There was an error updating topic notification preferences',
        message: undefined,
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!object) return;

    const defaultPreference = {
      notificationCadence: 'OFF' as NotificationCadence,
      highRelevanceNotifications: 'OFF' as NotificationCadence,
      negativeSentimentNotifications: 'OFF' as NotificationCadence,
    };

    let notificationPreference = defaultPreference;

    if (type === 'TOPIC' && 'userTopicPreference' in object) {
      notificationPreference = { ...defaultPreference, ...object.userTopicPreference };
    } else if (type === 'TEAM' && 'userGroupPreference' in object) {
      notificationPreference = { ...defaultPreference, ...object.userGroupPreference };
    } else if (type === 'TOPIC_GROUP' && 'userTagPreferences' in object) {
      notificationPreference = { ...defaultPreference, ...object.userTagPreferences };
    }

    const { notificationCadence, highRelevanceNotifications, negativeSentimentNotifications } =
      notificationPreference;

    form.setValues({
      id: object?.id || 0,
      highRelevanceNotifications:
        (cadenceToLabel?.[highRelevanceNotifications] as NotificationCadence) ?? cadenceToLabel.OFF,
      negativeSentimentNotifications:
        (cadenceToLabel?.[negativeSentimentNotifications] as NotificationCadence) ??
        cadenceToLabel.OFF,
      notificationCadence:
        (cadenceToLabel?.[notificationCadence] as NotificationCadence) ?? cadenceToLabel.WEEKLY,
    });
  }, [object]);

  return (
    <GenericModal
      title="Notification Settings"
      onClose={() => {
        onClose();
      }}
      loading={loading}
      opened={opened}
      onExitTransitionEnd={async () => {
        form.reset();
      }}
    >
      <form onSubmit={form.onSubmit(editObjectPreferences)} noValidate>
        <Flex direction="column" gap={24}>
          <TextInput label={`${typeToName[type]} Name`} type="text" value={object?.name} disabled />
          <Select
            label="Email Notifications"
            description="Receive email notifications when this topic is mentioned"
            type="dropdown"
            data={['Off', 'Daily', 'Weekly', 'Monthly']}
            {...form.getInputProps('notificationCadence')}
            comboboxProps={{ keepMounted: true }}
          />
          <Switch
            label="Negative Sentiment Notifications"
            description="Receive email notifications when this topic is mentioned with negative sentiment"
            checked={form.values.negativeSentimentNotifications !== cadenceToLabel.OFF}
            onChange={(value) => {
              form.setFieldValue(
                'negativeSentimentNotifications',
                (value ? cadenceToLabel.WEEKLY : cadenceToLabel.OFF) as NotificationCadence
              );
            }}
          />
          <div hidden={form.values.negativeSentimentNotifications === cadenceToLabel.OFF}>
            <Select
              aria-label="Negative Sentiment Email Notifications"
              data={['Off', 'Daily', 'Weekly', 'Monthly']}
              {...form.getInputProps('negativeSentimentNotifications')}
              comboboxProps={{ keepMounted: true }}
            />
          </div>
          <Switch
            label="High Relevance Notifications"
            description="Receive email notifications for highly relevant posts that mention this topic"
            checked={form.values.highRelevanceNotifications !== cadenceToLabel.OFF}
            onChange={(value) => {
              form.setFieldValue(
                'highRelevanceNotifications',
                (value ? cadenceToLabel.WEEKLY : cadenceToLabel.OFF) as NotificationCadence
              );
            }}
          />
          <div hidden={form.values.highRelevanceNotifications === cadenceToLabel.OFF}>
            <Select
              aria-label="High Relevance Email Notifications"
              data={['Off', 'Daily', 'Weekly', 'Monthly']}
              {...form.getInputProps('highRelevanceNotifications')}
              comboboxProps={{ keepMounted: true }}
            />
          </div>
          <Flex direction="column" gap={8}>
            <Flex gap={8}>
              <Button
                variant="secondary"
                onClick={() => {
                  onClose();
                }}
              >
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </Flex>
      </form>
    </GenericModal>
  );
};

export default SingleObjectNotificationSettingModal;
