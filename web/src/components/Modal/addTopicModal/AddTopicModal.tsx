import { Flex, Select, useMantineTheme } from '@mantine/core';
import Switch from '@/components/Switch/Switch';
import { Text } from '@/components/Texts/Text/Text';
import { NOTIFICATION_PREFERENCE_OPTIONS } from '@/dictionary/notificationCadenceDictionary';
import { TeamTypeProps } from '@/types/teamType';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import Button from '../../Button/Button';
import { Input } from '../../Input/Input';
import SearchableSelect from '../../SearchableSelect/SearchableSelect';
import GenericModal from '../GenericModal';
import { useAddTopicModal } from './useAddTopicModal';

type AddTopicModalProps = {
  onClose: () => void;
  onSubmit: () => void;

  opened: boolean;
};

const AddTopicModal = ({ onClose, onSubmit, opened }: AddTopicModalProps) => {
  const {
    loading,
    groupOptions,
    tagOptions,
    showAddModalType,
    addNewTopicModalForm,
    addNewGroupModalForm,
    addNewTopicGroupForm,
    searchValues,

    createNewTopic,
    createNewGroup,
    createNewTopicGroup,
    setShowAddModalType,
    handleSearchValues,
  } = useAddTopicModal({
    onSubmit,
  });

  const theme = useMantineTheme();
  const tagPillTheme = theme.other.pill.tagPill;
  const groupPillTheme = theme.other.pill.groupPill;
  const switchTheme = theme.other.switch;

  return (
    <GenericModal
      title={`Add ${showAddModalType === 'group' ? 'Team' : showAddModalType === 'topic-group' ? 'Topic Group' : 'Topic'}`}
      onClose={onClose}
      loading={loading}
      opened={opened}
    >
      {showAddModalType === 'group' ? (
        <form onSubmit={addNewGroupModalForm.onSubmit(createNewGroup)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Team Name"
              description="The name of the team as it will appear throughout the platform"
              placeholder="Enter the team name"
              type="text"
              {...addNewGroupModalForm.getInputProps('name')}
              required
            />
            <Input
              label="Description"
              description="The purpose of the team"
              placeholder="Team description"
              type="textarea"
              {...addNewGroupModalForm.getInputProps('description')}
            />
            <SearchableSelect<TopicGroupProps>
              label="Topic Groups"
              description="Select the topic groups that will be tracked by the team"
              placeholder="Search topic groups"
              options={tagOptions}
              value={addNewGroupModalForm.values.tags ?? []}
              onChange={(tag) =>
                addNewGroupModalForm.setFieldValue('tags', [
                  tag,
                  ...(addNewGroupModalForm.values.tags ?? []),
                ])
              }
              remove={(id) => {
                addNewGroupModalForm.setFieldValue(
                  'tags',
                  addNewGroupModalForm?.values?.tags?.filter((t) => t.id !== id)
                );
              }}
              pillStyle={{ ...tagPillTheme.root }}
            />
            <Flex gap={8}>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowAddModalType(null);
                  addNewGroupModalForm.reset();
                }}
              >
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      ) : showAddModalType === 'topic-group' ? (
        <form onSubmit={addNewTopicGroupForm.onSubmit(createNewTopicGroup)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Tag Name"
              description="The name of the tag as it will appear throughout the platform"
              placeholder="Enter the tag name"
              type="text"
              {...addNewTopicGroupForm.getInputProps('name')}
              required
            />
            <Input
              label="Description"
              description="The purpose of the tag"
              placeholder="Enter the tag description"
              type="textarea"
              {...addNewTopicGroupForm.getInputProps('description')}
            />
            <Switch
              {...addNewTopicGroupForm.getInputProps('isPersonal')}
              label="Is this a personal topic group?"
              description={
                addNewTopicGroupForm.values.isPersonal
                  ? 'Not visible to other users'
                  : 'Visible to other users'
              }
              checked={addNewTopicGroupForm.values.isPersonal}
            />
            <Select
              label="Email Notifications"
              description="Receive email notifications when this topic group is mentioned"
              type="dropdown"
              data={NOTIFICATION_PREFERENCE_OPTIONS}
              {...addNewTopicGroupForm.getInputProps('userTagPreferences.notificationCadence')}
              comboboxProps={{ keepMounted: true }}
            />

            <Switch
              label="Negative Sentiment Notifications"
              description="Receive email notifications when this topic group is mentioned with negative sentiment"
              checked={
                addNewTopicGroupForm.values.userTagPreferences.negativeSentimentNotifications !==
                'OFF'
              }
              onChange={(value) => {
                addNewTopicGroupForm.setFieldValue(
                  'userTagPreferences.negativeSentimentNotifications',
                  value ? 'MONTHLY' : 'OFF'
                );
              }}
            />
            <div
              hidden={
                addNewTopicGroupForm.values.userTagPreferences.negativeSentimentNotifications ===
                'OFF'
              }
            >
              <Select
                aria-label="Negative Sentiment Email Notifications"
                data={NOTIFICATION_PREFERENCE_OPTIONS}
                {...addNewTopicGroupForm.getInputProps(
                  'userTagPreferences.negativeSentimentNotifications'
                )}
                comboboxProps={{ keepMounted: true }}
              />
            </div>

            <Switch
              label="High Relevance Notifications"
              description="Receive email notifications for highly relevant posts that mention this topic group"
              checked={
                addNewTopicGroupForm.values.userTagPreferences.highRelevanceNotifications !== 'OFF'
              }
              onChange={(value) => {
                addNewTopicGroupForm.setFieldValue(
                  'userTagPreferences.highRelevanceNotifications',
                  value ? 'MONTHLY' : 'OFF'
                );
              }}
            />
            <div
              hidden={
                addNewTopicGroupForm.values.userTagPreferences.highRelevanceNotifications === 'OFF'
              }
            >
              <Select
                aria-label="High Relevance Email Notifications"
                data={NOTIFICATION_PREFERENCE_OPTIONS}
                {...addNewTopicGroupForm.getInputProps(
                  'userTagPreferences.highRelevanceNotifications'
                )}
                comboboxProps={{ keepMounted: true }}
              />
            </div>
            <Flex gap={8}>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowAddModalType(null);
                  addNewTopicGroupForm.reset();
                }}
              >
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      ) : (
        <form onSubmit={addNewTopicModalForm.onSubmit(createNewTopic)} noValidate>
          <Flex direction="column" gap={18}>
            <Input
              label="Topic Name"
              description="The name of the topic as it will appear throughout the platform"
              placeholder="Enter the topic name"
              type="text"
              {...addNewTopicModalForm.getInputProps('name')}
              required
            />
            <Input
              type="textarea"
              label="Description"
              description="Add descriptive information to provide the model with more context about this topic"
              placeholder="Enter the topic description"
              {...addNewTopicModalForm.getInputProps('description')}
            />
            <SearchableSelect<TopicGroupProps>
              label="Topic Groups"
              description="Select the topic groups that can be used to track the topic"
              placeholder="Search topic groups"
              options={tagOptions}
              value={addNewTopicModalForm.values.tags}
              onChange={(tag) => {
                addNewTopicModalForm.setFieldValue('tags', [
                  tag,
                  ...addNewTopicModalForm.values.tags,
                ]);
              }}
              remove={(id) => {
                addNewTopicModalForm.setFieldValue(
                  'tags',
                  addNewTopicModalForm.values.tags.filter((t) => t.id !== id)
                );
              }}
              pillStyle={{
                ...tagPillTheme.root,
              }}
              nothingFoundMessage={
                <Flex direction="column" gap={8} align="center" w="100%">
                  <Button
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => {
                      setShowAddModalType('topic-group');
                      addNewTopicGroupForm.setValues({
                        name: searchValues.topicGroupSearch,
                        description: '',
                      });
                      handleSearchValues('topicGroupSearch', '');
                    }}
                  >
                    <Text
                      size="md"
                      className="text-[#7950F2]"
                    >{`Create new topic group "${searchValues.topicGroupSearch}"`}</Text>
                  </Button>
                </Flex>
              }
              onSearchChange={(value) => {
                handleSearchValues('topicGroupSearch', value);
              }}
              externalSearchValue={searchValues.topicGroupSearch}
            />
            <SearchableSelect<TeamTypeProps>
              label="Teams"
              description="Select the teams that will have access to the topic"
              placeholder="Search teams"
              options={groupOptions}
              value={addNewTopicModalForm.values.groups}
              onChange={(group) =>
                addNewTopicModalForm.setFieldValue('groups', [
                  group,
                  ...addNewTopicModalForm.values.groups,
                ])
              }
              remove={(id) => {
                addNewTopicModalForm.setFieldValue(
                  'groups',
                  addNewTopicModalForm.values.groups.filter((g) => g.id !== id)
                );
              }}
              pillStyle={{
                ...groupPillTheme.root,
              }}
              pillIcon={
                <img src="/icons/pills/pillUserIcon.svg" alt="User Icon" className="mr-[10.5px]" />
              }
              nothingFoundMessage={
                <Flex direction="column" gap={8} align="center" w="100%">
                  <Button
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => {
                      setShowAddModalType('group');
                      addNewGroupModalForm.setValues({
                        name: searchValues.groupSearch,
                        description: '',
                      });
                      handleSearchValues('groupSearch', '');
                    }}
                  >
                    <Text
                      size="md"
                      className="text-[#7950F2]"
                    >{`Create new team "${searchValues.groupSearch}"`}</Text>
                  </Button>
                </Flex>
              }
              onSearchChange={(value) => {
                handleSearchValues('groupSearch', value);
              }}
              externalSearchValue={searchValues.groupSearch}
            />
            <Select
              label="Email Notifications"
              description="Receive email notification when this topic is mentioned"
              type="dropdown"
              data={NOTIFICATION_PREFERENCE_OPTIONS}
              {...addNewTopicModalForm.getInputProps('userTopicPreference.notificationCadence')}
              comboboxProps={{ keepMounted: true }}
            />

            <Switch
              label="Negative Sentiment Notifications"
              description="Receive email notification when this topic is mentioned with negative sentiment"
              checked={
                addNewTopicModalForm.values.userTopicPreference.negativeSentimentNotifications !==
                'OFF'
              }
              onChange={(value) => {
                addNewTopicModalForm.setFieldValue(
                  'userTopicPreference.negativeSentimentNotifications',
                  value ? 'MONTHLY' : 'OFF'
                );
              }}
            />
            <div
              hidden={
                addNewTopicModalForm.values.userTopicPreference.negativeSentimentNotifications ===
                'OFF'
              }
            >
              <Select
                aria-label="Negative Sentiment Email Notifications"
                data={NOTIFICATION_PREFERENCE_OPTIONS}
                {...addNewTopicModalForm.getInputProps(
                  'userTopicPreference.negativeSentimentNotifications'
                )}
                comboboxProps={{ keepMounted: true }}
              />
            </div>

            <Switch
              label="High Relevance Notifications"
              description="Receive email notifications for highly relevant posts that mention this topic"
              checked={
                addNewTopicModalForm.values.userTopicPreference.highRelevanceNotifications !== 'OFF'
              }
              onChange={(value) => {
                addNewTopicModalForm.setFieldValue(
                  'userTopicPreference.highRelevanceNotifications',
                  value ? 'MONTHLY' : 'OFF'
                );
              }}
              color={switchTheme.backgroundColor}
            />
            <div
              hidden={
                addNewTopicModalForm.values.userTopicPreference.highRelevanceNotifications === 'OFF'
              }
            >
              <Select
                aria-label="High Relevance Email Notifications"
                data={NOTIFICATION_PREFERENCE_OPTIONS}
                {...addNewTopicModalForm.getInputProps(
                  'userTopicPreference.highRelevanceNotifications'
                )}
                comboboxProps={{ keepMounted: true }}
              />
            </div>
            <Flex gap={8}>
              <Button variant="secondary" onClick={onClose}>
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      )}
    </GenericModal>
  );
};

export default AddTopicModal;
