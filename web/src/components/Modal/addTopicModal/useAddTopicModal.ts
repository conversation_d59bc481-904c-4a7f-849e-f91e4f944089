import { useEffect, useState } from 'react';
import { useForm } from '@mantine/form';
import { groupService } from '@/api/group/groupService';
import { tagService } from '@/api/tags/tagService';
import { topicService } from '@/api/topics/topicService';
import { notifications } from '@/helpers/notifications';
import { TeamFormProps, TeamTypeProps } from '@/types/teamType';
import { TopicGroupFormProps, TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicFormProps } from '@/types/topicType';

export const useAddTopicModal = ({
  onSubmit = () => {},
}: {
  onSubmit?: () => void;
} = {}) => {
  const [groupOptions, setGroupOptions] = useState<TeamTypeProps[]>([]);
  const [tagOptions, setTagOptions] = useState<TopicGroupProps[]>([]);
  const [loading, setLoading] = useState(false);

  const [searchValues, setSearchValues] = useState<{
    topicGroupSearch: string;
    groupSearch: string;
  }>({ topicGroupSearch: '', groupSearch: '' });

  const [showAddModalType, setShowAddModalType] = useState<'topic-group' | 'group' | null>(null);

  const addNewTopicModalForm = useForm<TopicFormProps>({
    initialValues: {
      name: '',
      description: '',
      groups: [],
      tags: [],
      userTopicPreference: {
        isFavourite: false,
        notificationCadence: 'MONTHLY',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Topic name is required'),
    },
  });

  const addNewGroupModalForm = useForm<TeamFormProps>({
    initialValues: {
      name: '',
      description: '',
      tags: [],
      userGroupPreference: {
        notificationCadence: 'MONTHLY',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Team name is required'),
    },
  });

  const addNewTopicGroupForm = useForm<TopicGroupFormProps>({
    initialValues: {
      name: '',
      description: '',
      isPersonal: false,
      userTagPreferences: {
        notificationCadence: 'MONTHLY',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Topic Group name is required'),
    },
  });

  const initialize = async () => {
    setLoading(true);
    Promise.all([groupService.fetchGroups(), tagService.fetchTags()])
      .then(([groupResponse, tagResponse]) => {
        setGroupOptions(groupResponse.data.items as TeamTypeProps[]);
        setTagOptions(tagResponse.data.items as TopicGroupProps[]);
      })
      .catch(() => {
        notifications.show({
          title: 'Error fetching data',
          message: 'There was an error fetching teams or topic groups',
          autoClose: 3000,
          color: 'red',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const createNewTopic = async (values: TopicFormProps) => {
    setLoading(true);
    try {
      await topicService.createTopic({
        name: values.name,
        description: values.description,
        groups: values.groups.map((group) => group.name),
        tags: values.tags.map((tag) => tag.id),
        userTopicPreference: {
          isFavourite: false,
          negativeSentimentNotifications: values.userTopicPreference.negativeSentimentNotifications,
          notificationCadence: values.userTopicPreference.notificationCadence,
          highRelevanceNotifications: values.userTopicPreference.highRelevanceNotifications,
        },
      });
      setShowAddModalType(null);
      addNewTopicModalForm.reset();
      notifications.show({
        title: 'Topic Successfully Created!',
        message: 'The topic has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
      onSubmit();
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error creating the topic',
        },
        error
      );
    } finally {
      setLoading(false);
    }
  };

  const createNewTopicGroup = async (values: TopicGroupFormProps) => {
    setLoading(true);
    try {
      const response = await tagService.createTags({
        name: values.name,
        description: values.description,
        isPersonal: values.isPersonal,
      });

      await tagService.patchNotificationPreferences([
        {
          id: response.data.id,
          negativeSentimentNotifications: values.userTagPreferences.negativeSentimentNotifications,
          notificationCadence: values.userTagPreferences.notificationCadence,
          highRelevanceNotifications: values.userTagPreferences.highRelevanceNotifications,
        },
      ]);

      setTagOptions((prev) => [...prev, response?.data]);
      addNewTopicModalForm.setFieldValue('tags', [
        ...addNewTopicModalForm.values.tags,
        response?.data,
      ]);

      notifications.show({
        title: 'Topic Group Successfully Created!',
        message: 'The topic Group has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
      setShowAddModalType(null);
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error creating the topic group',
        },
        error
      );
    } finally {
      setLoading(false);
    }
  };

  const createNewGroup = async (values: TeamFormProps) => {
    setLoading(true);
    try {
      const response = await groupService.createGroups({
        name: values.name,
        description: values.description,
        tags: values.tags?.map((t) => t.id),
      });

      await groupService.patchNotificationPreferences([
        {
          id: response.data.id,
          negativeSentimentNotifications: values.userGroupPreference.negativeSentimentNotifications,
          notificationCadence: values.userGroupPreference.notificationCadence,
          highRelevanceNotifications: values.userGroupPreference.highRelevanceNotifications,
        },
      ]);

      setGroupOptions((prev) => [...prev, response?.data]);

      addNewTopicModalForm.setFieldValue('groups', [
        ...addNewTopicModalForm.values.groups,
        response?.data,
      ]);

      notifications.show({
        title: 'Team Successfully Created!',
        message: 'The team has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
      setShowAddModalType(null);
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error creating the team',
        },
        error
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSearchValues = (key: 'topicGroupSearch' | 'groupSearch', value: string) => {
    setSearchValues((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  useEffect(() => {
    initialize();
  }, []);

  return {
    loading,
    groupOptions,
    tagOptions,
    showAddModalType,
    addNewTopicModalForm,
    addNewGroupModalForm,
    addNewTopicGroupForm,
    searchValues,

    createNewTopic,
    createNewGroup,
    createNewTopicGroup,
    setShowAddModalType,
    handleSearchValues,
  };
};
