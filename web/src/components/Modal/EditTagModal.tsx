import { useEffect, useState } from 'react';
import { Flex, Pill, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { tagService } from '@/api/tags/tagService';
import { Text } from '@/components/Texts/Text/Text';
import { notifications } from '@/helpers/notifications';
import { TeamTypeProps } from '@/types/teamType';
import { EditTopicGroupFormProps, TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicProps } from '@/types/topicType';
import Button from '../Button/Button';
import { Input } from '../Input/Input';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import Switch from '../Switch/Switch';
import GenericModal from './GenericModal';

type EditTagModalProps = {
  tag: TopicGroupProps | null;
  onClose: () => void;
  onSubmit: () => Promise<void>;
  deleteSelectedTag: () => Promise<void>;
  opened: boolean;
  topicOptions: TopicProps[];
  groupOptions: TeamTypeProps[];
};

const EditTagModal = ({
  tag,
  onClose,
  onSubmit,
  deleteSelectedTag,
  opened,
  topicOptions,
  groupOptions,
}: EditTagModalProps) => {
  const [loading, setLoading] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState<'' | 'DELETE' | 'ADMIN_EDIT'>(
    ''
  );
  const tagPillTheme = useMantineTheme().other.pill.tagPill;
  const topicPillTheme = useMantineTheme().other.pill.topicPill;
  const groupPillTheme = useMantineTheme().other.pill.groupPill;
  const switchTheme = useMantineTheme().other.switch;

  const form = useForm<EditTopicGroupFormProps>({
    initialValues: {
      name: '',
      description: '',
      isPersonal: false,
      topics: [],
      groups: [],
    },
    validate: {
      name: (value) => (value ? null : 'Topic Group name is required'),
    },
  });

  const editTag = async (values: EditTopicGroupFormProps) => {
    setLoading(true);
    try {
      await tagService.editTags(String(tag?.id), {
        description: values.description,
        topics: values.topics?.map((t) => t.id),
        groups: values.groups?.map((g) => g.id),
      });
      notifications.show({
        title: 'Topic Group Successfully Updated!',
        message: 'The topic group has been updated successfully.',
        autoClose: 3000,
        color: 'teal',
      });
      await onSubmit();
      onClose();
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error updating the topic group',
        },
        error
      );
    } finally {
      setLoading(false);
    }
  };

  const initialize = async () => {
    if (opened && tag) {
      form.setValues({
        name: tag.name,
        description: tag.description as string,
        isPersonal: tag.isPersonal,
        topics: tag.topics ?? [],
        groups: tag.groups ?? [],
      });
    }
  };

  useEffect(() => {
    initialize();
  }, [tag]);

  return (
    <GenericModal
      title={`${showConfirmationModal === 'DELETE' ? 'Delete' : 'Edit'} Topic Group`}
      onClose={() => {
        onClose();
        setShowConfirmationModal('');
      }}
      loading={loading}
      opened={opened}
    >
      {showConfirmationModal === 'DELETE' ? (
        <form
          className="w-full h-full flex flex-col items-center justify-center px-[20%] text-center gap-[24px]"
          noValidate
        >
          <Text size="xl">Are you sure you want to delete this topic group?</Text>
          <Pill
            variant="secondary"
            withRemoveButton={false}
            style={{
              backgroundColor: tagPillTheme.root.backgroundColor,
              color: tagPillTheme.root.color,
              height: 'auto',
              overflow: 'visible',
            }}
            className="min-w-fit py-[4px] px-[16px]"
          >
            <Text size="xl">{tag?.name}</Text>
          </Pill>
          <Text size="sm">This action cannot be undone</Text>
          <div className="flex flex-row gap-[8px] w-full">
            <Button
              onClick={() => {
                setShowConfirmationModal('');
              }}
              variant="secondary"
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setLoading(true);
                await deleteSelectedTag();
                setShowConfirmationModal('');
                setLoading(false);
              }}
              variant="danger"
            >
              Delete
            </Button>
          </div>
        </form>
      ) : (
        <form onSubmit={form.onSubmit(editTag)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Topic Group Name"
              description="The name of the topic group as it will appear throughout the platform"
              placeholder="Enter the topic group name"
              type="text"
              {...form.getInputProps('name')}
              required
              disabled
            />
            <Input
              label="Description"
              description="The purpose of the topic group"
              placeholder="Topic Group description"
              type="textarea"
              {...form.getInputProps('description')}
            />
            <Switch
              readOnly
              label="Is this a personal topic group?"
              description={
                form.values.isPersonal ? 'Not visible to other users' : 'Visible to other users'
              }
              color={switchTheme.backgroundColor}
              switchSize="sm"
              labelPosition="left"
              checked={tag?.isPersonal}
              disabled
              onChange={() => {}}
            />
            <SearchableSelect<{ id: number; name: string }>
              label="Topics"
              description="Select the topics that will be tracked by the topic group"
              placeholder="Search topics"
              options={topicOptions}
              value={form.values.topics ?? []}
              onChange={(topic) =>
                form.setFieldValue('topics', [topic, ...(form.values.topics ?? [])])
              }
              remove={(id) => {
                form.setFieldValue(
                  'topics',
                  form?.values?.topics?.filter((t) => t.id !== id)
                );
              }}
              pillStyle={{ ...topicPillTheme.root }}
            />
            <div hidden={form.values.isPersonal}>
              <SearchableSelect<{ id: number; name: string }>
                label="Teams"
                description="Select the teams that will track the topic group"
                placeholder="Search teams"
                options={groupOptions}
                value={form.values.groups ?? []}
                onChange={(group) =>
                  form.setFieldValue('groups', [group, ...(form.values.groups ?? [])])
                }
                remove={(id) => {
                  form.setFieldValue(
                    'groups',
                    form?.values?.groups?.filter((g) => g.id !== id)
                  );
                }}
                pillStyle={{ ...groupPillTheme.root }}
              />
            </div>
            <Flex direction="column" gap={8}>
              <Flex gap={8}>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowConfirmationModal('');
                    onClose();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="secondaryDanger"
                  type="button"
                  onClick={async () => {
                    setShowConfirmationModal('DELETE');
                  }}
                >
                  Delete Topic Group
                </Button>
              </Flex>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      )}
    </GenericModal>
  );
};

export default EditTagModal;
