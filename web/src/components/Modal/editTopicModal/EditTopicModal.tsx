import { Flex, useMantineTheme } from '@mantine/core';
import Pill from '@/components/Pill/Pill';
import { Text } from '@/components/Texts/Text/Text';
import { TeamTypeProps } from '@/types/teamType';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicProps } from '@/types/topicType';
import Button from '../../Button/Button';
import { Input } from '../../Input/Input';
import SearchableSelect from '../../SearchableSelect/SearchableSelect';
import GenericModal from '../GenericModal';
import { useEditTopicModal } from './useEditTopicModal';

type EditTopicModalProps = {
  topic: TopicProps | null;
  opened: boolean;

  deleteSelectedTopic: () => Promise<void>;
  onClose: () => void;
  onSubmit: () => void;
};

const EditTopicModal = ({
  topic,
  opened,
  onClose,
  onSubmit,
  deleteSelectedTopic,
}: EditTopicModalProps) => {
  const {
    loading,
    groupOptions,
    tagOptions,
    showConfirmationModal,
    editTopicModalForm,
    searchValues,
    addNewTopicGroupForm,
    addNewGroupModalForm,
    showEditModalType,

    setShowConfirmationModal,
    setLoading,
    editTopic,
    handleSearchValues,
    createNewTopicGroup,
    createNewGroup,
    setShowEditModalType,
  } = useEditTopicModal({
    topic,
    opened,
    onSubmit,
  });

  const tagPillTheme = useMantineTheme().other.pill.tagPill;
  const groupPillTheme = useMantineTheme().other.pill.groupPill;

  return (
    <GenericModal
      title={
        showEditModalType
          ? `Add ${showEditModalType === 'group' ? 'Team' : 'Topic Group'}`
          : `${showConfirmationModal === 'DELETE' ? 'Delete' : 'Edit'} Topic`
      }
      onClose={() => {
        onClose();
      }}
      onExitTransitionEnd={() => {
        setShowEditModalType(null);
        setShowConfirmationModal('');
      }}
      loading={loading}
      opened={opened}
    >
      {showEditModalType === 'group' ? (
        <form onSubmit={addNewGroupModalForm.onSubmit(createNewGroup)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Team Name"
              placeholder="Enter the team name"
              type="text"
              {...addNewGroupModalForm.getInputProps('name')}
              required
            />
            <Input
              label="Description"
              placeholder="Team description"
              type="textarea"
              {...addNewGroupModalForm.getInputProps('description')}
            />
            <SearchableSelect<TopicGroupProps>
              label="Topic Groups"
              description="Select the topic groups that can be used to track the topic"
              placeholder="Search topic groups"
              options={tagOptions}
              value={addNewGroupModalForm.values.tags ?? []}
              onChange={(tag) =>
                addNewGroupModalForm.setFieldValue('tags', [
                  tag,
                  ...(addNewGroupModalForm.values.tags ?? []),
                ])
              }
              remove={(id) => {
                addNewGroupModalForm.setFieldValue(
                  'tags',
                  addNewGroupModalForm?.values?.tags?.filter((t) => t.id !== id)
                );
              }}
              pillStyle={{ ...tagPillTheme.root }}
            />
            <Flex gap={8}>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowEditModalType(null);
                  addNewGroupModalForm.reset();
                }}
              >
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      ) : showEditModalType === 'topic-group' ? (
        <form onSubmit={addNewTopicGroupForm.onSubmit(createNewTopicGroup)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Tag Name"
              description="The name of the tag as it will appear throughout the platform"
              placeholder="Enter the tag name"
              type="text"
              {...addNewTopicGroupForm.getInputProps('name')}
              required
            />
            <Input
              label="Description"
              description="The purpose of the tag"
              placeholder="Enter the tag description"
              type="textarea"
              {...addNewTopicGroupForm.getInputProps('description')}
            />
            <Flex gap={8}>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowEditModalType(null);
                  addNewTopicGroupForm.reset();
                }}
              >
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      ) : showConfirmationModal === 'DELETE' ? (
        <form
          className="w-full h-full flex flex-col items-center justify-center px-[20%] text-center gap-[24px]"
          noValidate
        >
          <Text size="xl">Are you sure you want to delete this topic?</Text>
          <Pill variant="secondary" withRemoveButton={false}>
            <Text size="lg" bold>
              {topic?.name}
            </Text>
          </Pill>
          <Text size="md">This action cannot be undone.</Text>
          <div className="flex flex-row gap-[8px] w-full">
            <Button
              onClick={() => {
                setShowConfirmationModal('');
              }}
              variant="secondary"
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setLoading(true);
                await deleteSelectedTopic();
                setShowConfirmationModal('');
                setLoading(false);
              }}
              variant="danger"
            >
              Delete
            </Button>
          </div>
        </form>
      ) : (
        <form onSubmit={editTopicModalForm.onSubmit(editTopic)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Topic Name"
              description="The name of the team as it will appear throughout the platform"
              placeholder="Enter the topic name"
              type="text"
              {...editTopicModalForm.getInputProps('name')}
              disabled
            />
            <Input
              label="Description"
              description="Add descriptive information to provide the model with more context about this topic"
              placeholder="Enter the topic description"
              type="textarea"
              {...editTopicModalForm.getInputProps('description')}
            />

            <SearchableSelect<TopicGroupProps>
              label="Topic Groups"
              description="Select the topic groups that can be used to track the topic"
              placeholder="Search topic groups"
              options={tagOptions}
              value={editTopicModalForm.values.tags}
              onChange={(tag) =>
                editTopicModalForm.setFieldValue('tags', [tag, ...editTopicModalForm.values.tags])
              }
              remove={(id) => {
                editTopicModalForm.setFieldValue(
                  'tags',
                  editTopicModalForm.values.tags.filter((t) => t.id !== id)
                );
              }}
              pillStyle={{ ...tagPillTheme.root }}
              nothingFoundMessage={
                <Flex direction="column" gap={8} align="center" w="100%">
                  <Button
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => {
                      setShowEditModalType('topic-group');
                      addNewTopicGroupForm.setValues({
                        name: searchValues.topicGroupSearch,
                        description: '',
                      });
                      handleSearchValues('topicGroupSearch', '');
                    }}
                  >
                    <Text size="md" className="text-[#7950F2]">
                      {`Create new topic group "${searchValues.topicGroupSearch}"`}
                    </Text>
                  </Button>
                </Flex>
              }
              onSearchChange={(value) => {
                handleSearchValues('topicGroupSearch', value);
              }}
              externalSearchValue={searchValues.topicGroupSearch}
            />

            <SearchableSelect<TeamTypeProps>
              label="Teams"
              description="Select the teams that will have access to the topic"
              placeholder="Search teams"
              options={groupOptions}
              value={editTopicModalForm.values.groups}
              onChange={(group) =>
                editTopicModalForm.setFieldValue('groups', [
                  group,
                  ...editTopicModalForm.values.groups,
                ])
              }
              remove={(id) => {
                editTopicModalForm.setFieldValue(
                  'groups',
                  editTopicModalForm.values.groups.filter((g) => g.id !== id)
                );
              }}
              pillStyle={{ ...groupPillTheme.root }}
              pillIcon={
                <img src="/icons/pills/pillUserIcon.svg" alt="User Icon" className="mr-[10.5px]" />
              }
              nothingFoundMessage={
                <Flex direction="column" gap={8} align="center" w="100%">
                  <Button
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => {
                      setShowEditModalType('group');
                      addNewGroupModalForm.setValues({
                        name: searchValues.groupSearch,
                        description: '',
                        tags: [],
                      });
                      handleSearchValues('groupSearch', '');
                    }}
                  >
                    <Text size="md" className="text-[#7950F2]">
                      {`Create new team "${searchValues.groupSearch}"`}
                    </Text>
                  </Button>
                </Flex>
              }
              onSearchChange={(value) => {
                handleSearchValues('groupSearch', value);
              }}
              externalSearchValue={searchValues.groupSearch}
            />
            <Flex direction="column" gap={8}>
              <Flex gap={8}>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowConfirmationModal('');
                    onClose();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="secondaryDanger"
                  type="button"
                  onClick={() => {
                    setShowConfirmationModal('DELETE');
                  }}
                >
                  Delete Topic
                </Button>
              </Flex>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      )}
    </GenericModal>
  );
};

export default EditTopicModal;
