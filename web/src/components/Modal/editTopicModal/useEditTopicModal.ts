import { useEffect, useState } from 'react';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { groupService } from '@/api/group/groupService';
import { tagService } from '@/api/tags/tagService';
import { topicService } from '@/api/topics/topicService';
import { TeamFormProps, TeamTypeProps } from '@/types/teamType';
import { TopicGroupFormProps, TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicFormProps, TopicProps } from '@/types/topicType';

export const useEditTopicModal = ({
  topic,
  opened,
  onSubmit,
}: {
  topic: TopicProps | null;
  opened: boolean;
  onSubmit: () => void;
}) => {
  const [groupOptions, setGroupOptions] = useState<TeamTypeProps[]>([]);
  const [tagOptions, setTagOptions] = useState<TopicGroupProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState<'' | 'DELETE'>('');

  const [searchValues, setSearchValues] = useState<{
    topicGroupSearch: string;
    groupSearch: string;
  }>({ topicGroupSearch: '', groupSearch: '' });

  const [showEditModalType, setShowEditModalType] = useState<'topic-group' | 'group' | null>(null);

  const editTopicModalForm = useForm<TopicFormProps>({
    initialValues: {
      name: topic?.name || '',
      description: topic?.description || '',
      groups: topic?.groups || [],
      tags: topic?.tags || [],
      userTopicPreference: {
        isFavourite: topic?.userTopicPreference?.isFavourite ?? false,
        negativeSentimentNotifications:
          topic?.userTopicPreference?.negativeSentimentNotifications || 'OFF',
        notificationCadence: topic?.userTopicPreference?.notificationCadence || 'OFF',
        highRelevanceNotifications: topic?.userTopicPreference?.highRelevanceNotifications || 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Topic name is required'),
    },
  });

  const addNewGroupModalForm = useForm<TeamFormProps>({
    initialValues: {
      name: '',
      description: '',
      tags: [],
      userGroupPreference: {
        notificationCadence: 'OFF',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Team name is required'),
    },
  });

  const addNewTopicGroupForm = useForm<TopicGroupFormProps>({
    initialValues: {
      name: '',
      description: '',
      isPersonal: false,
      userTagPreferences: {
        notificationCadence: 'MONTHLY',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Topic Group name is required'),
    },
  });

  const initialize = async () => {
    setLoading(true);
    editTopicModalForm.setValues({
      name: topic?.name || '',
      description: topic?.description || '',
      groups: topic?.groups || [],
      tags: topic?.tags || [],
      userTopicPreference: {
        isFavourite: topic?.userTopicPreference?.isFavourite || false,
        negativeSentimentNotifications:
          topic?.userTopicPreference?.negativeSentimentNotifications || 'OFF',
        highRelevanceNotifications: topic?.userTopicPreference?.highRelevanceNotifications || 'OFF',
        notificationCadence: topic?.userTopicPreference?.notificationCadence || 'WEEKLY',
      },
    });
    Promise.all([groupService.fetchGroups(), tagService.fetchTags()])
      .then(([groupResponse, tagResponse]) => {
        setGroupOptions(groupResponse.data.items as TeamTypeProps[]);
        setTagOptions(tagResponse.data.items as TopicGroupProps[]);
      })
      .catch(() => {
        notifications.show({
          title: 'Error fetching data',
          message: 'There was an error fetching teams or topic groups',
          autoClose: 3000,
          color: 'red',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const editTopic = async (values: TopicFormProps) => {
    setLoading(true);
    try {
      await topicService.editTopic(topic!.id, {
        name: values.name,
        description: values.description,
        groups: values.groups.map((group) => group.name),
        tags: values.tags.map((tag) => tag.id),
        userTopicPreference: {
          isFavourite: topic?.userTopicPreference?.isFavourite ?? false,
          negativeSentimentNotifications:
            values.userTopicPreference.negativeSentimentNotifications ?? 'OFF',
          notificationCadence: values.userTopicPreference.notificationCadence ?? 'OFF',
          highRelevanceNotifications:
            values.userTopicPreference.highRelevanceNotifications ?? 'OFF',
        },
      });
      notifications.show({
        title: 'Topic Successfully Updated!',
        message: `Topic with name ${values.name} has been updated.`,
        autoClose: 3000,
        color: 'teal',
      });
    } catch (error) {
      notifications.show({
        title: 'There was an error updating the topic',
        message: undefined,
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
      setShowConfirmationModal('');
      onSubmit();
    }
  };

  const createNewTopicGroup = async (values: TopicGroupFormProps) => {
    setLoading(true);
    try {
      const response = await tagService.createTags({
        name: values.name,
        description: values.description,
        isPersonal: values.isPersonal,
      });

      await tagService.patchNotificationPreferences([
        {
          id: response.data.id,
          negativeSentimentNotifications: values.userTagPreferences.negativeSentimentNotifications,
          notificationCadence: values.userTagPreferences.notificationCadence,
          highRelevanceNotifications: values.userTagPreferences.highRelevanceNotifications,
        },
      ]);

      setTagOptions((prev) => [...prev, response?.data]);
      editTopicModalForm.setFieldValue('tags', [...editTopicModalForm.values.tags, response?.data]);

      notifications.show({
        title: 'Topic Group Successfully Created!',
        message: 'The topic Group has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
    } catch (error) {
      notifications.show({
        title: 'There was an error creating the topic group',
        message: undefined,
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
      setShowEditModalType(null);
    }
  };

  const createNewGroup = async (values: TeamFormProps) => {
    setLoading(true);
    try {
      const response = await groupService.createGroups({
        name: values.name,
        description: values.description,
      });

      await groupService.patchNotificationPreferences([
        {
          id: response.data.id,
          negativeSentimentNotifications: values.userGroupPreference.negativeSentimentNotifications,
          notificationCadence: values.userGroupPreference.notificationCadence,
          highRelevanceNotifications: values.userGroupPreference.highRelevanceNotifications,
        },
      ]);
      setGroupOptions((prev) => [...prev, response?.data]);

      editTopicModalForm.setFieldValue('groups', [
        ...editTopicModalForm.values.groups,
        response?.data,
      ]);

      notifications.show({
        title: 'Team Successfully Created!',
        message: 'The team has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
    } catch (error) {
      notifications.show({
        title: 'There was an error creating the team',
        message: undefined,
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setShowEditModalType(null);
      setLoading(false);
    }
  };

  const handleSearchValues = (key: 'topicGroupSearch' | 'groupSearch', value: string) => {
    setSearchValues((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  useEffect(() => {
    if (!opened) return;
    initialize();
  }, [topic, opened]);

  return {
    loading,
    groupOptions,
    tagOptions,
    showConfirmationModal,
    editTopicModalForm,
    searchValues,
    addNewTopicGroupForm,
    addNewGroupModalForm,
    showEditModalType,

    setShowConfirmationModal,
    setLoading,
    editTopic,
    handleSearchValues,
    createNewTopicGroup,
    createNewGroup,
    setShowEditModalType,
  };
};
