import { useEffect, useState } from 'react';
import { Flex, Pill, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { groupService } from '@/api/group/groupService';
import { Text } from '@/components/Texts/Text/Text';
import { notifications } from '@/helpers/notifications';
import { TeamFormProps, TeamTypeProps } from '@/types/teamType';
import { TopicGroupProps } from '@/types/topicGroupTypes';
import Button from '../Button/Button';
import { Input } from '../Input/Input';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import GenericModal from './GenericModal';

type EditGroupModalProps = {
  group: TeamTypeProps | null;
  topicGroupOptions: TopicGroupProps[];
  onClose: () => void;
  onSubmit: () => Promise<void>;
  deleteSelectedGroup: () => Promise<void>;
  opened: boolean;
};

const EditGroupModal = ({
  group,
  topicGroupOptions,
  onClose,
  onSubmit,
  deleteSelectedGroup,
  opened,
}: EditGroupModalProps) => {
  const [loading, setLoading] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState<'' | 'DELETE' | 'ADMIN_EDIT'>(
    ''
  );

  const theme = useMantineTheme();
  const groupPillTheme = theme.other.pill.groupPill;
  const tagPillTheme = theme.other.pill.tagPill;

  const form = useForm<TeamFormProps>({
    initialValues: {
      name: '',
      description: '',
      tags: [],
      userGroupPreference: {
        notificationCadence: 'OFF',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Team name is required'),
    },
  });

  const editGroup = async (values: TeamFormProps) => {
    setLoading(true);
    try {
      await groupService.editGroup(String(group?.id), {
        name: values.name,
        description: values.description,
        tags: values.tags?.map((t) => t.id),
      });
      notifications.show({
        title: 'Team Successfully Updated!',
        message: 'The team has been updated successfully.',
        autoClose: 3000,
        color: 'teal',
      });
      await onSubmit();
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error updating the team',
        },
        error
      );
    } finally {
      setLoading(false);
    }
  };

  const initialize = () => {
    if (opened && group && topicGroupOptions) {
      const tagIds = group?.tags?.map((t) => t.id) ?? [];
      const topicGroups = topicGroupOptions?.filter((t) => tagIds.includes(t.id));
      form.setValues({
        name: group.name,
        description: group.description as string,
        tags: topicGroups,
      });
    }
  };

  useEffect(() => {
    initialize();
  }, [group, topicGroupOptions]);

  return (
    <GenericModal
      title={`${showConfirmationModal === 'DELETE' ? 'Delete' : 'Edit'} Team`}
      onClose={() => {
        onClose();
        setShowConfirmationModal('');
      }}
      loading={loading}
      opened={opened}
    >
      {showConfirmationModal === 'DELETE' ? (
        <form
          className="w-full h-full flex flex-col items-center justify-center px-[20%] text-center gap-[24px]"
          noValidate
        >
          <Text size="xl">Are you sure you want to delete this team?</Text>
          <Pill
            variant="secondary"
            withRemoveButton={false}
            style={{
              backgroundColor: groupPillTheme.root.backgroundColor,
              border: groupPillTheme.root.border,
              height: 'auto',
              overflow: 'visible',
            }}
          >
            <Flex direction="row" align="center" className="py-[4px] px-[8px]">
              <img
                src="/icons/pills/pillUserIcon.svg"
                alt="User Icon"
                className="min-w-[14px] min-h-[20px] mr-[13px]"
              />
              <Text size="xl">{group?.name}</Text>
            </Flex>
          </Pill>
          <Text size="sm">This action cannot be undone</Text>

          <div className="flex flex-row gap-[8px] w-full">
            <Button
              onClick={() => {
                setShowConfirmationModal('');
              }}
              variant="secondary"
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setLoading(true);
                await deleteSelectedGroup();
                setShowConfirmationModal('');
                setLoading(false);
              }}
              variant="danger"
            >
              Delete
            </Button>
          </div>
        </form>
      ) : (
        <form onSubmit={form.onSubmit(editGroup)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Team Name"
              description="The name of the team as it will appear throughout the platform"
              placeholder="Enter the team name"
              type="text"
              {...form.getInputProps('name')}
              required
            />
            <Input
              label="Description"
              description="The purpose of the team"
              placeholder="Team description"
              type="textarea"
              {...form.getInputProps('description')}
            />
            <SearchableSelect<TopicGroupProps>
              label="Topic Groups"
              description="Select the topic groups that will be tracked by the team"
              placeholder="Search topic groups"
              options={topicGroupOptions}
              value={form.values.tags ?? []}
              onChange={(tag) => form.setFieldValue('tags', [...(form.values.tags ?? []), tag])}
              remove={(id) => {
                form.setFieldValue(
                  'tags',
                  form?.values?.tags?.filter((t) => t.id !== id)
                );
              }}
              pillStyle={{ ...tagPillTheme.root }}
            />
            <Flex direction="column" gap={8}>
              <Flex gap={8}>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowConfirmationModal('');
                    onClose();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="secondaryDanger"
                  type="button"
                  onClick={async () => {
                    setShowConfirmationModal('DELETE');
                  }}
                >
                  Delete Team
                </Button>
              </Flex>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      )}
    </GenericModal>
  );
};

export default EditGroupModal;
