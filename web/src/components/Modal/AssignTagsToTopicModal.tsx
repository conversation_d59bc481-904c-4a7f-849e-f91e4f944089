import { useEffect, useState } from 'react';
import { Flex, Pill, Select, Switch, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { tagService } from '@/api/tags/tagService';
import { topicService } from '@/api/topics/topicService';
import { Text } from '@/components/Texts/Text/Text';
import { NOTIFICATION_PREFERENCE_OPTIONS } from '@/dictionary/notificationCadenceDictionary';
import { TopicGroupFormProps, TopicGroupProps } from '@/types/topicGroupTypes';
import { TopicProps } from '@/types/topicType';
import Button from '../Button/Button';
import { Input } from '../Input/Input';
import GenericModal from './GenericModal';

type AssignTagsToTopicModalProps = {
  opened: boolean;
  onClose: () => void;
  onSubmit: () => void;
  onExitTransitionEnd?: () => void;
  topics: TopicProps[];
};

const AssignTagsToTopicModal = ({
  opened,
  onClose,
  onSubmit,
  onExitTransitionEnd,
  topics,
}: AssignTagsToTopicModalProps) => {
  const [loading, setLoading] = useState(false);
  const [tagOptions, setTagOptions] = useState<TopicGroupProps[]>([]);
  const [selectedTags, setSelectedTags] = useState<TopicGroupProps[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  const theme = useMantineTheme();
  const tagPillTheme = theme.other.pill.tagPill;
  const switchTheme = theme.other.switch;

  const form = useForm<TopicGroupFormProps>({
    initialValues: {
      name: '',
      description: '',
      isPersonal: false,
      userTagPreferences: {
        notificationCadence: 'MONTHLY',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Topic Group name is required'),
    },
  });

  const initialize = async () => {
    setLoading(true);
    try {
      const res = await tagService.fetchTags();
      const allTags = res.data.items as TopicGroupProps[];
      const usedMap = new Map<number, TopicGroupProps>();
      topics.flatMap((t) => t.tags).forEach((tag) => usedMap.set(tag.id, tag));
      const initial = Array.from(usedMap.values());
      setSelectedTags(initial);
      setTagOptions(allTags.filter((opt) => !usedMap.has(opt.id)));
    } catch (error) {
      notifications.show({
        title: 'Failed to load topic groups',
        color: 'red',
        autoClose: 3000,
        message: '',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    initialize();
  }, [opened, topics]);

  const handleSelect = (value: string | null) => {
    const tag = tagOptions.find((t) => t.name === value);
    if (tag) {
      setSelectedTags((prev) => [...prev, tag]);
      setTagOptions((prev) => prev.filter((t) => t.id !== tag.id));
    }
  };

  const handleRemove = (tagId: number) => {
    const tag = selectedTags.find((t) => t.id === tagId);
    if (tag) {
      setSelectedTags((prev) => prev.filter((t) => t.id !== tagId));
      setTagOptions((prev) => [...prev, tag]);
    }
  };

  const handleAssignTag = async () => {
    setLoading(true);
    try {
      const tagIds = selectedTags.map((t) => t.id);
      await Promise.all(topics.map((topic) => topicService.assignTags(topic.id, tagIds)));
      notifications.show({
        title: 'Topic groups assigned successfully!',
        color: 'teal',
        autoClose: 3000,
        message: '',
      });
      onSubmit();
    } catch {
      notifications.show({
        title: 'Failed to assign topic groups',
        color: 'red',
        autoClose: 3000,
        message: '',
      });
    } finally {
      setLoading(false);
    }
  };

  const createNewTag = async (values: TopicGroupFormProps) => {
    setLoading(true);
    try {
      const response = await tagService.createTags({
        name: values.name,
        description: values.description,
        isPersonal: values.isPersonal,
      });

      await tagService.patchNotificationPreferences([
        {
          id: response.data.id,
          negativeSentimentNotifications: values.userTagPreferences.negativeSentimentNotifications,
          notificationCadence: values.userTagPreferences.notificationCadence,
          highRelevanceNotifications: values.userTagPreferences.highRelevanceNotifications,
        },
      ]);

      notifications.show({
        title: 'Topic Group Successfully Created!',
        message: 'The topic group has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
    } catch (error) {
      notifications.show({
        title: 'There was an error creating the topic group',
        message: undefined,
        autoClose: 3000,
        color: 'red',
      });
    } finally {
      setLoading(false);
      setShowAddModal(false);
      onClose();
    }
  };

  return (
    <GenericModal
      title={showAddModal ? 'Add Topic Group' : 'Assign Topic Groups to Topic'}
      opened={opened}
      onClose={() => {
        onClose();
      }}
      loading={loading}
      onExitTransitionEnd={async () => {
        onExitTransitionEnd?.();
        setShowAddModal(false);
        setTagOptions([]);
        setSelectedTags([]);
        form.reset();
      }}
    >
      {showAddModal ? (
        <form onSubmit={form.onSubmit(createNewTag)} noValidate>
          <Flex direction="column" gap={24}>
            <Input
              label="Topic Group Name"
              description="The name of the topic group as it will appear throughout the platform"
              placeholder="Enter the topic group name"
              type="text"
              {...form.getInputProps('name')}
              required
            />
            <Input
              label="Description"
              description="The purpose of the topic group"
              placeholder="Enter the topic group description"
              type="text"
              {...form.getInputProps('description')}
            />

            <Select
              label="Email Notifications"
              description="Receive email notifications when this topic group is mentioned"
              type="dropdown"
              data={NOTIFICATION_PREFERENCE_OPTIONS}
              {...form.getInputProps('userTagPreferences.notificationCadence')}
              comboboxProps={{ keepMounted: true }}
            />

            <Switch
              label="Negative Sentiment Notifications"
              description="Receive email notifications when this topic group is mentioned with negative sentiment"
              checked={form.values.userTagPreferences.negativeSentimentNotifications !== 'OFF'}
              onChange={(value) => {
                form.setFieldValue(
                  'userTagPreferences.negativeSentimentNotifications',
                  value ? 'MONTHLY' : 'OFF'
                );
              }}
            />
            <div hidden={form.values.userTagPreferences.negativeSentimentNotifications === 'OFF'}>
              <Select
                aria-label="Negative Sentiment Email Notifications"
                data={NOTIFICATION_PREFERENCE_OPTIONS}
                {...form.getInputProps('userTagPreferences.negativeSentimentNotifications')}
                comboboxProps={{ keepMounted: true }}
              />
            </div>

            <Switch
              label="High Relevance Notifications"
              description="Receive email notifications for highly relevant posts that mention this topic group"
              checked={form.values.userTagPreferences.highRelevanceNotifications !== 'OFF'}
              onChange={(value) => {
                form.setFieldValue(
                  'userTagPreferences.highRelevanceNotifications',
                  value ? 'MONTHLY' : 'OFF'
                );
              }}
            />
            <div hidden={form.values.userTagPreferences.highRelevanceNotifications === 'OFF'}>
              <Select
                aria-label="High Relevance Email Notifications"
                data={NOTIFICATION_PREFERENCE_OPTIONS}
                {...form.getInputProps('userTagPreferences.highRelevanceNotifications')}
                comboboxProps={{ keepMounted: true }}
              />
            </div>
            <Switch
              {...form.getInputProps('isPersonal')}
              label="Is this a personal topic group?"
              description={
                form.values.isPersonal ? 'Not visible to other users' : 'Visible to other users'
              }
              color={switchTheme.backgroundColor}
              size="sm"
              mb={8}
              labelPosition="left"
              styles={{
                label: { fontWeight: 700 },
              }}
              checked={form.values.isPersonal}
            />
            <Flex gap={8}>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowAddModal(false);
                  form.reset();
                }}
              >
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </Flex>
          </Flex>
        </form>
      ) : (
        <Flex direction="column" gap={24}>
          <Flex direction="row" gap={8} wrap="wrap">
            {selectedTags.map((tag) => (
              <Pill
                key={tag.id}
                withRemoveButton
                onRemove={() => handleRemove(tag.id)}
                style={{
                  backgroundColor: tagPillTheme.root.backgroundColor,
                  color: tagPillTheme.root.color,
                }}
              >
                {tag.name}
              </Pill>
            ))}
          </Flex>

          <Select
            label="Topic Groups"
            description="Select the topic groups to assign"
            placeholder="Search topic groups"
            data={tagOptions.map((t) => t.name)}
            searchable
            onSearchChange={(val) => setSearchValue(val)}
            value={null}
            nothingFoundMessage={
              <Flex direction="column" gap={8} align="center" w="100%">
                <Text size="md" className="text-[#ADB5BD]">
                  No results found
                </Text>
                <Button
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => {
                    setShowAddModal(true);
                    form.setValues({
                      name: searchValue,
                      description: '',
                    });
                    setSearchValue('');
                  }}
                >
                  <Text
                    size="md"
                    className="text-[#7950F2]"
                  >{`Create new topic group "${searchValue}"`}</Text>
                </Button>
              </Flex>
            }
            onChange={handleSelect}
            disabled={loading}
          />

          <Flex gap={8} justify="flex-end">
            <Button
              variant="secondary"
              onClick={() => {
                setShowAddModal(false);
                onClose();
              }}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button variant="primary" onClick={handleAssignTag} loading={loading}>
              Save
            </Button>
          </Flex>
        </Flex>
      )}
    </GenericModal>
  );
};

export default AssignTagsToTopicModal;
