import { useEffect, useState } from 'react';
import { Flex, Select, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import { tagService } from '@/api/tags/tagService';
import Switch from '@/components/Switch/Switch';
import { useIsAdmin } from '@/context/AppContext';
import { NOTIFICATION_PREFERENCE_OPTIONS } from '@/dictionary/notificationCadenceDictionary';
import { notifications } from '@/helpers/notifications';
import { TeamTypeProps } from '@/types/teamType';
import { TopicGroupFormProps } from '@/types/topicGroupTypes';
import { TopicProps } from '@/types/topicType';
import Button from '../Button/Button';
import { Input } from '../Input/Input';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import GenericModal from './GenericModal';

type AddTagModalProps = {
  onClose: () => void;
  onSubmit: () => Promise<void>;
  opened: boolean;
  topicOptions: TopicProps[];
  groupOptions: TeamTypeProps[];
};

const AddTagModal = ({
  onClose,
  onSubmit,
  opened,
  topicOptions,
  groupOptions,
}: AddTagModalProps) => {
  const [loading, setLoading] = useState(false);
  const switchTheme = useMantineTheme().other.switch;
  const topicPillTheme = useMantineTheme().other.pill.topicPill;
  const groupPillTheme = useMantineTheme().other.pill.groupPill;

  const isAdmin = useIsAdmin();

  const form = useForm<TopicGroupFormProps>({
    initialValues: {
      name: '',
      description: '',
      isPersonal: !isAdmin,
      topics: [],
      groups: [],
      userTagPreferences: {
        notificationCadence: 'OFF',
        negativeSentimentNotifications: 'OFF',
        highRelevanceNotifications: 'OFF',
      },
    },
    validate: {
      name: (value) => (value ? null : 'Topic Group name is required'),
    },
  });

  const createNewTag = async (values: TopicGroupFormProps) => {
    setLoading(true);
    try {
      const response = await tagService.createTags({
        name: values.name,
        description: values.description,
        isPersonal: values.isPersonal,
        topics: values.topics?.map((t) => t.id),
        groups: values.groups?.map((g) => g.id),
      });

      await tagService.patchNotificationPreferences([
        {
          id: response.data.id,
          negativeSentimentNotifications: values.userTagPreferences.negativeSentimentNotifications,
          notificationCadence: values.userTagPreferences.notificationCadence,
          highRelevanceNotifications: values.userTagPreferences.highRelevanceNotifications,
        },
      ]);

      notifications.show({
        title: 'Topic Group Successfully Created!',
        message: 'The topic group has been created successfully.',
        autoClose: 3000,
        color: 'teal',
      });
    } catch (error) {
      notifications.show(
        {
          title: 'There was an error creating the topic group',
        },
        error
      );
    } finally {
      setLoading(false);
      await onSubmit();
    }
  };

  useEffect(() => {
    form.reset();
  }, [opened]);

  return (
    <GenericModal title="Add Topic Group" onClose={onClose} loading={loading} opened={opened}>
      <form onSubmit={form.onSubmit(createNewTag)} noValidate>
        <Flex direction="column" gap={24}>
          <Input
            label="Topic Group Name"
            description="The name of the topic group as it will appear throughout the platform"
            placeholder="Enter the topic group name"
            type="text"
            {...form.getInputProps('name')}
            required
          />
          <Input
            label="Description"
            description="The purpose of the topic group"
            placeholder="Enter the topic group description"
            type="text"
            {...form.getInputProps('description')}
          />

          <Switch
            {...form.getInputProps('isPersonal')}
            label="Is this a personal topic group?"
            description={
              form.values.isPersonal ? 'Not visible to other users' : 'Visible to other users'
            }
            color={switchTheme.backgroundColor}
            switchSize="sm"
            labelPosition="left"
            checked={form.values.isPersonal}
            disabled={!isAdmin}
          />

          <SearchableSelect<TopicProps>
            label="Topics"
            description="Select the topics that will be tracked by the topic group"
            placeholder="Search topics"
            options={topicOptions}
            value={form.values.topics ?? []}
            onChange={(topic) =>
              form.setFieldValue('topics', [...(form.values.topics ?? []), topic])
            }
            remove={(id) => {
              form.setFieldValue(
                'topics',
                form?.values?.topics?.filter((t) => t.id !== id)
              );
            }}
            pillStyle={{ ...topicPillTheme.root }}
          />

          <div hidden={form.values.isPersonal}>
            <SearchableSelect<TeamTypeProps>
              label="Teams"
              description="Select the teams that will track the topic group"
              placeholder="Search teams"
              options={groupOptions}
              value={form.values.groups ?? []}
              onChange={(group) =>
                form.setFieldValue('groups', [...(form.values.groups ?? []), group])
              }
              remove={(id) => {
                form.setFieldValue(
                  'groups',
                  form.values.groups?.filter((g) => g.id !== id)
                );
              }}
              pillStyle={{ ...groupPillTheme.root }}
            />
          </div>

          <Select
            label="Email Notifications"
            description="Receive email notifications when this topic group is mentioned"
            type="dropdown"
            data={NOTIFICATION_PREFERENCE_OPTIONS}
            {...form.getInputProps('userTagPreferences.notificationCadence')}
            comboboxProps={{ keepMounted: true }}
          />

          <Switch
            label="Negative Sentiment Notifications"
            description="Receive email notifications when this topic group is mentioned with negative sentiment"
            checked={form.values.userTagPreferences.negativeSentimentNotifications !== 'OFF'}
            onChange={(value) => {
              form.setFieldValue(
                'userTagPreferences.negativeSentimentNotifications',
                value ? 'MONTHLY' : 'OFF'
              );
            }}
          />
          <div hidden={form.values.userTagPreferences.negativeSentimentNotifications === 'OFF'}>
            <Select
              aria-label="Negative Sentiment Email Notifications"
              data={NOTIFICATION_PREFERENCE_OPTIONS}
              {...form.getInputProps('userTagPreferences.negativeSentimentNotifications')}
              comboboxProps={{ keepMounted: true }}
            />
          </div>

          <Switch
            label="High Relevance Notifications"
            description="Receive email notifications for highly relevant posts that mention this topic group"
            checked={form.values.userTagPreferences.highRelevanceNotifications !== 'OFF'}
            onChange={(value) => {
              form.setFieldValue(
                'userTagPreferences.highRelevanceNotifications',
                value ? 'MONTHLY' : 'OFF'
              );
            }}
          />
          <div hidden={form.values.userTagPreferences.highRelevanceNotifications === 'OFF'}>
            <Select
              aria-label="High Relevance Email Notifications"
              data={NOTIFICATION_PREFERENCE_OPTIONS}
              {...form.getInputProps('userTagPreferences.highRelevanceNotifications')}
              comboboxProps={{ keepMounted: true }}
            />
          </div>
          <Flex gap={8}>
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Save
            </Button>
          </Flex>
        </Flex>
      </form>
    </GenericModal>
  );
};

export default AddTagModal;
