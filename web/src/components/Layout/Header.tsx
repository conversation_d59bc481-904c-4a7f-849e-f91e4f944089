import { useNavigate } from 'react-router-dom';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';
import Button from '../Button/Button';

interface HeaderProps {
  setMobileMenuOpen: (open: boolean) => void;
  mobileMenuOpen: boolean;
}

const Header = ({ setMobileMenuOpen, mobileMenuOpen }: HeaderProps) => {
  const { sessionTenant } = useAppContext();
  const isMicrochipTenantSession = sessionTenant?.id === 'DEFAULT';

  const navigate = useNavigate();
  const { userInfo } = useAppContext();

  return (
    <div
      className="
        fixed top-0 left-0 z-50 w-full
        flex items-center justify-between
        px-[20px] py-[12px]
        bg-[#212123]
      "
    >
      <Button
        className="cursor-pointer"
        onClick={() => {
          navigate('/timeline');
        }}
      >
        <div className="flex flex-row items-center gap-[12px]">
          <img src="/icons/truthKeepLogo.svg" alt="logo" className="w-[36px] h-[36px]" />
          <p className="text-[18px] leading-[24px] font-[700] text-[#FBFBFB]">Truthkeep</p>
        </div>
      </Button>

      <div className="flex flex-row items-center gap-[12px]">
        <img
          src={isMicrochipTenantSession ? '/icons/microchipLogo.svg' : '/icons/miscIcons/user.svg'}
          alt="user"
          className="w-[32px] h-[32px] hidden md:block"
        />
        <Button
          onClick={() => {
            setMobileMenuOpen(!mobileMenuOpen);
          }}
          className={`
            w-[18px] h-[13.5px] cursor-pointer block md:hidden
            transition-transform duration-300
            ${mobileMenuOpen ? 'rotate-90' : 'rotate-0'}
          `}
        >
          <img src="/icons/hamburgerIcon.svg" alt="hamburgerIcon" />
        </Button>
        <Text size="lg" bold className="text-[#FBFBFB] hidden md:block">
          {userInfo?.firstName} {userInfo?.lastName}
        </Text>
      </div>
    </div>
  );
};

export default Header;
