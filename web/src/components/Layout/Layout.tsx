import { ReactNode, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { truthkeepAnalyticsService } from '@/api/truthkeep-analytics/truthkeepAnalyticsService';
import Header from './Header';
import Nav from './Nav';

export function Layout({ children }: { children: ReactNode }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setMobileMenuOpen(false);
      }
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const { pathname } = useLocation();
  useEffect(() => {
    // Track page access
    truthkeepAnalyticsService.trackPageAccess().catch(() => {
      // Fail silently
    });
  }, [pathname]);

  return (
    <div className="relative flex flex-col min-h-screen bg-[#F8F9FA]">
      <Header setMobileMenuOpen={setMobileMenuOpen} mobileMenuOpen={mobileMenuOpen} />
      <Nav mobileMenuOpen={mobileMenuOpen} />
      <div
        className={`block md:hidden fixed top-0 left-0 w-full h-full bg-[#000] transition-opacity duration-300 z-10 ${
          mobileMenuOpen ? 'opacity-70' : 'opacity-0 pointer-events-none'
        }`}
      />
      <div className="pt-[60px] pl-0 md:pl-[300px] min-h-screen flex flex-col bg-[#F8F9FA]">
        {children}
      </div>
    </div>
  );
}
