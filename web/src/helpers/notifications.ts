import { AxiosError } from 'axios';
import { notifications as mantineNotifications, NotificationData } from '@mantine/notifications';

const DEFAULT_NOTIFICATION_DATA: Partial<NotificationData> = {
  autoClose: 3000,
  color: 'red',
};

export const notifications = {
  ...mantineNotifications,
  show: (notificationData: Partial<NotificationData>, error?: unknown) => {
    const isPermissionError = error instanceof AxiosError && error.response?.status === 403;
    const isConflictError = error instanceof AxiosError && error.response?.status === 409;
    const message = isPermissionError
      ? 'You do not have permission to perform this action.'
      : isConflictError
        ? 'A resource with this name already exists.'
        : notificationData.message;

    mantineNotifications.show({
      ...DEFAULT_NOTIFICATION_DATA,
      ...notificationData,
      message,
    });
  },
};
