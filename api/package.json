{"name": "nest-scraper", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"setup-database": "prisma generate && prisma migrate deploy && prisma db seed", "build": "nest build", "format": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\" \"baseline-test/**/*.ts\"", "format:fix": "npm run format -- --write", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --ignore-pattern \"baseline-test/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "PORT=3001 NODE_OPTIONS='--max-old-space-size=4096' jest --config ./test/jest-e2e.json", "migrate:dev": "npx prisma migrate dev", "migrate:dev:create": "npx prisma migrate dev --create-only", "migrate:deploy": "npx prisma migrate deploy", "prisma:generate": "npx prisma generate", "prisma:studio": "npx prisma studio", "prisma:seed": "npx prisma db seed", "prisma:seed:test": "npx prisma db seed -- --environment test"}, "dependencies": {"@aws-sdk/client-s3": "^3.872.0", "@aws-sdk/client-ses": "^3.750.0", "@bull-board/api": "^6.7.9", "@bull-board/express": "^6.7.9", "@bull-board/nestjs": "^6.7.9", "@epic-web/cachified": "^5.5.1", "@fast-csv/format": "^5.0.5", "@googleapis/youtube": "^20.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/class-transformer": "^0.4.0", "@nestjs/cli": "^11.0.10", "@nestjs/common": "^11.0.6", "@nestjs/core": "^11.0.6", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.6", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.2", "@nestjs/swagger": "^11.0.3", "@prisma/client": "^6.2.1", "@types/bcrypt": "^5.0.2", "@types/html-to-text": "^9.0.4", "bcrypt": "^5.1.1", "bullmq": "^5.41.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "handlebars": "^4.7.8", "html-to-text": "^9.0.5", "natural": "^8.1.0", "nestjs-cls": "^6.0.1", "nestjs-prisma": "^0.25.0-dev.0", "nestjs-s3": "^3.0.1", "openai": "^4.103.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma": "^6.14.0", "socks-proxy-agent": "^8.0.5", "temporal-polyfill": "^0.3.0", "tiktoken": "^1.0.21", "ts-node": "^10.9.2", "zod": "^3.24.2"}, "devDependencies": {"@darraghor/eslint-plugin-nestjs-typed": "^6.1.3", "@eslint/js": "^9.19.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.6", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.18", "@types/node": "^22.10.10", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "cookie-es": "^1.2.2", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.22.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}