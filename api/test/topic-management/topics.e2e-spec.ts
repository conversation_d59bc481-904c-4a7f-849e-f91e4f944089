import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";
import TestAgent from "supertest/lib/agent";

import { login, loginAsAdmin, createApp, stopApp } from "../util";

describe("Topics", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("POST /topics 400", async () => {
        const request = await login();
        await request.post("/topics").expect(400);
    });

    describe("", () => {
        let userSession: TestAgent;
        let adminSession: TestAgent;
        let testTag;
        let testGroup;

        beforeAll(async () => {
            userSession = await login();
            adminSession = await loginAsAdmin();

            // Create a tag
            {
                const name = `tag-${randomUUID()}`;
                const description = "test description";
                const tagResponse = await adminSession
                    .post("/tags")
                    .send({
                        name,
                        description,
                    })
                    .expect(201);
                testTag = tagResponse.body;
            }

            // Create a group
            {
                const name = `group-${randomUUID()}`;
                const description = "This is a test group";
                const groupResponse = await adminSession
                    .post("/groups")
                    .send({ name, description })
                    .expect(201);
                testGroup = groupResponse.body;
            }
        });

        afterAll(async () => {
            // Delete the tag
            await adminSession.delete(`/tags/${testTag.id}`).expect(204);

            // Delete the group
            await adminSession.delete(`/groups/${testGroup.id}`).expect(204);
        });

        describe("Create topic", () => {
            let topic;
            let name;
            let description;

            beforeEach(async () => {
                name = `topic-${randomUUID()}`;
                description = `description ${randomUUID()}`;

                const createTopicResponse = await adminSession
                    .post("/topics")
                    .send({
                        name,
                        description,
                        tags: [testTag.id],
                        groups: [testGroup.name],
                        userTopicPreference: {
                            isFavourite: true,
                            notificationCadence: "WEEKLY",
                        },
                    })
                    .expect(201);

                topic = createTopicResponse.body;
            });

            it("Topic is created correctly", () => {
                expect(topic).toMatchObject({
                    id: expect.any(Number),
                    name,
                    description,
                    userTopicPreference: {
                        isFavourite: true,
                        notificationCadence: "WEEKLY",
                    },
                });
            });

            it("GET /topics 200 returns the topic", async () => {
                // Get list of topics
                const userSession = await login();
                const getTopicsResponse = await userSession
                    .get("/topics")
                    .expect(200);

                // Check the response structure
                expect(getTopicsResponse.body).toMatchObject({
                    page: 1,
                    total: expect.any(Number),
                    totalPages: expect.any(Number),
                });

                // Check the newly created topic is present
                expect(
                    getTopicsResponse.body.items.length,
                ).toBeGreaterThanOrEqual(1);
                expect(getTopicsResponse.body.items[0]).toMatchObject({
                    id: topic.id,
                    name,
                    description,
                });

                // Check that tags are set correctly
                const { tags } = getTopicsResponse.body.items[0];
                expect(tags).toMatchObject([{ name: testTag.name }]);

                // Check that groups are set correctly
                const { groups } = getTopicsResponse.body.items[0];
                expect(groups).toMatchObject([{ name: testGroup.name }]);

                // Check that "createdBy" is set correctly
                const usersMeResponse = await adminSession
                    .get("/users/me")
                    .expect(200);
                const {
                    id: userId,
                    firstName,
                    lastName,
                } = usersMeResponse.body;

                const { createdBy } = getTopicsResponse.body.items[0];
                expect(createdBy).toMatchObject({
                    id: userId,
                    firstName,
                    lastName,
                });
            });

            it("GET /topics 200 filters topics", async () => {
                const userSession = await login();

                // Filter by non-existent tag
                {
                    const getTopicsResponse = await userSession
                        .get("/topics?tagIds=-1")
                        .expect(200);

                    expect(getTopicsResponse.body).toMatchObject({
                        items: [],
                        total: 0,
                        totalPages: 0,
                    });
                }

                // Filter by non-existent group
                {
                    const getTopicsResponse = await userSession
                        .get("/topics?groupIds=-1")
                        .expect(200);

                    expect(getTopicsResponse.body).toMatchObject({
                        items: [],
                        total: 0,
                        totalPages: 0,
                    });
                }

                // Filter by favourite status
                {
                    const getTopicsResponse = await userSession
                        .get("/topics?onlyFavourited=true")
                        .expect(200);

                    expect(getTopicsResponse.body).toMatchObject({
                        items: [],
                        total: 0,
                        totalPages: 0,
                    });
                }

                // Filter by existant tag and group
                {
                    const getTopicsResponse = await userSession
                        .get(
                            `/topics?tagIds=${testTag.id}&groupIds=${testGroup.id}`,
                        )
                        .expect(200);

                    expect(getTopicsResponse.body).toMatchObject({
                        items: expect.arrayContaining([
                            expect.objectContaining({
                                id: topic.id,
                            }),
                        ]),
                    });
                }
            });

            describe("Edit topic", () => {
                let newGroups: string[];
                let newTagIds: string[];

                beforeEach(async () => {
                    newGroups = [];
                    newTagIds = [];

                    // Create new groups
                    for (let i = 0; i < 2; i++) {
                        const newGroupResponse = await adminSession
                            .post("/groups")
                            .send({ name: `new-group ${randomUUID()}` })
                            .expect(201);
                        newGroups.push(newGroupResponse.body.name);
                    }
                    expect(newGroups.length).toEqual(2);

                    // Create new tags
                    for (let i = 0; i < 2; i++) {
                        const newTagResponse = await adminSession
                            .post("/tags")
                            .send({ name: `new-tag ${randomUUID()}` })
                            .expect(201);
                        newTagIds.push(newTagResponse.body.id);
                    }
                    expect(newTagIds.length).toEqual(2);
                });

                it("PUT /topics/:id edits the topic details", async () => {
                    const request = await login();

                    const newDescription = `new-description ${randomUUID()}`;

                    // send PUT request
                    const updateTopicResponse = await request
                        .put(`/topics/${topic.id}`)
                        .send({
                            name: topic.name,
                            description: newDescription,
                            tags: newTagIds,
                            groups: newGroups,
                            userTopicPreference: {
                                notificationCadence: "WEEKLY",
                            },
                        })
                        .expect(200);

                    // Check response body
                    const newTopic = updateTopicResponse.body;
                    expect(newTopic).toMatchObject({
                        id: topic.id,
                        name: topic.name,
                        description: newDescription,
                        tags: [{ id: newTagIds[0] }, { id: newTagIds[1] }],
                        groups: [
                            { name: newGroups[0] },
                            { name: newGroups[1] },
                        ],
                        userTopicPreference: {
                            notificationCadence: "WEEKLY",
                        },
                    });
                });

                it("PUT /topics/:id/groups edits the topic's groups", async () => {
                    const request = await login();

                    // send PUT request
                    const updateTopicGroupsResponse = await request
                        .put(`/topics/${topic.id}/groups`)
                        .send(newGroups)
                        .expect(200);

                    // Check response body
                    const newTopicGroups = updateTopicGroupsResponse.body;
                    expect(newTopicGroups).toMatchObject([
                        { name: newGroups[0] },
                        { name: newGroups[1] },
                    ]);
                });

                it("PUT /topics/:id/tags edits the topic's tags", async () => {
                    const request = await login();

                    // send PUT request
                    const updateTopicTagsResponse = await request
                        .put(`/topics/${topic.id}/tags`)
                        .send(newTagIds)
                        .expect(200);

                    // Check response body
                    const newTopicTags = updateTopicTagsResponse.body;
                    expect(newTopicTags).toMatchObject([
                        { id: newTagIds[0] },
                        { id: newTagIds[1] },
                    ]);
                });

                it("PATCH /topics/preferences - bulk patching", async () => {
                    const userSession = await login();

                    // Update topic preferences for this user
                    await userSession
                        .patch(`/topics/preferences`)
                        .send({
                            items: [
                                {
                                    id: topic.id,
                                    patch: {
                                        isFavourite: true,
                                        notificationCadence: "WEEKLY",
                                        highRelevanceNotifications: "MONTHLY",
                                        negativeSentimentNotifications: "DAILY",
                                    },
                                },
                            ],
                        })
                        .expect(204);

                    // Check that the topic is updated
                    const getTopicsResponse = await userSession
                        .get("/topics")
                        .expect(200);

                    expect(getTopicsResponse.body.items[0]).toMatchObject({
                        userTopicPreference: {
                            isFavourite: true,
                            notificationCadence: "WEEKLY",
                            highRelevanceNotifications: "MONTHLY",
                            negativeSentimentNotifications: "DAILY",
                        },
                    });

                    // Check that the topic preferences are un-changed for other users
                    const secondUserSession = await login();
                    const secondGetTopicsResponse = await secondUserSession
                        .get("/topics")
                        .expect(200);

                    expect(
                        secondGetTopicsResponse.body.items[0]
                            .userTopicPreference,
                    ).toBeUndefined();
                });

                describe("Delete topic", () => {
                    it("DELETE /topics 403 as non-admin", async () => {
                        const { id } = topic;

                        // Delete the topic
                        await userSession
                            .delete("/topics")
                            .send({ topicIds: [id] })
                            .expect(403);
                    });

                    it("DELETE /topics", async () => {
                        const { id } = topic;

                        // Delete the topic
                        await adminSession
                            .delete("/topics")
                            .send({ topicIds: [id] })
                            .expect(204);

                        // Topic no longer exists
                        const getTopicsResponse = await adminSession
                            .get("/topics")
                            .expect(200);

                        const [matchingTopic] =
                            getTopicsResponse.body.items.filter(
                                (topic) => topic.id === id,
                            );
                        expect(matchingTopic).not.toBeDefined();
                    });
                });
            });
        });
    });
});
