import { INestApplication } from "@nestjs/common";

import {
    Analysis,
    DataSource,
    NotificationCadence,
    PrismaClient,
} from "@prisma/client";
import { randomUUID } from "crypto";
import TestAgent from "supertest/lib/agent";

import {
    createAndOnboardNewUser,
    loginAsSuperAdmin,
    readLatestEmailTo,
    createApp,
    stopApp,
    startApp,
    buildApp,
    loginAsAdmin,
    printFailedBullMQJobs,
} from "../util";
import { waitFor } from "../util";

jest.setTimeout(30000);

describe("Email notifications", () => {
    let app: INestApplication;
    let adminSession: TestAgent;
    let prisma: PrismaClient;
    let topicName: string;
    let topicId: number;
    let analysis: Analysis;

    beforeAll(async () => {
        app = await createApp();

        adminSession = await loginAsAdmin();

        // Create a new topic
        topicName = `Test Topic ${randomUUID()}`;
        const topicResponse = await adminSession
            .post("/topics")
            .send({
                name: topicName,
            })
            .expect(201);
        topicId = topicResponse.body.id;

        // Stop app before manually adding timeline items
        await stopApp(app);

        app = await buildApp();

        prisma = new PrismaClient();

        // Create a new reddit post
        const redditPostId = randomUUID();
        await prisma.redditPost.create({
            data: {
                id: redditPostId,
                title: "Test Title",
                publishedAt: new Date(),
                text: "Test Text",
                tenantId: "DEFAULT",
            },
        });

        // Create a new analysis
        analysis = await prisma.analysis.create({
            data: {
                source: DataSource.REDDIT_POST,
                sourceId: redditPostId,
                sentiment: 50,
                summary: "Test Summary",
                relevance: 50,
                tenantId: "DEFAULT",
                narratives: {
                    create: {
                        sentiment: 50,
                        narrative: {
                            create: {
                                summary: "Test narrative summary",
                                topic: {
                                    connect: {
                                        tenantId_name: {
                                            tenantId: "DEFAULT",
                                            name: topicName,
                                        },
                                    },
                                },
                                tenant: {
                                    connect: {
                                        id: "DEFAULT",
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });

        await startApp(app);
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
        await printFailedBullMQJobs();
    });

    it("Sends daily digest email", async () => {
        const user = await adminSession.get("/users/me").expect(200);
        const userEmailAddress = user.body.email;

        // Subscribe to daily notifications
        await adminSession
            .patch("/topics/preferences")
            .send({
                items: [
                    {
                        id: topicId,
                        notifications: NotificationCadence.DAILY,
                    },
                ],
            })
            .expect(204);

        // Trigger daily notification job
        await adminSession.post("/notifications/daily-digest").expect(204);

        // Check that an email was sent
        await waitFor(async () => {
            const email = await readLatestEmailTo(userEmailAddress);
            expect(email.subject).toContain("Truthkeep daily digest");
            expect(email.text).toContain(topicName);
            expect(email.html).toContain(topicName);
            return true;
        });
    });

    it("Sends weekly digest email", async () => {
        const user = await adminSession.get("/users/me").expect(200);
        const userEmailAddress = user.body.email;

        // Subscribe to weekly notifications
        await adminSession
            .patch("/topics/preferences")
            .send({
                items: [
                    {
                        id: topicId,
                        notifications: NotificationCadence.WEEKLY,
                    },
                ],
            })
            .expect(204);

        // Trigger weekly notification job
        await adminSession.post("/notifications/weekly-digest").expect(204);

        // Check that an email was sent
        await waitFor(async () => {
            const email = await readLatestEmailTo(userEmailAddress);
            expect(email.subject).toContain("Truthkeep weekly digest");
            expect(email.text).toContain(topicName);
            expect(email.html).toContain(topicName);
            return true;
        });
    });

    it("Sends monthly digest email", async () => {
        const user = await adminSession.get("/users/me").expect(200);
        const userEmailAddress = user.body.email;

        // Subscribe to monthly notifications
        await adminSession
            .patch("/topics/preferences")
            .send({
                items: [
                    {
                        id: topicId,
                        notifications: NotificationCadence.MONTHLY,
                    },
                ],
            })
            .expect(204);

        // Trigger monthly notification job
        await adminSession.post("/notifications/monthly-digest").expect(204);

        // Check that an email was sent
        await waitFor(async () => {
            const email = await readLatestEmailTo(userEmailAddress);
            expect(email.subject).toContain("Truthkeep monthly digest");
            expect(email.text).toContain(topicName);
            expect(email.html).toContain(topicName);
            return true;
        });
    });

    it("Sends group share email", async () => {
        const request = await loginAsSuperAdmin();

        // Create a new user
        const newUser = await createAndOnboardNewUser();

        // Create a new group
        const groupName = `Test Group ${randomUUID()}`;
        const groupResponse = await request
            .post("/groups")
            .send({
                name: groupName,
            })
            .expect(201);
        const groupId = groupResponse.body.id;

        // Add the new user to the group
        await request
            .put(`/users/${newUser.userId}`)
            .send({
                firstName: "Test",
                lastName: "User",
                email: newUser.email,
                groups: [groupId],
            })
            .expect(200);

        // Share a post with the group
        await request
            .post(`/insights/timeline/${analysis.id}/groups/${groupId}`)
            .expect(201);

        // Trigger notification job
        await request.post("/notifications/group-share").expect(204);

        // Check that an email was sent
        await waitFor(async () => {
            const email = await readLatestEmailTo(newUser.email);
            expect(email.subject).toContain("Shared with you");
            expect(email.text).toContain(groupName);
            expect(email.html).toContain(groupName);
            return true;
        });
    });
});
