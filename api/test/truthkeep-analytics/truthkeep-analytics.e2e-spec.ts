import { INestApplication } from "@nestjs/common";

import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import {
    createAndOnboardNewUser,
    createApp,
    login,
    loginAsAdmin,
    loginAsSuperAdmin,
    stopApp,
} from "../util";

describe("Truthkeep analytics", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("403 for non-admins", async () => {
        const request = await login(URL_BASE);
        await request.get("/truthkeep-analytics").expect(403);
    });

    it("403 for non-super admins", async () => {
        const request = await loginAsAdmin(URL_BASE);
        await request.get("/truthkeep-analytics").expect(403);
    });

    it("Returns analytics with date range", async () => {
        const request = await loginAsSuperAdmin();
        const response = await request
            .get("/truthkeep-analytics?dateFrom=2000-01-01&dateTo=2000-12-31")
            .expect(200);

        expect(response.body).toMatchObject({
            users: expect.arrayContaining([
                expect.objectContaining({
                    firstName: expect.any(String),
                    lastName: expect.any(String),
                    lastAccess: null,
                    timeline: 0,
                    analytics: 0,
                    topicManagement: 0,
                }),
            ]),
        });
    });

    describe("POST /truthkeep-analytics", () => {
        it("Updates analytics", async () => {
            // Create a new user
            const user = await createAndOnboardNewUser();
            const userSession = await supertest.agent(URL_BASE);

            await userSession
                .post("/login")
                .send({ username: user.email, password: user.password })
                .expect(204);

            const superAdminSession = await loginAsSuperAdmin();

            // Check analytics count for new user
            let response = await superAdminSession
                .get("/truthkeep-analytics")
                .expect(200);
            let userAnalytics = response.body.users.find(
                ({ id }) => id === user.userId,
            );
            expect(userAnalytics?.timeline ?? 0).toBe(0);

            // Track access to timeline page
            await userSession
                .post("/truthkeep-analytics")
                .set("Referer", "https://example.com/timeline")
                .expect(204);

            response = await superAdminSession
                .get("/truthkeep-analytics")
                .expect(200);

            // Analytics count is updated
            userAnalytics = response.body.users.find(
                ({ id }) => id === user.userId,
            );

            // Count is incremented
            expect(userAnalytics?.timeline ?? 0).toBe(1);

            // Last access time is updated
            const lastAccess = userAnalytics?.lastAccess;
            const lastAccessTime = new Date(lastAccess).getTime();
            expect((new Date().getTime() - lastAccessTime) / 1000).toBeLessThan(
                24 * 60 * 60,
            );
        });
    });

    describe("/truthkeep-analytics/users", () => {
        it("403 for non-admins", async () => {
            const request = await login();
            await request.get("/truthkeep-analytics/users").expect(403);
        });

        it("403 for non-super admins", async () => {
            const request = await loginAsAdmin();
            await request.get("/truthkeep-analytics/users").expect(403);
        });

        it("Returns users as text", async () => {
            const request = await loginAsSuperAdmin();
            const response = await request
                .get("/truthkeep-analytics/users")
                .expect(200);
            const users = response.text.split("\n");
            expect(users.length).toBeGreaterThan(0);
            expect(users).toContain(process.env.ADMIN_EMAIL);
        });
    });
});
