import { INestApplication } from "@nestjs/common";

import { Narrative, PrismaClient, Subreddit, Topic } from "@prisma/client";
import { randomUUID } from "crypto";
import TestAgent from "supertest/lib/agent";

import { MICROCHIP_TOPIC_NAME } from "../../src/common/constants";
import { login, buildApp, startApp, stopApp } from "../util";

describe("Analytics", () => {
    let app: INestApplication;
    let prisma: PrismaClient;
    let request: TestAgent;
    let subreddit: Subreddit;
    let topic: Topic;
    let narrative: Narrative;

    beforeAll(async () => {
        prisma = new PrismaClient();

        // Delete existing data
        await prisma.analysis.deleteMany();
        await prisma.topic.deleteMany();
        await prisma.narrative.deleteMany();
        await prisma.redditPost.deleteMany();
        await prisma.subreddit.deleteMany();

        // Create a subreddit
        subreddit = await prisma.subreddit.create({
            data: {
                tenantId: "DEFAULT",
                name: `subreddit-${randomUUID()}`,
            },
        });

        // Create a reddit post
        const redditPost = await prisma.redditPost.create({
            data: {
                id: randomUUID(),
                text: `text-${randomUUID()}`,
                title: `title-${randomUUID()}`,
                subreddit: {
                    connect: {
                        tenantId_name: {
                            tenantId: "DEFAULT",
                            name: subreddit.name,
                        },
                    },
                },
            },
        });

        // Create a sentiment analysis
        topic = await prisma.topic.create({
            data: {
                name: MICROCHIP_TOPIC_NAME,
                tenantId: "DEFAULT",
            },
        });

        // Create a narrative
        narrative = await prisma.narrative.create({
            data: {
                aspect: "BUG",
                summary: `summary-${randomUUID()}`,
                tenant: {
                    connect: {
                        id: "DEFAULT",
                    },
                },
                topic: {
                    connect: {
                        tenantId_name: {
                            tenantId: "DEFAULT",
                            name: topic.name,
                        },
                    },
                },
            },
        });

        // Create an analysis
        await prisma.$transaction(async (tx) => {
            const analysis = await tx.analysis.create({
                data: {
                    source: "REDDIT_POST",
                    sourceId: redditPost.id,
                    summary: `summary-${randomUUID()}`,
                    sentiment: 50,
                    relevance: 50,
                    tenant: {
                        connect: {
                            id: "DEFAULT",
                        },
                    },
                },
            });

            await tx.analysisToNarrative.create({
                data: {
                    analysisId: analysis.id,
                    narrativeId: narrative.id,
                    sentiment: 50,
                },
            });
        });

        app = await buildApp();
        await startApp(app);

        request = await login();
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    it("Returns source analytics", async () => {
        const analyticsResponse = await request
            .get("/insights/analytics/sources/reddit")
            .expect(200);

        expect(analyticsResponse.body).toEqual(
            expect.arrayContaining([
                {
                    name: subreddit.name,
                    count: 1,
                    sentiment: 50,
                    sentimentDistribution: [0, 0, 1, 0, 0],
                },
            ]),
        );
    });

    it("Returns trending topic analytics", async () => {
        const analyticsResponse = await request
            .get("/insights/analytics/trends/topics")
            .expect(200);

        expect(analyticsResponse.body).toEqual(
            expect.arrayContaining([
                {
                    id: expect.any(Number),
                    name: topic.name,
                    count: 1,
                    sentiment: 50,
                    sentimentDistribution: [0, 0, 1, 0, 0],
                },
            ]),
        );
    });

    it("Returns trending narrative analytics", async () => {
        const request = await login();

        const analyticsResponse = await request
            .get("/insights/analytics/trends/narratives")
            .expect(200);

        expect(analyticsResponse.body).toEqual(
            expect.arrayContaining([
                {
                    id: narrative.id,
                    summary: narrative.summary,
                    count: 1,
                    sentiment: 50,
                    sentimentDistribution: [0, 0, 1, 0, 0],
                },
            ]),
        );
    });

    it("Returns narrative area chart data", async () => {
        const request = await login();

        const analyticsResponse = await request
            .get(
                `/insights/analytics/comparison/aspects/mentions?topicIds=${topic.id}&aspects=${narrative.aspect}`,
            )
            .expect(200);

        const { topics, data } = analyticsResponse.body;

        expect(topics).toContain(topic.name);

        // Find a data point that contains the topic
        const entryWithTopic = data.find(
            (d: Record<string, number>) => topic.name in d,
        );

        expect(entryWithTopic).toEqual(
            expect.objectContaining({
                date: expect.any(String),
                [topic.name]: expect.any(Number),
            }),
        );
    });

    it("Returns topic/narrative scatter plot", async () => {
        const request = await login();

        const analyticsResponse = await request
            .get("/insights/analytics/comparison/narratives")
            .expect(200);

        expect(analyticsResponse.body).toEqual(
            expect.arrayContaining([
                {
                    narrativeId: expect.any(Number),
                    summary: expect.any(String),
                    narrativeAspect: expect.any(String),
                    topicId: expect.any(Number),
                    topicName: expect.any(String),
                    sentiment: expect.any(Number),
                    count: expect.any(Number),
                },
            ]),
        );

        // Handles filters
        await request
            .get(
                `/insights/analytics/comparison/narratives?narrativeIds=${narrative.id}&topicIds=${topic.id}`,
            )
            .expect(200);
    });

    it("Returns topic/narrative scatter plot popover data", async () => {
        const request = await login();

        const analyticsResponse = await request
            .get("/insights/analytics/comparison/narratives")
            .expect(200);

        const narrativeId = analyticsResponse.body[0].narrativeId;

        const popoverResponse = await request
            .get(`/insights/analytics/narratives/${narrativeId}`)
            .expect(200);

        expect(popoverResponse.body).toEqual(
            expect.objectContaining({
                id: narrativeId,
                summary: expect.any(String),
                narrativeAspect: expect.any(String),
                topicId: expect.any(Number),
                topicName: expect.any(String),
                sentiment: expect.any(Number),
                count: expect.any(Number),
                sentimentDistribution: [0, 0, 1, 0, 0],
            }),
        );
    });

    it("GET /insights/timeline/tenant", async () => {
        const request = await login();

        const response = await request
            .get("/insights/timeline/tenant?sort=NEGATIVE_SENTIMENT")
            .expect(200);

        expect(response.body.total).toBeGreaterThan(0);

        expect(response.body).toMatchObject({
            items: expect.arrayContaining([
                expect.objectContaining({
                    id: expect.any(Number),
                    type: expect.stringMatching(
                        /REDDIT_POST|YOUTUBE_VIDEO|YOUTUBE_COMMENT|AVR_FREAKS|MICROCHIP_CLASSIC/,
                    ),
                    sourceId: expect.any(String),
                    voteScore: expect.any(Number),
                    commentCount: expect.any(Number),
                    publishedAt: expect.any(String),
                    externalUrl: expect.any(String),
                    title: expect.any(String),
                    topics: expect.arrayContaining([
                        expect.objectContaining({
                            name: MICROCHIP_TOPIC_NAME,
                        }),
                    ]),
                }),
            ]),
        });
    });
});
