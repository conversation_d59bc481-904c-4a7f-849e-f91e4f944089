import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import { ParsedResponse } from "openai/resources/responses/responses";
import * as request from "supertest";

import { OpenaiService } from "../../src/openai/openai.service";
import { URL_BASE } from "../constants";
import { MockOpenaiService } from "../mocks/openai.service";
import { createApp, login, stopApp } from "../util";

describe("Chat", () => {
    let app: INestApplication;
    let prisma: PrismaClient;
    const mockOpenaiService = new MockOpenaiService();
    const createResponseSpy = jest
        .spyOn(mockOpenaiService, "createResponse")
        .mockResolvedValue({
            output_text: "Hello yourself!",
        } as ParsedResponse<unknown>);

    beforeAll(async () => {
        app = await createApp({
            providerOverrides: [[OpenaiService, mockOpenaiService]],
        });
        prisma = new PrismaClient();
    });

    afterEach(() => {
        createResponseSpy.mockClear();
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    it("Requires authentication", async () => {
        await request(URL_BASE).post("/chat").send({}).expect(401);
    });

    it("Sends and recieves messages", async () => {
        const session = await login();

        const chatResponse = await session
            .post("/chat")
            .send({
                messages: [
                    {
                        type: "text",
                        content: "Hello, world!",
                    },
                ],
            })
            .expect(201);

        expect(chatResponse.body).toMatchObject({
            messages: [
                {
                    type: "text",
                    content: "Hello yourself!",
                },
            ],
        });
    });

    it("Maintains conversation state", async () => {
        const session = await login();

        // Send an initial message
        const firstChatResponse = await session
            .post("/chat")
            .send({
                messages: [
                    {
                        type: "text",
                        content: "Hello, world!",
                    },
                ],
            })
            .expect(201);

        const responseId = firstChatResponse.body.messages[0].id;

        // Send a follow up message with the previous response ID
        await session
            .post("/chat")
            .send({
                previousResponseId: responseId,
                messages: [
                    {
                        type: "text",
                        content: "Hello, world!",
                    },
                ],
            })
            .expect(201);

        // Check that the previous response ID is used
        expect(createResponseSpy).toHaveBeenCalledWith(
            expect.objectContaining({
                previous_response_id: responseId,
            }),
        );
    });

    describe("Search tool", () => {
        let postId: string;

        beforeEach(async () => {
            // Create a reddit post
            postId = randomUUID();
            await prisma.redditPost.create({
                data: {
                    id: postId,
                    title: "Test post",
                    text: "Test text",
                    publishedAt: new Date(),
                    subreddit: {
                        create: {
                            name: randomUUID(),
                            tenantId: "DEFAULT",
                        },
                    },
                    tenant: {
                        connect: {
                            id: "DEFAULT",
                        },
                    },
                },
            });
        });

        it("Is provided", async () => {
            const session = await login();

            await session.post("/chat").send({ messages: [] }).expect(201);

            expect(createResponseSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    tools: expect.arrayContaining([
                        expect.objectContaining({
                            type: "function",
                            name: "search",
                        }),
                    ]),
                    stream: false,
                }),
            );
        });

        it("Handles function call", async () => {
            const session = await login();

            // Mock the search tool function call
            createResponseSpy.mockResolvedValueOnce({
                output: [
                    {
                        call_id: "123",
                        type: "function_call",
                        name: "search_similar",
                        parsed_arguments: {
                            source: "REDDIT_POST",
                            id: postId,
                        },
                        arguments: JSON.stringify({
                            source: "REDDIT_POST",
                            id: postId,
                        }),
                    },
                ],
            } as ParsedResponse<unknown>);

            // Mock the second pass repsonse
            createResponseSpy.mockResolvedValueOnce({
                output_text: "Here is one simliar post ...",
            } as ParsedResponse<unknown>);

            // Send the request
            const response = await session
                .post("/chat")
                .send({
                    messages: [
                        {
                            type: "text",
                            content:
                                "What other posts are similar to this one?",
                        },
                        {
                            type: "REDDIT_POST",
                            content: postId,
                        },
                    ],
                })
                .expect(201);

            expect(response.body).toMatchObject({
                messages: [
                    {
                        type: "text",
                        content: "Here is one simliar post ...",
                    },
                ],
            });
        });
    });
});
