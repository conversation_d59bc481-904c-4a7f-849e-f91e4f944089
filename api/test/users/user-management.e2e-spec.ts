import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import * as supertest from "supertest";

import { URL_BASE } from "../constants";
import {
    createAndOnboardNewUser,
    createNewUser,
    login,
    loginAsAdmin,
    createApp,
    stopApp,
} from "../util";

describe("Users", () => {
    let app: INestApplication;
    let prisma: PrismaClient;
    const groups: number[] = [];

    beforeAll(async () => {
        app = await createApp();

        prisma = new PrismaClient();

        // Ensure there are some groups that we can add users to
        const adminSession = await loginAsAdmin(URL_BASE);

        const firstGroupResponse = await adminSession
            .post("/groups")
            .send({ name: `group-${randomUUID()}` })
            .expect(201);

        const secondGroupResponse = await adminSession
            .post("/groups")
            .send({ name: `group-${randomUUID()}` })
            .expect(201);

        groups.push(firstGroupResponse.body.id);
        groups.push(secondGroupResponse.body.id);
    });

    afterAll(async () => {
        await stopApp(app);
        await prisma.$disconnect();
    });

    it("GET /users/me 401", async () => {
        const request = supertest.agent(URL_BASE);
        await request.get("/users/me").expect(401);
    });

    it(`GET /users/me`, async () => {
        const { userId, email, password } = await createAndOnboardNewUser();

        // Login
        const request = supertest.agent(URL_BASE);
        await request
            .post("/login")
            .send({ username: email, password })
            .expect(204);

        // Response matches newly created user
        const usersMeResponse = await request.get("/users/me").expect(200);

        expect(usersMeResponse.body).toMatchObject({
            id: userId,
            email,
            firstName: "First",
            lastName: "Last",
            isAdmin: false,
            groups: [],
            tenants: expect.arrayContaining([
                expect.objectContaining({
                    id: expect.any(String),
                    name: expect.any(String),
                }),
            ]),
        });
    });

    test("GET /users 200", async () => {
        const request = await loginAsAdmin(URL_BASE);
        const response = await request.get("/users").expect(200);
        const paginatedUsersResponse = response.body;

        expect(paginatedUsersResponse.items.length).toBeGreaterThan(0);
        expect(paginatedUsersResponse.total).toBeGreaterThan(0);
        expect(paginatedUsersResponse.page).toEqual(1);
        expect(paginatedUsersResponse.size).toEqual(10);
        expect(paginatedUsersResponse.totalPages).toBeGreaterThan(0);
        expect(paginatedUsersResponse.items[0]).toMatchObject({
            id: expect.any(String),
            email: expect.any(String),
            firstName: expect.any(String),
            lastName: expect.any(String),
            isAdmin: expect.any(Boolean),
            groups: expect.any(Array),
        });

        // Check that the last page exists
        const lastPage = paginatedUsersResponse.totalPages;
        await request.get(`/users?page=${lastPage}`).expect(200);

        // Check that there is no more data after the last page
        await request.get(`/users?page=${lastPage + 1}`).expect(404);
    });

    it("GET /users?roles=admin", async () => {
        const request = await loginAsAdmin(URL_BASE);

        const usersResponse = await request
            .get("/users?roles=admin")
            .expect(200);
        const users = usersResponse.body.items;

        expect(users.length).toBeGreaterThanOrEqual(1);
        for (const user of users) {
            expect(user.isAdmin).toBeTruthy();
        }
    });

    it("POST /users 401", async () => {
        const request = supertest.agent(URL_BASE);

        // Try to create a user without authenticating
        await request.post("/users").expect(401);
    });

    it("POST /users 403", async () => {
        const request = await login(URL_BASE);

        // Try to create user as a non-admin
        await request.post("/users").expect(403);
    });

    it("POST /users 400", async () => {
        const request = await loginAsAdmin(URL_BASE);

        // Try to create a user with missing details
        await request
            .post("/users")
            .set({ origin: URL_BASE })
            .send({})
            .expect(400);
    });

    it("POST /users 409", async () => {
        const request = await loginAsAdmin(URL_BASE);

        const email = `user-${randomUUID()}@example.com`;
        const body = { firstName: "First", email };

        // Create a new user
        await request
            .post("/users")
            .set({ origin: URL_BASE })
            .send(body)
            .expect(201);

        // Try to create a duplicate user
        await request
            .post("/users")
            .set({ origin: URL_BASE })
            .send(body)
            .expect(409);
    });

    it("POST /users 201", async () => {
        const request = await loginAsAdmin(URL_BASE);

        const email = `user-${randomUUID()}@example.com`;
        const firstName = "Firsty";
        const lastName = "Lasterson";

        // Create a new user
        await request
            .post("/users")
            .set({ origin: URL_BASE })
            .send({
                firstName,
                lastName,
                email,
                groups: [...groups],
            })
            .expect(201);

        // Check that the user was persisted
        const response = await request.get("/users");
        const users = response.body.items;
        const user = users.find((user) => user["email"] === email);

        expect(user["email"]).toEqual(email);
        expect(user["firstName"]).toEqual(firstName);
        expect(user["lastName"]).toEqual(lastName);
        expect(user["isAdmin"]).toEqual(false);
        expect(user["groups"].length).toEqual(2);

        const userGroups = user["groups"].map(({ id }) => id);
        expect(userGroups).toContain(groups[0]);
        expect(userGroups).toContain(groups[1]);
    });

    it("POST /users 201 - create a new admin", async () => {
        const request = await loginAsAdmin(URL_BASE);

        const email = `user-${randomUUID()}@example.com`;
        const firstName = "Fir";
        const lastName = "Lazt";

        // Create a new user
        await request
            .post("/users")
            .set({ origin: URL_BASE })
            .send({
                firstName,
                lastName,
                email,
                isAdmin: true,
            })
            .expect(201);

        // Check that the user was persisted
        const response = await request.get("/users");
        const users = response.body.items;
        const user = users.find((user) => user["email"] === email);

        expect(user).toMatchObject({
            email,
            firstName,
            lastName,
            isAdmin: true,
            groups: [],
        });
    });

    it("DELETE /users 403 - as a non-admin", async () => {
        // Create a new user
        const firstUserSession = await login(URL_BASE);
        const firstUser = await firstUserSession
            .get("/users/me")
            .expect(200)
            .then((response) => response.body);

        // Try to delete the user
        const secondUserSession = await login(URL_BASE);
        await secondUserSession
            .delete("/users")
            .send({ userIds: [firstUser.id] })
            .expect(403);

        // User is still logged in
        await firstUserSession.get("/users/me").expect(200);
    });

    it("DELETE /users 403 - deleting self", async () => {
        // Login as admin
        const adminSession = await loginAsAdmin(URL_BASE);

        // Get account ID
        const meResponse = await adminSession.get("/users/me").expect(200);
        const adminId = meResponse.body.id;

        // Try to delete self
        await adminSession
            .delete("/users")
            .send({ userIds: [adminId] })
            .expect(403);
    });

    it("DELETE /users 204", async () => {
        // Create a new user
        const { userId, email, password } =
            await createAndOnboardNewUser(URL_BASE);

        // Log in as the new user
        const userSession = await supertest.agent(URL_BASE);
        await userSession
            .post("/login")
            .send({ username: email, password })
            .expect(204);

        // Login as admin
        const adminSession = await loginAsAdmin(URL_BASE);

        // Delete the user
        await adminSession
            .delete("/users")
            .send({ userIds: [userId] })
            .expect(204);

        // User no longer exists
        await userSession.get("/users/me").expect(404);

        // Refresh token is invalidated
        await userSession.post("/refresh").expect(401);

        // User cannot log in
        await userSession
            .post("/login")
            .send({ username: email, password })
            .expect(401);

        // User does not appear in the list of users
        const response = await adminSession.get("/users").expect(200);
        const users = response.body.items;
        const emails = users.map((user) => user["email"]);

        expect(emails).not.toContain(email);

        // Deleted record is persisted as JSON to "DeletedEntity" table
        const deletedEntity = await prisma.deletedEntity.findFirst({
            select: {
                data: true,
            },
            where: {
                model: "User",
                modelId: userId,
            },
        });

        expect(deletedEntity.data["email"]).toEqual(email);
    });

    it("DELETE /users 404", async () => {
        // Login as admin
        const adminSession = await loginAsAdmin(URL_BASE);

        // Delete a non-existant user
        await adminSession
            .delete("/users/")
            .send({ userIds: ["does-not-exist"] })
            .expect(404);
    });

    it("PUT user 403", async () => {
        const newUser = await createNewUser(URL_BASE);

        const request = await login();
        await request
            .put(`/users/${newUser.userId}`)
            .send({ firstName: "New", email: "<EMAIL>" })
            .expect(403);
    });

    it("PUT user 404", async () => {
        const adminSession = await loginAsAdmin(URL_BASE);

        await adminSession
            .put(`/users/does-not-exist`)
            .send({
                firstName: "",
                lastName: "",
                email: "<EMAIL>",
                isAdmin: false,
            })
            .expect(404);
    });

    it("PUT user 400", async () => {
        const adminSession = await loginAsAdmin(URL_BASE);

        const newUser = await createNewUser(URL_BASE);

        await adminSession.put(`/users/${newUser.userId}`).send({}).expect(400);
    });

    it("PUT user 200", async () => {
        const adminSession = await loginAsAdmin(URL_BASE);

        const userSession = await login(URL_BASE);

        const user = await userSession
            .get("/users/me")
            .expect(200)
            .then(({ body }) => body);

        const newEmail = `new-${randomUUID()}@example.com`;

        await adminSession
            .put(`/users/${user.id}`)
            .send({
                firstName: "New",
                lastName: "Newson",
                email: newEmail,
                isAdmin: true,
                groups,
            })
            .expect(200);

        // Changes are persisted
        const updatedUser = await userSession
            .get("/users/me")
            .expect(200)
            .then(({ body }) => body);

        expect(updatedUser["email"]).toEqual(newEmail);
        expect(updatedUser["firstName"]).toEqual("New");
        expect(updatedUser["lastName"]).toEqual("Newson");
        expect(updatedUser["isAdmin"]).toEqual(true);
        expect(updatedUser["groups"].length).toEqual(2);

        const userGroups = updatedUser["groups"].map(({ id }) => id);
        expect(userGroups).toContain(groups[0]);
        expect(userGroups).toContain(groups[1]);
    });

    it("PUT user 409 - conflicting email", async () => {
        const adminSession = await loginAsAdmin(URL_BASE);

        const userSession = await login(URL_BASE);

        const user = await userSession
            .get("/users/me")
            .expect(200)
            .then(({ body }) => body);

        const secondUser = await createNewUser(URL_BASE);

        // Update fails with HTTP 409
        await adminSession
            .put(`/users/${user.id}`)
            .send({
                firstName: "New",
                lastName: "Newson",
                email: secondUser.email,
                isAdmin: false,
            })
            .expect(409);

        // User is unchanged
        const updatedUser = await userSession
            .get("/users/me")
            .expect(200)
            .then(({ body }) => body);

        expect(updatedUser).toMatchObject({
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            isAdmin: false,
        });
    });
});
