import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";

import { createApp, loginAsAdmin, loginAsSuperAdmin, stopApp } from "../util";

describe("Tenants", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await createApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    describe("POST /tenants", () => {
        it("returns 403 for non-super admin", async () => {
            const request = await loginAsAdmin();
            await request.post("/tenants").expect(403);
        });

        it("Creates tenant with default topic", async () => {
            const request = await loginAsSuperAdmin();

            // Create a tenant
            const tenantName = `test-tenant-${randomUUID()}`;
            const tenant = await request
                .post("/tenants")
                .send({ name: tenantName })
                .expect(201);
            const tenantId = tenant.body.id;

            // Set session tenant
            await request.put("/session-tenant").send({ tenantId }).expect(204);

            // Check that the default topic is created
            const topicResponse = await request.get("/topics").expect(200);

            expect(topicResponse.body).toMatchObject({
                items: expect.arrayContaining([
                    expect.objectContaining({
                        name: tenantName,
                    }),
                ]),
            });
        });
    });

    describe("GET /tenants", () => {
        it("returns 403 for non-super admin", async () => {
            const request = await loginAsAdmin();
            await request.get("/tenants").expect(403);
        });

        it("returns tenants", async () => {
            const request = await loginAsSuperAdmin();

            // Create a tenant
            const tenantId = randomUUID();
            const tenantName = `test-tenant-${tenantId}`;
            await request
                .post("/tenants")
                .send({ name: tenantName, id: tenantId })
                .expect(201);

            // Get tenants
            const tenantsResponse = await request
                .get("/tenants?size=100")
                .expect(200);

            expect(tenantsResponse.body).toMatchObject({
                items: expect.arrayContaining([
                    expect.objectContaining({
                        name: tenantName,
                        id: tenantId,
                    }),
                ]),
                total: expect.any(Number),
            });
        });
    });

    describe("GET /tenants/:id/users", () => {
        it("returns 403 for non-super admin", async () => {
            const request = await loginAsAdmin();
            await request.get("/tenants/123/users").expect(403);
        });

        it("returns tenant users", async () => {
            const request = await loginAsSuperAdmin();

            // Create a tenant
            const tenantName = `test-tenant-${randomUUID()}`;
            const tenant = await request
                .post("/tenants")
                .send({ name: tenantName })
                .expect(201);
            const tenantId = tenant.body.id;

            // Get tenant
            const tenantResponse = await request
                .get(`/tenants/${tenantId}/users`)
                .expect(200);

            // Check the super-admin is added to the tenant
            expect(tenantResponse.body).toMatchObject({
                items: expect.arrayContaining([
                    expect.objectContaining({
                        email: process.env.ADMIN_EMAIL,
                    }),
                ]),
                total: 1,
            });
        });
    });
});
