-- CreateTable
CREATE TABLE "public"."TruthkeepAccess" (
    "path" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 1,
    "userId" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "TruthkeepAccess_userId_path_date_key" ON "public"."TruthkeepAccess"("userId", "path", "date");

-- AddForeignKey
ALTER TABLE "public"."TruthkeepAccess" ADD CONSTRAINT "TruthkeepAccess_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
