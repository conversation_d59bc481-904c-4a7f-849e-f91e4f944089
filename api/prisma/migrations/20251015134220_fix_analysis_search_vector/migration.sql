-- The previous migration added "Analysis"."searchVector" as a generated column, however it does not work well with Prisma.
-- Instead we treat it as a regular column and update it with a trigger.

ALTER TABLE "public"."Analysis" ALTER COLUMN "searchVector" DROP EXPRESSION;

CREATE OR REPLACE FUNCTION analysis_search_vector_update() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  NEW."searchVector" :=
    to_tsvector(
      'english',
      coalesce(NEW."summary", '') || ' ' ||
      coalesce(NEW."longSummary", '') || ' ' ||
      coalesce(NEW."tipsAndActions", '')
    );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER analysis_search_vector_trigger
BEFORE INSERT OR UPDATE ON "Analysis"
FOR EACH ROW
EXECUTE FUNCTION analysis_search_vector_update();
