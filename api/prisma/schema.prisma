generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions", "views", "fullTextSearchPostgres"]
  binaryTargets   = ["native", "debian-openssl-1.1.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider   = "postgres"
  url        = env("DATABASE_URL")
  extensions = [vector]
}

model DeletedEntity {
  id        Int      @id @default(autoincrement())
  model     String
  modelId   String
  deletedAt DateTime @default(now())
  data      Json
}

model Tenant {
  id        String   @id @default(cuid(2))
  name      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  users         User[]
  topics        Topic[]
  analyses      Analysis[]
  groups        Group[]
  tags          Tag[]
  narratives    Narrative[]
  timelineItems TimelineItem[]

  youtubeChannels YoutubeChannel[]
  youtubeVideos   YoutubeVideo[]
  youtubeComments YoutubeComment[]

  subreddits     Subreddit[]
  redditPosts    RedditPost[]
  redditComments RedditComment[]

  salesforceSites SalesforceSite[]

  allAboutCircuitsForums AllAboutCircuitsForum[]
  SearchableEntity       SearchableEntity[]
}

model User {
  id                String                @id @default(cuid(2))
  username          String                @unique
  email             String                @unique
  passwordHash      String
  createdAt         DateTime              @default(now())
  onboardingStep    OnboardingStep        @default(COMPLETE)
  firstName         String                @default("")
  lastName          String                @default("")
  groups            GroupsOnUsers[]
  roles             RolesOnUsers[]
  shares            Share[]
  topics            Topic[]
  sessions          UserSession[]
  topicPreferences  UserTopicPreference[]
  UserVerifications UserVerification[]
  bookmarked        Analysis[]            @relation("AnalysisToUser")
  tenants           Tenant[]
  Tag               Tag[]
  groupPreferences  UserGroupPreference[]
  tagPreferences    UserTagPreference[]
  truthkeepAccesses TruthkeepAccess[]
}

model UserVerification {
  id        String    @id @default(cuid(2))
  userId    String
  code      String
  createdAt DateTime  @default(now())
  expiresAt DateTime
  usedAt    DateTime?
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, code])
}

model UserSession {
  id        String    @id @default(cuid(2))
  userId    String
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Role {
  id    String         @id @default(cuid(2))
  name  String         @unique
  users RolesOnUsers[]
}

model RolesOnUsers {
  userId     String
  roleId     String
  assignedAt DateTime @default(now())
  role       Role     @relation(fields: [roleId], references: [id])
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, roleId])
}

model Group {
  id          Int             @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  users       GroupsOnUsers[]
  shares      Share[]
  topics      Topic[]         @relation("GroupToTopic")
  tags        Tag[]           @relation("GroupToTag")

  tenantId            String                @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant              Tenant                @relation(fields: [tenantId], references: [id])
  userGroupPreference UserGroupPreference[]

  @@unique([tenantId, name])
}

model GroupsOnUsers {
  userId     String
  groupId    Int
  assignedAt DateTime @default(now())
  group      Group?   @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, groupId])
}

model YoutubeVideo {
  id            String
  title         String
  publishedAt   DateTime
  subtitles     String
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  commentCount  Int?
  favoriteCount Int?
  likeCount     Int?
  viewCount     Int?
  channelId     String?
  comments      YoutubeComment[]
  channel       YoutubeChannel?  @relation(fields: [tenantId, channelId], references: [tenantId, id])

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, id])
}

model RedditPost {
  id            String
  title         String
  text          String
  createdAt     DateTime        @default(now())
  updatedAt     DateTime?       @updatedAt
  commentCount  Int             @default(0)
  voteScore     Int             @default(0)
  subredditName String?
  publishedAt   DateTime        @default(now())
  comments      RedditComment[]
  subreddit     Subreddit?      @relation(fields: [tenantId, subredditName], references: [tenantId, name])

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, id])
}

model RequestCache {
  key       String   @unique
  value     String
  metadata  String
  createdAt DateTime @default(now())
}

model Analysis {
  id                  Int                   @id @default(autoincrement())
  sentiment           Int
  sentimentReasoning  String?
  summary             String
  relevance           Int?
  relevanceReasoning  String?
  longSummary         String?
  isActionable        Boolean               @default(false)
  actionableReasoning String?
  tipsAndActions      String?
  source              DataSource
  sourceId            String
  narratives          AnalysisToNarrative[]
  shares              Share[]
  bookmarkedBy        User[]                @relation("AnalysisToUser")
  timelineItem        TimelineItem[]

  searchVector Unsupported("tsvector")?

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, sourceId])
  @@index([searchVector], map: "idx_analysis_search_vector", type: Gin)
}

model AnalysisToNarrative {
  analysisId         Int
  narrativeId        Int
  sentiment          Int
  sentimentReasoning String?

  analysis  Analysis  @relation(fields: [analysisId], references: [id], onDelete: Cascade)
  narrative Narrative @relation(fields: [narrativeId], references: [id], onDelete: Cascade)

  @@id([analysisId, narrativeId])
}

model Tag {
  id              Int      @id @default(autoincrement())
  name            String
  description     String?
  isPersonal      Boolean  @default(false)
  createdByUserId String?
  createdBy       User?    @relation(fields: [createdByUserId], references: [id])
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  topics          Topic[]  @relation("TagToTopic")
  groups          Group[]  @relation("GroupToTag")

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // Uniqueness constraints depends on isPersonal value
  // See migrations/20250918164818_tag_is_personal/migration.sql
  userTagPreferences UserTagPreference[]
}

model Topic {
  id                  Int                    @id @default(autoincrement())
  name                String
  description         String?
  userId              String?
  createdBy           User?                  @relation(fields: [userId], references: [id])
  narratives          Narrative[]
  userTopicPreference UserTopicPreference[]
  groups              Group[]                @relation("GroupToTopic")
  tags                Tag[]                  @relation("TagToTopic")
  tenantId            String?                @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant              Tenant?                @relation(fields: [tenantId], references: [id])
  embedding           Unsupported("vector")?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, name])
}

model UserTopicPreference {
  topicId                        Int
  userId                         String
  notificationCadence            NotificationCadence @default(WEEKLY)
  highRelevanceNotifications     NotificationCadence @default(OFF)
  negativeSentimentNotifications NotificationCadence @default(OFF)
  isFavourite                    Boolean             @default(false)
  topic                          Topic               @relation(fields: [topicId], references: [id], onDelete: Cascade)
  user                           User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([topicId, userId])
}

model UserGroupPreference {
  userId                         String
  groupId                        Int
  notificationCadence            NotificationCadence @default(WEEKLY)
  highRelevanceNotifications     NotificationCadence @default(OFF)
  negativeSentimentNotifications NotificationCadence @default(OFF)

  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@unique([groupId, userId])
}

model UserTagPreference {
  userId                         String
  tagId                          Int
  notificationCadence            NotificationCadence @default(WEEKLY)
  highRelevanceNotifications     NotificationCadence @default(OFF)
  negativeSentimentNotifications NotificationCadence @default(OFF)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([tagId, userId])
}

model YoutubeChannel {
  id                String
  channelHandle     String         @unique
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  uploadsPlaylistId String
  videos            YoutubeVideo[]

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, id])
}

model Subreddit {
  name      String
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  posts     RedditPost[]

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, name])
}

model SalesforceSite {
  id        Int               @id @default(autoincrement())
  name      String
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  forums    SalesforceForum[]

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, name])
}

model SalesforceForum {
  id        Int      @id @default(autoincrement())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  salesforceSiteId Int
  site             SalesforceSite @relation(fields: [salesforceSiteId], references: [id], onDelete: Cascade)

  parentForumId Int?
  parentForum   SalesforceForum?  @relation("SubForum", fields: [parentForumId], references: [id])
  subForums     SalesforceForum[] @relation("SubForum")

  posts SalesforcePost[]

  @@unique([salesforceSiteId, name])
}

model SalesforcePost {
  id          String   @id
  title       String
  text        String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime
  viewCount   Int      @default(0)
  url         String

  memberLikeCount    Int @default(0)
  memberDislikeCount Int @default(0)
  guestLikeCount     Int @default(0)
  guestDislikeCount  Int @default(0)

  forumId Int
  forum   SalesforceForum       @relation(fields: [forumId], references: [id], onDelete: Cascade)
  replies SalesforcePostReply[]
}

model SalesforcePostReply {
  id             String   @id
  text           String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  publishedAt    DateTime
  name           String
  userScreenName String?

  memberLikeCount    Int @default(0)
  memberDislikeCount Int @default(0)
  guestLikeCount     Int @default(0)
  guestDislikeCount  Int @default(0)

  postId String
  post   SalesforcePost @relation(fields: [postId], references: [id], onDelete: Cascade)
}

model AllAboutCircuitsForum {
  id        Int      @id @default(autoincrement())
  name      String
  slug      String
  url       String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  posts    AllAboutCircuitsPost[]
  tenant   Tenant                 @relation(fields: [tenantId], references: [id])
  tenantId String                 @default(dbgenerated("current_setting('app.tenant_id'::text)"))

  @@unique([slug])
}

model AllAboutCircuitsPost {
  id          String                      @id
  author      String
  title       String
  text        String
  createdAt   DateTime                    @default(now())
  updatedAt   DateTime                    @updatedAt
  publishedAt DateTime
  viewCount   Int                         @default(0)
  replyCount  Int                         @default(0)
  url         String
  forumId     Int
  forum       AllAboutCircuitsForum       @relation(fields: [forumId], references: [id], onDelete: Cascade)
  replies     AllAboutCircuitsPostReply[]
}

model AllAboutCircuitsPostReply {
  id          String               @id
  author      String
  text        String
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  publishedAt DateTime
  postId      String
  url         String
  post        AllAboutCircuitsPost @relation(fields: [postId], references: [id], onDelete: Cascade)
}

model RedditComment {
  id          String
  body        String
  score       Int?
  parentId    String?
  postId      String
  replyCount  Int?
  publishedAt DateTime?

  tenantId   String          @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant     Tenant          @relation(fields: [tenantId], references: [id])
  parent     RedditComment?  @relation("RedditCommentToRedditComment", fields: [tenantId, parentId], references: [tenantId, id])
  replies    RedditComment[] @relation("RedditCommentToRedditComment")
  redditPost RedditPost      @relation(fields: [tenantId, postId], references: [tenantId, id], onDelete: Cascade)

  @@id([tenantId, id])
}

model YoutubeComment {
  id           String
  body         String
  score        Int?
  parentId     String?
  videoId      String
  replyCount   Int?
  publishedAt  DateTime?
  parent       YoutubeComment?  @relation("YoutubeCommentToReplies", fields: [tenantId, parentId], references: [tenantId, id])
  replies      YoutubeComment[] @relation("YoutubeCommentToReplies")
  youtubeVideo YoutubeVideo     @relation(fields: [tenantId, videoId], references: [tenantId, id], onDelete: Cascade)

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, id])
}

model SearchableEntity {
  id           Int                      @id @default(autoincrement())
  source       DataSource
  sourceId     String
  embedding    Unsupported("vector")
  searchVector Unsupported("tsvector")?
  createdAt    DateTime                 @default(now())

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, source, sourceId])
  @@index([embedding], map: "SearchableEntity_embedding_idx")
}

model Narrative {
  id        Int                   @id @default(autoincrement())
  aspect    NarrativeAspect?
  topicId   Int?
  summary   String
  createdAt DateTime              @default(now())
  updatedAt DateTime              @updatedAt
  analyses  AnalysisToNarrative[]
  topic     Topic?                @relation(fields: [topicId], references: [id])

  tenantId String @default(dbgenerated("current_setting('app.tenant_id'::text)"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])
}

model Share {
  groupId    Int
  userId     String
  createdAt  DateTime @default(now())
  analysisId Int
  analysis   Analysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)
  group      Group    @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([analysisId, groupId, userId])
}

model TruthkeepAccess {
  path  String
  date  DateTime
  count Int      @default(1)

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, path, date])
}

view TimelineItem {
  analysisId   Int
  source       DataSource
  sourceId     String
  likeCount    Int?
  commentCount Int?
  publishedAt  DateTime?

  analysis Analysis @relation(fields: [analysisId], references: [id])

  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  @@unique([analysisId])
  @@unique([tenantId, source, sourceId])
}

enum OnboardingStep {
  PASSWORD_CREATION
  COMPLETE
}

enum NotificationCadence {
  DAILY
  WEEKLY
  MONTHLY
  OFF
}

enum DataSource {
  REDDIT_POST
  YOUTUBE_VIDEO
  REDDIT_COMMENT
  YOUTUBE_COMMENT
  AVR_FREAKS
  MICROCHIP_CLASSIC
  ALL_ABOUT_CIRCUITS
}

enum NarrativeAspect {
  LEARNING_CURVE
  EASE_OF_DEBUGGING
  EASE_OF_USE
  EASE_OF_INTEGRATION
  DOCUMENTATION_QUALITY
  PRICING
  PERFORMANCE
  CUSTOMER_SUPPORT
  BUSINESS_PRACTICES
  FEATURE_REQUEST
  BUG
  UNKNOWN
}
