import { BadRequestException, Injectable } from "@nestjs/common";

import { Prisma } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { BulkPatchPreferenceRequestDto } from "../common/dto/preference.dto";
import { PrismaService } from "../prisma/prisma.service";
import { CreateGroupRequestDto } from "./dto/create-group.dto";
import { GroupFilterParams } from "./dto/get-groups.dto";
import { UpdateGroupRequestDto } from "./dto/update-group.dto";

@Injectable()
export class GroupsService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly cls: ClsService,
    ) {}

    async getGroups(
        take: number,
        skip: number,
        filterParams: GroupFilterParams,
        userId: string,
    ) {
        const { topicIds, tagIds, myTeamsOnly } = filterParams;

        const topicFilter: Prisma.GroupWhereInput =
            topicIds?.length > 0
                ? { topics: { some: { id: { in: topicIds } } } }
                : {};

        const tagFilter: Prisma.GroupWhereInput =
            tagIds?.length > 0
                ? {
                      tags: { some: { id: { in: tagIds } } },
                  }
                : {};

        const myTeamsFilter: Prisma.GroupWhereInput = myTeamsOnly
            ? { users: { some: { userId } } }
            : {};

        const sqlFilters: Prisma.GroupWhereInput = {
            ...topicFilter,
            ...tagFilter,
            ...myTeamsFilter,
        };

        const groups = await this.prisma.group.findMany({
            where: sqlFilters,
            include: {
                _count: {
                    select: {
                        users: true,
                    },
                },
                users: {
                    where: {
                        userId,
                    },
                },
                tags: {
                    where: {
                        OR: [
                            {
                                isPersonal: false,
                            },
                            {
                                createdByUserId: userId,
                            },
                        ],
                    },
                    select: {
                        id: true,
                        name: true,
                    },
                },
                topics: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                userGroupPreference: {
                    where: {
                        userId,
                    },
                },
            },
            orderBy: { updatedAt: "desc" },
            take,
            skip,
        });

        const formattedGroups = groups.map((group) => ({
            id: group.id,
            name: group.name,
            description: group.description,
            tags: group.tags,
            topics: group.topics,
            isUserMember: group.users.length > 0,
            memberCount: group._count.users,
            userGroupPreference: group.userGroupPreference[0],
        }));

        const total = await this.prisma.group.count({ where: sqlFilters });

        return { items: formattedGroups, total };
    }

    async createGroup(createGroupRequestDto: CreateGroupRequestDto) {
        const { name, description, tags } = createGroupRequestDto;

        await this.assertNoPersonalTags(tags);

        return this.prisma.group.create({
            data: {
                name,
                description,
                tags: {
                    connect: tags?.map((tagId) => ({
                        id: tagId,
                    })),
                },
                tenant: { connect: { id: this.cls.get("tenantId") } },
            },
        });
    }

    async addUserToGroups(userId: string, groupIds: number[] = []) {
        const data = groupIds.map((groupId) => ({
            groupId,
            userId,
        }));

        await this.prisma.groupsOnUsers.createMany({
            data,
        });
    }

    async removeUserFromGroups(userId: string, groupIds: number[] = []) {
        await this.prisma.groupsOnUsers.deleteMany({
            where: {
                userId,
                groupId: { in: groupIds },
            },
        });
    }

    async setUserGroups(userId: string, groupIds: number[] = []) {
        await this.prisma.groupsOnUsers.deleteMany({
            where: {
                userId,
            },
        });

        await this.addUserToGroups(userId, groupIds);
    }

    async updateGroup(
        groupId: number,
        updateGroupRequestDto: UpdateGroupRequestDto,
        userId: string,
    ) {
        const { name, description, tags = [] } = updateGroupRequestDto;

        await this.assertNoPersonalTags(tags);

        // TODO - put this all in a transaction

        // Delete all tag associations for this group, except other users' personal tags
        await this.prisma.$executeRaw`
            DELETE FROM "_GroupToTag"
            WHERE "A" = ${groupId}
            AND "B" NOT IN (
                SELECT id FROM "Tag" WHERE "isPersonal" = true AND "createdByUserId" != ${userId}
            )
        `;

        // Set tag associations for this group
        if (tags.length > 0) {
            await this.prisma.$executeRaw`
                INSERT INTO "_GroupToTag" ("A", "B")
                VALUES ${Prisma.join(
                    tags.map((tagId) => Prisma.sql`(${groupId}, ${tagId})`),
                    ",",
                )}
            `;
        }

        await this.prisma.group.update({
            where: { id: groupId },
            data: {
                name,
                description,
            },
        });
    }

    async bulkPatch(
        bulkPatchRequestDto: BulkPatchPreferenceRequestDto,
        userId: string,
    ) {
        for (const item of bulkPatchRequestDto.items) {
            await this.prisma.userGroupPreference.upsert({
                where: { groupId_userId: { groupId: item.id, userId } },
                create: { ...item.patch, userId, groupId: item.id },
                update: item.patch,
            });
        }
    }

    async deleteGroup(id: number) {
        await this.prisma.group.delete({ where: { id } });
    }

    private async assertNoPersonalTags(tags: number[]) {
        if (!tags?.length) return;

        const includesPersonalTags = await this.prisma.tag.count({
            where: { id: { in: tags }, isPersonal: true },
        });

        if (includesPersonalTags) {
            throw new BadRequestException(
                "Personal tags cannot be added to a group",
            );
        }
    }
}
