import { IntersectionType } from "@nestjs/swagger";

import { IsNumber } from "class-validator";

import {
    MyTeamsFilteredParams,
    MyTeamsFilteredRequestDto,
    TagFilteredParams,
    TagFilteredRequestDto,
    TopicFilteredParams,
    TopicFilteredRequestDto,
} from "../../common/dto/filtered-request.dto";
import {
    PaginatedRequestDto,
    PaginatedResponseDto,
} from "../../generic/dto/paginated.dto";

export class GroupDto {
    id: number;
    name: string;
    description: string;
    isUserMember?: boolean = false;
    memberCount?: number = 0;
}

export type GroupFilterParams = TopicFilteredParams &
    TagFilteredParams &
    MyTeamsFilteredParams;

export class GetGroupsRequestDto
    extends IntersectionType(
        PaginatedRequestDto,
        TopicFilteredRequestDto,
        TagFilteredRequestDto,
        MyTeamsFilteredRequestDto,
    )
    implements GroupFilterParams
{
    @IsNumber()
    size: number = 500;
}

export class GetGroupsResponseDto extends PaginatedResponseDto<GroupDto> {}
