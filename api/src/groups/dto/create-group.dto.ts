import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { Transform } from "class-transformer";
import { IsArray, IsOptional, IsString } from "class-validator";
import { randomUUID } from "crypto";

const exampleGroupName = `group-${randomUUID()}`;

export class CreateGroupRequestDto {
    @IsString()
    @ApiProperty({ example: exampleGroupName })
    name: string;

    @IsString()
    @IsOptional()
    @ApiProperty({ example: "This is a group for any user" })
    description: string;

    @IsArray()
    @IsOptional()
    @ApiPropertyOptional({ example: [1, 2], isArray: true, type: Number })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    tags?: number[];
}
