import { Job as BullMqJob, Queue } from "bullmq";

import { SalesforceSiteName } from "../salesforce/types";

export type Job<JobData> = BullMqJob<
    JobData & {
        tenantId: string;
    }
>;

export type TimelineRefreshJob = Job<{
    workflowKey?: string;
    channelHandles?: string[];
    subredditNames?: string[];
    salesforceForums?: {
        siteName: SalesforceSiteName;
        forumNames: string[];
    }[];
    subredditJobParams?: Omit<RedditSubredditJob["data"], "subredditName">;
    youtubeChannelJobParams?: Omit<YoutubeChannelJob["data"], "channelHandle">;
    salesforceForumJobParams?: Omit<
        SalesforceForumJob["data"],
        "siteName" | "forumName"
    >;
    allAboutCircuitsJobParams?: AllAboutCircuitsJob["data"];
}>;

export type RedditSubredditJob = Job<{
    subredditName: string;
    limit?: number;
    sort?: "new" | "top";
    t?: "hour" | "day" | "week" | "month" | "year" | "all";
}>;

export type RedditPostJob = Job<{
    postId: string;
    subredditName: string;
    postURL: string;
}>;

export type RedditRefreshJob = Job<unknown>;

export type RedditPostRefreshJob = Job<{
    postId: string;
    postTitle: string;
    subredditName: string;
}>;

export type YoutubeChannelJob = Job<{
    channelHandle: string;
    maxResults?: number;
}>;

export type YoutubeVideoJob = Job<{
    youtubeId: string;
    videoURL: string;
    channelHandle: string;
}>;

export type YoutubeRefreshJob = Job<unknown>;

export type YoutubeVideoRefreshJob = Job<{
    youtubeId: string;
    title: string;
    channelHandle: string;
}>;

export type SalesforceForumJob = Job<{
    forumName: string;
    siteName: SalesforceSiteName;
    dateAfter?: string;
    dateBefore?: string;
    limit?: number;
}>;

export type SalesforceForumPostJob = Job<{
    forumName: string;
    siteName: SalesforceSiteName;
    topicTitle: string;
    topicId: string;
    topicURL: string;
}>;

export type SalesforceRefreshJob = Job<unknown>;

export type SalesforceForumPostRefreshJob = Job<{
    forumName: string;
    siteName: SalesforceSiteName;
    postId: string;
    postURL: string;
}>;

export type AllAboutCircuitsJob = Job<{
    dateFrom?: string;
}>;

export type AIAnalysisRedditJob = Job<{
    type: "reddit";
    postId: string;
    subredditName: string;
    postURL: string;
}>;

export type AIAnalysisYoutubeJob = Job<{
    type: "youtube";
    youtubeId: string;
    videoURL: string;
    channelHandle: string;
}>;

export type AIAnalysisSalesforceJob = Job<{
    type: "salesforce";
    forumName: string;
    siteName: SalesforceSiteName;
    topicId: string;
    topicURL: string;
}>;

export type AIAnalysisAllAboutCircuitsJob = Job<{
    type: "allAboutCircuits";
    forumName: string;
    postId: string;
    postURL: string;
}>;

export type NarrativeGenerationJob = Job<unknown>;

export type AIAnalysisJob =
    | AIAnalysisRedditJob
    | AIAnalysisYoutubeJob
    | AIAnalysisSalesforceJob
    | AIAnalysisAllAboutCircuitsJob;

export type HistoricalAnalysisJob = Job<{ topidId: number }>;

export type TimelineRefreshQueue = Queue<TimelineRefreshJob>;

export type RedditSubredditQueue = Queue<RedditSubredditJob>;
export type RedditPostQueue = Queue<RedditPostJob>;
export type RedditRefreshQueue = Queue<RedditRefreshJob>;
export type RedditPostRefreshQueue = Queue<RedditPostRefreshJob>;

export type YoutubeChannelQueue = Queue<YoutubeChannelJob>;
export type YoutubeVideoQueue = Queue<YoutubeVideoJob>;
export type YoutubeRefreshQueue = Queue<YoutubeRefreshJob>;
export type YoutubeVideoRefreshQueue = Queue<YoutubeVideoRefreshJob>;

export type SalesforceForumQueue = Queue<SalesforceForumJob>;
export type SalesforceForumPostQueue = Queue<SalesforceForumPostJob>;
export type SalesforceRefreshQueue = Queue<SalesforceRefreshJob>;
export type SalesforceForumPostRefreshQueue =
    Queue<SalesforceForumPostRefreshJob>;

export type AllAboutCircuitsQueue = Queue<AllAboutCircuitsJob>;

export type AIAnalysisQueue = Queue<AIAnalysisJob>;
export type HistoricalAnalysisQueue = Queue<HistoricalAnalysisJob>;

export type NarrativeGenerationQueue = Queue<NarrativeGenerationJob>;
