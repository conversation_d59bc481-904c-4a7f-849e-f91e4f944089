import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from "@nestjs/common";

import { format } from "@fast-csv/format";
import { Request, Response } from "express";
import { Observable } from "rxjs";

import { PaginatedResponseDto } from "../../generic/dto/paginated.dto";

export class CSVResponseInterceptor<T, U = T> implements NestInterceptor {
    constructor(
        private readonly headers: (keyof U)[],
        private readonly filename: string = "data.csv",
        private readonly transform?: (entity: T) => U,
    ) {}

    intercept(
        context: ExecutionContext,
        next: CallHandler<PaginatedResponseDto<T>>,
    ): Observable<string | PaginatedResponseDto<T>> {
        const req: Request = context.switchToHttp().getRequest();
        const res: Response = context.switchToHttp().getResponse();

        const isCSVRequest = req.url.includes(".csv");
        if (!isCSVRequest) return next.handle();

        res.setHeader("Content-Type", "text/plain; charset=utf-8");
        res.setHeader(
            "Content-Disposition",
            `inline; filename=${this.filename}`,
        );

        return new Observable((subscriber) => {
            const subscription = next.handle().subscribe({
                next: (response: PaginatedResponseDto<T>) => {
                    // Create CSV stream with proper formatting
                    const csvStream = format({
                        headers: this.headers as string[],
                        writeHeaders: true,
                    });

                    // Pipe CSV stream to response
                    csvStream.pipe(res);

                    // Handle stream errors
                    csvStream.on("error", (err) => {
                        res.status(500).end(
                            `Error generating CSV: ${err.message}`,
                        );
                        subscriber.error(err);
                    });

                    // Wait for response to finish sending
                    res.on("finish", () => {
                        subscriber.next();
                        subscriber.complete();
                    });

                    // Write rows
                    const items = response.items ?? [];
                    for (const item of items) {
                        const transformedItem: U = this.transform
                            ? this.transform(item)
                            : (item as unknown as U);
                        const row: string[] = [];
                        for (const header of this.headers) {
                            const value = transformedItem[header];
                            const formattedValue =
                                typeof value === "string"
                                    ? value
                                    : value instanceof Date
                                      ? value.toISOString().slice(0, 10)
                                      : value?.toString();
                            row.push(formattedValue);
                        }
                        csvStream.write(row);
                    }

                    // End the CSV stream (this will automatically end the response via pipe)
                    csvStream.end();
                },
                error: (err) => {
                    res.status(500).end(`Error generating CSV: ${err.message}`);
                    subscriber.error(err);
                },
            });

            return () => subscription.unsubscribe();
        });
    }
}
