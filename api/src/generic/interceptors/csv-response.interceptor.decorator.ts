import { applyDecorators, UseInterceptors } from "@nestjs/common";

import { CSVResponseInterceptor } from "./csv-response.interceptor";

export interface CSVExportOptions<T, U = T> {
    filename?: string;
    headers: (keyof U)[];
    transform?: (value: T) => U;
}

export const CSVExportable = <T, U = T>(
    options: CSVExportOptions<T, U>,
): MethodDecorator =>
    applyDecorators(
        UseInterceptors(
            new CSVResponseInterceptor<T, U>(
                options.headers,
                options.filename,
                options.transform,
            ),
        ),
    );
