import { CacheTTL } from "@nestjs/cache-manager";
import {
    Controller,
    Get,
    Param,
    Query,
    UseInterceptors,
    UsePipes,
} from "@nestjs/common";
import {
    ApiOperation,
    ApiProduces,
    ApiResponse,
    ApiTags,
} from "@nestjs/swagger";

import { GroupFilterPipe } from "../common/pipes/group-filter.pipe";
import { PaginatedResponseDto } from "../generic/dto/paginated.dto";
import { CSVExportable } from "../generic/interceptors/csv-response.interceptor.decorator";
import { HttpCacheInterceptor } from "../http-cache/http-cache.interceptor";
import { AnalyticsService } from "./analytics.service";
import {
    AnalyticsRequestDto,
    NarrativeAnalyticsDto,
    NarrativeAspectTrendsComparisonDto,
    SourceAnalyticsDto,
    TenantAnalyticsRequestDto,
    TopicAnalyticsDto,
    TopicScatterPlotDto,
} from "./dto/analytics.dto";
import { getLocalDateRange } from "./util";

@Controller("insights/analytics")
@ApiTags("*** insights - analytics")
@UseInterceptors(HttpCacheInterceptor)
@CacheTTL(5 * 60 * 1000)
export class AnalyticsController {
    constructor(private readonly analyticsService: AnalyticsService) {}

    @Get("sources/reddit")
    @ApiOperation({ summary: "Individual subreddit sentiment histogram" })
    @ApiResponse({
        type: [SourceAnalyticsDto],
    })
    async getRedditSources(
        @Query() query: AnalyticsRequestDto,
    ): Promise<SourceAnalyticsDto[]> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        return this.analyticsService.getSubredditAnalytics({
            localDateFrom,
            localDateTo,
            ...query,
        });
    }

    @Get(["trends/topics", "trends/topics.csv"])
    @ApiOperation({ summary: "Individual topic sentiment histogram" })
    @ApiResponse({ type: PaginatedResponseDto<TopicAnalyticsDto> })
    @ApiProduces("application/json", "text/csv")
    @CSVExportable<TopicAnalyticsDto>({
        filename: "tk-topics.csv",
        headers: ["name", "count", "sentiment"],
    })
    async getTrendingTopics(
        @Query() query: AnalyticsRequestDto,
    ): Promise<PaginatedResponseDto<TopicAnalyticsDto>> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        const results = await this.analyticsService.getTrendingTopicsAnalytics(
            {
                localDateFrom,
                localDateTo,
                ...query,
            },
            10,
        );

        return {
            items: results,
            total: results.length,
            page: 1,
            size: 10,
            totalPages: 1,
        };
    }

    @Get(["trends/narratives", "trends/narratives.csv"])
    @ApiOperation({ summary: "Individual narrative sentiment histogram" })
    @ApiResponse({ type: PaginatedResponseDto<NarrativeAnalyticsDto> })
    @ApiProduces("application/json", "text/csv")
    @CSVExportable<NarrativeAnalyticsDto>({
        filename: "tk-narratives.csv",
        headers: ["summary", "count", "sentiment"],
    })
    async getTrendingNarratives(
        @Query() query: AnalyticsRequestDto,
    ): Promise<PaginatedResponseDto<NarrativeAnalyticsDto>> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        const results =
            await this.analyticsService.getTrendingNarrativesAnalytics(
                {
                    localDateFrom,
                    localDateTo,
                    ...query,
                },
                10,
            );

        return {
            items: results,
            total: results.length,
            page: 1,
            size: 10,
            totalPages: 1,
        };
    }

    @Get("trends/narratives/tenant")
    @ApiOperation({
        summary: "Tenant specific narrative sentiment histograms",
    })
    @ApiResponse({ type: [NarrativeAnalyticsDto] })
    async getTrendingTenantNarratives(
        @Query() query: TenantAnalyticsRequestDto,
    ): Promise<NarrativeAnalyticsDto[]> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        return this.analyticsService.getTrendingTenantNarrativesAnalytics(
            {
                localDateFrom,
                localDateTo,
                ...query,
            },
            10,
        );
    }

    @Get("comparison/aspects/mentions")
    @ApiOperation({ summary: "Narrative trends comparison" })
    @ApiResponse({ type: NarrativeAspectTrendsComparisonDto })
    @UsePipes(GroupFilterPipe)
    async getNarrativeTrendsComparison(
        @Query() query: AnalyticsRequestDto,
    ): Promise<NarrativeAspectTrendsComparisonDto> {
        const { dateFrom } = query;
        const { localDateFrom, localDateTo } = getLocalDateRange({
            dateFrom: dateFrom ?? "2025-01-01",
            ...query,
        });
        return this.analyticsService.getNarrativeAspectTrendsComparison(
            {
                localDateFrom,
                localDateTo,
                ...query,
            },
            "mentions",
        );
    }

    @Get("comparison/aspects/sentiment")
    @ApiOperation({ summary: "Narrative trends sentiment comparison" })
    @ApiResponse({ type: NarrativeAspectTrendsComparisonDto })
    @UsePipes(GroupFilterPipe)
    async getNarrativeTrendsSentimentComparison(
        @Query() query: AnalyticsRequestDto,
    ): Promise<NarrativeAspectTrendsComparisonDto> {
        const { dateFrom } = query;
        const { localDateFrom, localDateTo } = getLocalDateRange({
            dateFrom: dateFrom ?? "2025-01-01",
            ...query,
        });
        return this.analyticsService.getNarrativeAspectTrendsComparison(
            {
                localDateFrom,
                localDateTo,
                ...query,
            },
            "averageSentiment",
        );
    }

    @Get("comparison/narratives")
    @ApiOperation({ summary: "Topic/narrative scatter plot" })
    @ApiResponse({ type: [TopicScatterPlotDto] })
    @UsePipes(GroupFilterPipe)
    async getNarrativeComparison(
        @Query() query: AnalyticsRequestDto,
    ): Promise<TopicScatterPlotDto[]> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        return this.analyticsService.getTopicScatterPlot({
            localDateFrom,
            localDateTo,
            ...query,
        });
    }

    @Get("narratives/:narrativeId")
    @ApiOperation({ summary: "Topic/narrative scatter plot popover data" })
    @ApiResponse({ type: NarrativeAnalyticsDto })
    async getNarrativeComparisonPopover(
        @Param("narrativeId") narrativeId: number,
        @Query() query: AnalyticsRequestDto,
    ): Promise<NarrativeAnalyticsDto> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        return this.analyticsService.getNarrativeAnalytics({
            ...query,
            localDateFrom,
            localDateTo,
            narrativeIds: [narrativeId],
        });
    }

    @Get("topics/tenant")
    @ApiOperation({
        summary: `Tenant specific topic detail analytics`,
    })
    @ApiResponse({ type: TopicAnalyticsDto })
    async getTenantTopicDetail(
        @Query() query: TenantAnalyticsRequestDto,
    ): Promise<TopicAnalyticsDto> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        return this.analyticsService.getTenantTopicAnalytics({
            localDateFrom,
            localDateTo,
        });
    }

    @Get("topics/:id")
    @ApiOperation({ summary: "Top topic detail analytics" })
    @ApiResponse({ type: TopicAnalyticsDto })
    async getTopicDetail(
        @Param("id") topicId: number,
        @Query() query: AnalyticsRequestDto,
    ): Promise<TopicAnalyticsDto> {
        const { localDateFrom, localDateTo } = getLocalDateRange(query);
        return this.analyticsService.getTopicAnalytics(
            { localDateFrom, localDateTo },
            Number(topicId),
        );
    }
}
