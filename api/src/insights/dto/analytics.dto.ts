import { IntersectionType, OmitType } from "@nestjs/swagger";

import { NarrativeAspect } from "@prisma/client";

import {
    DateFilteredRequestDto,
    DateFilterParams,
} from "../../generic/dto/date-filtered.dto";
import {
    SentimentFilteredDto,
    SentimentFilterParams,
    SourceFilteredDto,
    SourceFilterParams,
    TopicFilteredDto,
    TopicFilterParams,
    NarrativeFilteredDto,
    NarrativeFilterParams,
    NarrativeAspectFilteredDto,
    NarrativeAspectFilterParams,
    GroupFilterParams,
    GroupFilteredDto,
} from "./shared.dto";

export interface AnalyticsFilterParams
    extends DateFilterParams,
        SentimentFilterParams,
        TopicFilterParams,
        SourceFilterParams,
        NarrativeFilterParams,
        NarrativeAspectFilterParams,
        GroupFilterParams {}
export class AnalyticsRequestDto
    extends IntersectionType(
        DateFilteredRequestDto,
        SentimentFilteredDto,
        TopicFilteredDto,
        SourceFilteredDto,
        NarrativeFilteredDto,
        NarrativeAspectFilteredDto,
        GroupFilteredDto,
    )
    implements AnalyticsFilterParams {}

export class TenantAnalyticsRequestDto
    extends OmitType(AnalyticsRequestDto, ["topicIds"])
    implements AnalyticsFilterParams {}

export class SourceAnalyticsDto {
    name: string;
    count: number;
    sentiment: number;
    sentimentDistribution: number[];
}

export class TopicAnalyticsDto {
    id: number;
    name: string;
    count: number;
    sentiment: number;
    sentimentDistribution: number[];
}

export class NarrativeAnalyticsDto {
    id: number;
    summary: string;
    count: number;
    sentiment: number;
    sentimentDistribution: number[];
}

export class NarrativeAspectTrendsComparisonDto {
    topics: string[];
    data: Array<{ date: string } & Record<string, number>>;
}

export class TopicScatterPlotDto {
    narrativeId: number;
    summary: string;
    narrativeAspect: NarrativeAspect;
    topicId: number;
    topicName: string;
    sentiment: number;
    count: number;
}
