import {
    ApiPropertyOptional,
    IntersectionType,
    OmitType,
} from "@nestjs/swagger";

import { DataSource, NarrativeAspect } from "@prisma/client";
import { Transform } from "class-transformer";
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsNumber,
    IsOptional,
    IsString,
    <PERSON>,
    Min,
} from "class-validator";

import {
    DateFilteredRequestDto,
    DateFilterParams,
} from "../../generic/dto/date-filtered.dto";
import { PaginatedRequestDto } from "../../generic/dto/paginated.dto";
import {
    SentimentFilteredDto,
    SentimentFilterParams,
    SourceFilteredDto,
    SourceFilterParams,
    TopicFilteredDto,
    TopicFilterParams,
    NarrativeFilteredDto,
    NarrativeFilterParams,
    FavouriteFilterParams,
    FavouriteFilteredDto,
    GroupShareFilteredDto,
    GroupShareFilterParams,
    GroupFilterParams,
    GroupFilteredDto,
} from "./shared.dto";

const SortKey = {
    RELEVANCE: "RELEVANCE",
    DATE: "DATE",
    ENGAGEMENT: "ENGAGEMENT",
    POSITIVE_SENTIMENT: "POSITIVE_SENTIMENT",
    NEGATIVE_SENTIMENT: "NEGATIVE_SENTIMENT",
} as const;

export type SortKey = (typeof SortKey)[keyof typeof SortKey];

export interface TimelineFilterParams
    extends DateFilterParams,
        SentimentFilterParams,
        TopicFilterParams,
        SourceFilterParams,
        NarrativeFilterParams,
        FavouriteFilterParams,
        GroupFilterParams,
        GroupShareFilterParams {
    tagIds?: number[];
    bookmarkedOnly?: boolean;
    minRelevance?: number;
    maxRelevance?: number;
    sort?: SortKey;
    q?: string;
}

export type TenantTimelineFilterParams = Omit<TimelineFilterParams, "topicIds">;

export class TimelineRequestDto
    extends IntersectionType(
        PaginatedRequestDto,
        DateFilteredRequestDto,
        SentimentFilteredDto,
        TopicFilteredDto,
        SourceFilteredDto,
        NarrativeFilteredDto,
        FavouriteFilteredDto,
        GroupFilteredDto,
        GroupShareFilteredDto,
    )
    implements TimelineFilterParams
{
    @IsArray()
    @ApiPropertyOptional({
        type: Number,
        isArray: true,
        description: "Array of tag IDs",
        example: [],
    })
    @Transform(({ value }) =>
        Array.isArray(value) ? value.map(Number) : [Number(value)],
    )
    @IsOptional()
    tagIds?: number[] = [];

    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    bookmarkedOnly?: boolean;

    @IsOptional()
    @IsNumber()
    @Min(-1)
    @Max(100)
    @ApiPropertyOptional({
        description: "Minimum relevance filter",
        default: 75,
        example: -1,
    })
    @Transform(({ value }) => (value === undefined ? undefined : Number(value)))
    minRelevance?: number;

    @IsOptional()
    @IsNumber()
    @Min(-1)
    @Max(100)
    @ApiPropertyOptional({
        description: "Maximum relevance filter",
        default: 100,
        example: 100,
    })
    @Transform(({ value }) => (value === undefined ? undefined : Number(value)))
    maxRelevance?: number;

    @IsOptional()
    @IsEnum(SortKey)
    @ApiPropertyOptional({
        enum: SortKey,
        example: SortKey.RELEVANCE,
    })
    sort?: SortKey = SortKey.RELEVANCE;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({
        description: "Keyword search",
    })
    q?: string;
}

export class TenantTimelineRequestDto
    extends OmitType(TimelineRequestDto, ["topicIds"])
    implements TenantTimelineFilterParams {}

class TopicDto {
    id: number;
    name: string;
}

export class NarrativeDto {
    sentiment: number;
    sentimentReasoning: string;
    aspect: NarrativeAspect;
    topic: TopicDto;
    summary: string;
}

class RelevanceDto {
    score: number;
    reasoning: string;
}

class CommentDto {
    id: string;
    body: string;
}

class TagDto {
    id: number;
    name: string;
}

export class TimelineItemDto {
    id: number;
    externalUrl: string;
    sourceId: string;
    type: DataSource;
    publishedAt: Date;
    topics: TopicDto[];
    tags: TagDto[];
    sentiment: number;
    sentimentReasoning: string;
    title: string;
    summary: string;
    longSummary: string;
    tipsAndActions: string;
    narratives: NarrativeDto[];
    relevance: RelevanceDto;
    bookmarked?: boolean;
    subredditName?: string;
    channelHandle?: string;
    replies: {
        items: CommentDto[];
        count: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
