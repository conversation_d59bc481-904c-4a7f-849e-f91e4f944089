import { Injectable, Logger } from "@nestjs/common";

import { DataSource, Prisma } from "@prisma/client";
import { htmlToText } from "html-to-text";
import { uniqBy } from "lodash";
import { ClsService } from "nestjs-cls";

import { PrismaRootService } from "../prisma-root/prisma-root.service";
import { PrismaService } from "../prisma/prisma.service";
import { TimelineFilterParams } from "./dto/timeline.dto";
import { getLocalDateRangeEnd, getLocalDateRangeStart } from "./util";

export type TimelineParams = TimelineFilterParams & {
    take: number;
    skip: number;
    userId: string;
};

@Injectable()
export class TimelineService {
    private logger = new Logger("TimelineService");

    constructor(
        private readonly prisma: PrismaService,
        private readonly prismaRootService: PrismaRootService,
        private readonly cls: ClsService,
    ) {}

    async onModuleInit() {
        await this.refreshMaterializedView();
    }

    async getTimelineItems(params: TimelineParams) {
        const whereConditions: Prisma.Sql[] = [];
        const orderByClauses: Prisma.Sql[] = [];

        // Date filter
        const { dateFrom, dateTo, timeZone } = params;
        const localDateFrom =
            dateFrom && getLocalDateRangeStart(dateFrom, timeZone);
        const localDateTo = dateTo && getLocalDateRangeEnd(dateTo, timeZone);
        if (localDateFrom) {
            whereConditions.push(
                Prisma.sql`"TimelineItem"."publishedAt" >= ${localDateFrom}`,
            );
        }
        if (localDateTo) {
            whereConditions.push(
                Prisma.sql`"TimelineItem"."publishedAt" <= ${localDateTo}`,
            );
        }

        // Source filter
        if (params.sources?.length > 0) {
            const sourceValues = params.sources.map(
                (source) => Prisma.sql`${source}::"DataSource"`,
            );
            whereConditions.push(
                Prisma.sql`"TimelineItem"."source" IN (${Prisma.join(sourceValues, ", ")})`,
            );
        }

        // Relevance filter
        const minRelevance = params.minRelevance ?? -1;
        const maxRelevance = params.maxRelevance ?? 100;
        whereConditions.push(
            Prisma.sql`"Analysis"."relevance" >= ${minRelevance}`,
        );
        whereConditions.push(
            Prisma.sql`"Analysis"."relevance" <= ${maxRelevance}`,
        );

        // Sentiment filter
        if (params.sentiments?.length > 0) {
            const sentimentConditions: Prisma.Sql[] = [];
            if (params.sentiments.includes("POSITIVE")) {
                sentimentConditions.push(
                    Prisma.sql`("Analysis"."sentiment" >= 50 AND "Analysis"."sentiment" <= 100)`,
                );
            }
            if (params.sentiments.includes("NEGATIVE")) {
                sentimentConditions.push(
                    Prisma.sql`("Analysis"."sentiment" >= 0 AND "Analysis"."sentiment" <= 50)`,
                );
            }
            if (params.sentiments.includes("UNKNOWN")) {
                sentimentConditions.push(
                    Prisma.sql`"Analysis"."sentiment" = -1`,
                );
            }
            whereConditions.push(
                Prisma.sql`(${Prisma.join(sentimentConditions, " OR ")})`,
            );
        }

        // Full-text search filter
        if (params.q) {
            const search = params.q.trim();
            whereConditions.push(
                Prisma.sql`"Analysis"."searchVector" @@ plainto_tsquery('english', ${search})`,
            );
        }

        // Bookmark filter
        if (params.bookmarkedOnly) {
            whereConditions.push(
                Prisma.sql`EXISTS (
                    SELECT 1 FROM "_AnalysisToUser"
                    WHERE "_AnalysisToUser"."A" = "Analysis"."id"
                    AND "_AnalysisToUser"."B" = ${params.userId}
                )`,
            );
        }

        // Group share filter
        if (params.sharedWithGroupIds?.length > 0) {
            whereConditions.push(
                Prisma.sql`EXISTS (
                    SELECT 1 FROM "Share"
                    WHERE "Share"."analysisId" = "Analysis"."id"
                    AND "Share"."groupId" IN (${Prisma.join(params.sharedWithGroupIds)})
                )`,
            );
        }

        // Narrative, topic, tag, and group filters (require joins via AnalysisToNarrative)
        const narrativeJoinConditions: Prisma.Sql[] = [];

        if (params.narrativeIds?.length > 0) {
            narrativeJoinConditions.push(
                Prisma.sql`"Narrative"."id" IN (${Prisma.join(params.narrativeIds)})`,
            );
        }

        if (params.topicIds?.length > 0) {
            narrativeJoinConditions.push(
                Prisma.sql`"Topic"."id" IN (${Prisma.join(params.topicIds)})`,
            );
        }

        if (params.tagIds?.length > 0) {
            narrativeJoinConditions.push(
                Prisma.sql`EXISTS (
                    SELECT 1 FROM "_TagToTopic"
                    WHERE "_TagToTopic"."B" = "Topic"."id"
                    AND "_TagToTopic"."A" IN (${Prisma.join(params.tagIds)})
                )`,
            );
        }

        if (params.groupIds?.length > 0) {
            narrativeJoinConditions.push(
                Prisma.sql`EXISTS (
                    SELECT 1 FROM "_GroupToTopic"
                    WHERE "_GroupToTopic"."B" = "Topic"."id"
                    AND "_GroupToTopic"."A" IN (${Prisma.join(params.groupIds)})
                )`,
            );
        }

        if (params.onlyFavourited) {
            narrativeJoinConditions.push(
                Prisma.sql`EXISTS (
                    SELECT 1 FROM "UserTopicPreference"
                    WHERE "UserTopicPreference"."topicId" = "Topic"."id"
                    AND "UserTopicPreference"."userId" = ${params.userId}
                    AND "UserTopicPreference"."isFavourite" = true
                )`,
            );
        }

        // Build JOIN clause for narrative-related filters
        const needsNarrativeJoin = narrativeJoinConditions.length > 0;
        const narrativeJoin = needsNarrativeJoin
            ? Prisma.sql`
                INNER JOIN "AnalysisToNarrative" ON "AnalysisToNarrative"."analysisId" = "Analysis"."id"
                INNER JOIN "Narrative" ON "Narrative"."id" = "AnalysisToNarrative"."narrativeId"
                INNER JOIN "Topic" ON "Topic"."id" = "Narrative"."topicId"
            `
            : Prisma.empty;

        if (needsNarrativeJoin) {
            whereConditions.push(
                Prisma.sql`(${Prisma.join(narrativeJoinConditions, " AND ")})`,
            );
            orderByClauses.unshift(
                Prisma.sql`"TimelineItem"."source"`,
                Prisma.sql`"TimelineItem"."sourceId"`,
                Prisma.sql`"TimelineItem"."analysisId"`,
            );
        }

        // Build ORDER BY clause
        const sort = params.sort || "RELEVANCE";

        if (sort === "POSITIVE_SENTIMENT") {
            orderByClauses.push(Prisma.sql`"Analysis"."sentiment" DESC`);
        } else if (sort === "NEGATIVE_SENTIMENT") {
            orderByClauses.push(Prisma.sql`"Analysis"."sentiment" ASC`);
        } else if (sort === "ENGAGEMENT") {
            orderByClauses.push(Prisma.sql`"TimelineItem"."commentCount" DESC`);
            orderByClauses.push(Prisma.sql`"TimelineItem"."likeCount" DESC`);
        } else if (sort === "DATE") {
            orderByClauses.push(Prisma.sql`"TimelineItem"."publishedAt" DESC`);
        }

        // Always add default ordering
        orderByClauses.push(Prisma.sql`"Analysis"."relevance" DESC`);
        orderByClauses.push(Prisma.sql`"TimelineItem"."commentCount" DESC`);
        orderByClauses.push(Prisma.sql`"TimelineItem"."likeCount" DESC`);
        orderByClauses.push(Prisma.sql`"TimelineItem"."publishedAt" DESC`);

        const orderByClause = Prisma.sql`ORDER BY ${Prisma.join(orderByClauses, ", ")}`;

        // Build the main query
        const whereClause =
            whereConditions.length > 0
                ? Prisma.sql`WHERE ${Prisma.join(whereConditions, " AND ")}`
                : Prisma.empty;

        // Use DISTINCT to handle multiple narratives per analysis
        const distinctClause = needsNarrativeJoin
            ? Prisma.sql`DISTINCT ON ("TimelineItem"."source", "TimelineItem"."sourceId", "TimelineItem"."analysisId")`
            : Prisma.empty;

        const itemsQuery = Prisma.sql`
            SELECT ${distinctClause}
                "TimelineItem"."source",
                "TimelineItem"."sourceId",
                "TimelineItem"."analysisId"
            FROM "TimelineItem"
            INNER JOIN "Analysis" ON "Analysis"."id" = "TimelineItem"."analysisId"
            ${narrativeJoin}
            ${whereClause}
            ${orderByClause}
            LIMIT ${params.take}
            OFFSET ${params.skip}
        `;

        const countQuery = Prisma.sql`
            SELECT COUNT(DISTINCT ("TimelineItem"."source", "TimelineItem"."sourceId", "TimelineItem"."analysisId"))::int as count
            FROM "TimelineItem"
            INNER JOIN "Analysis" ON "Analysis"."id" = "TimelineItem"."analysisId"
            ${narrativeJoin}
            ${whereClause}
        `;

        const [items, countResult] = await Promise.all([
            this.prisma.$queryRawWithTenant<
                Array<{
                    source: DataSource;
                    sourceId: string;
                    analysisId: string;
                }>
            >(itemsQuery),
            this.prisma.$queryRawWithTenant<Array<{ count: number }>>(
                countQuery,
            ),
        ]);

        return { items, total: countResult[0]?.count ?? 0 };
    }

    async getTimelineItemDetails(
        source: DataSource,
        sourceId: string,
        userId?: string,
    ) {
        const itemPromise = this.prisma.timelineItem.findUniqueOrThrow({
            where: {
                tenantId_source_sourceId: {
                    tenantId: this.cls.get("tenantId"),
                    source,
                    sourceId,
                },
            },
            include: {
                analysis: {
                    include: {
                        narratives: {
                            include: {
                                narrative: {
                                    include: {
                                        topic: {
                                            include: {
                                                tags: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        bookmarkedBy: {
                            select: {
                                id: true,
                            },
                            where: {
                                id: userId,
                            },
                        },
                    },
                },
            },
        });

        let detailsPromise: Promise<{
            title: string;
            commentCount?: number;
            voteScore: number;
            externalUrl: string;
            channelHandle?: string;
            subredditName?: string;
            replies?: {
                items: {
                    id: string;
                    body: string;
                }[];
                count: number;
            };
        }>;

        switch (source) {
            case DataSource.REDDIT_POST:
                detailsPromise = this.getRedditPostDetails(sourceId);
                break;
            case DataSource.YOUTUBE_VIDEO:
                detailsPromise = this.getYoutubeVideoDetails(sourceId);
                break;
            case DataSource.YOUTUBE_COMMENT:
                detailsPromise = this.getYoutubeCommentDetails(sourceId);
                break;
            case DataSource.REDDIT_COMMENT:
                detailsPromise = this.getRedditCommentDetails(sourceId);
                break;
            case DataSource.AVR_FREAKS:
            case DataSource.MICROCHIP_CLASSIC:
                detailsPromise = this.getSalesforcePostDetails(sourceId);
                break;
            case DataSource.ALL_ABOUT_CIRCUITS:
                detailsPromise = this.getAllAboutCircuitsPostDetails(sourceId);
                break;
            default:
                throw new Error(`Unknown source: ${source}`);
        }

        const [item, details] = await Promise.all([
            itemPromise,
            detailsPromise,
        ]);

        const { publishedAt } = item;
        const {
            longSummary,
            isActionable,
            tipsAndActions,
            actionableReasoning,
            bookmarkedBy,
            createdAt,
            updatedAt,
        } = item.analysis;
        const {
            summary,
            narratives: rawNarratives,
            sentiment,
            sentimentReasoning,
            relevance,
            relevanceReasoning,
        } = item.analysis;
        const { title, commentCount, voteScore, externalUrl, replies } =
            details;

        const topics = uniqBy(
            rawNarratives
                .map((narrative) => narrative.narrative.topic)
                .filter(Boolean),
            "id",
        );
        const tags = uniqBy(
            topics.flatMap((topic) => topic.tags),
            "id",
        );

        const narratives = rawNarratives.map(
            ({ narrative, sentiment, sentimentReasoning }) => ({
                ...narrative,
                sentiment,
                sentimentReasoning,
            }),
        );

        return {
            id: item.analysisId,
            sourceId,
            type: item.source,
            publishedAt,
            commentCount,
            voteScore,
            title,
            sentiment,
            sentimentReasoning,
            narratives,
            topics,
            tags,
            summary,
            longSummary,
            isActionable,
            tipsAndActions,
            actionableReasoning,
            relevance: {
                score: relevance,
                reasoning: relevanceReasoning,
            },
            externalUrl,
            replies: replies ?? { items: [], count: 0 },
            bookmarked: !!bookmarkedBy.length,
            subredditName: details["subredditName"],
            channelHandle: details["channelHandle"],
            createdAt,
            updatedAt,
        };
    }

    private async getRedditPostDetails(id: string) {
        return this.prisma.redditPost
            .findUniqueOrThrow({
                select: {
                    id: true,
                    title: true,
                    commentCount: true,
                    voteScore: true,
                    subredditName: true,
                    comments: {
                        select: {
                            id: true,
                            body: true,
                        },
                        where: {
                            body: {
                                not: "[deleted]",
                            },
                        },
                        take: 3,
                    },
                },
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id,
                    },
                },
            })
            .then(
                ({
                    subredditName,
                    id,
                    commentCount,
                    comments,
                    title,
                    voteScore,
                }) => ({
                    id,
                    commentCount,
                    title,
                    voteScore,
                    subredditName,
                    externalUrl: `https://www.reddit.com/r/${subredditName}/comments/${id}`,
                    replies: {
                        items: comments.map((comment) => ({
                            ...comment,
                            externalUrl: `https://www.reddit.com/r/${subredditName}/comments/${id}/comment/${comment.id}`,
                        })),
                        count: commentCount,
                    },
                }),
            );
    }

    private async getYoutubeVideoDetails(id: string) {
        return this.prisma.youtubeVideo
            .findUniqueOrThrow({
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id,
                    },
                },
                select: {
                    id: true,
                    title: true,
                    commentCount: true,
                    likeCount: true,
                    channel: {
                        select: {
                            channelHandle: true,
                        },
                    },
                    comments: {
                        select: {
                            id: true,
                            body: true,
                        },
                        take: 3,
                    },
                },
            })
            .then(({ title, commentCount, likeCount, comments, channel }) => ({
                title,
                commentCount,
                voteScore: likeCount,
                externalUrl: `https://www.youtube.com/watch?v=${id}`,
                channelHandle: channel?.channelHandle,
                replies: {
                    items: comments.map((comment) => ({
                        ...comment,
                        externalUrl: `https://www.youtube.com/watch?v=${id}&lc=${comment.id}`,
                    })),
                    count: commentCount,
                },
            }));
    }

    private async getYoutubeCommentDetails(id: string) {
        const replyCount = await this.prisma.youtubeComment.count({
            where: {
                parentId: id,
            },
        });

        return this.prisma.youtubeComment
            .findUniqueOrThrow({
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id,
                    },
                },
                include: {
                    youtubeVideo: {
                        select: {
                            id: true,
                            channel: { select: { channelHandle: true } },
                        },
                    },
                    replies: {
                        select: {
                            id: true,
                            body: true,
                        },
                        take: 3,
                    },
                },
            })
            .then(
                ({
                    body,
                    score,
                    youtubeVideo: { id: videoId, channel },
                    replies,
                }) => ({
                    title: body,
                    voteScore: score,
                    externalUrl: `https://www.youtube.com/watch?v=${videoId}&lc=${id}`,
                    channelHandle: channel?.channelHandle,
                    replies: {
                        items: replies,
                        count: replyCount,
                    },
                }),
            );
    }

    private async getRedditCommentDetails(id: string) {
        const replyCount = await this.prisma.redditComment.count({
            where: {
                parentId: id,
            },
        });

        return this.prisma.redditComment
            .findUniqueOrThrow({
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id,
                    },
                },
                include: {
                    redditPost: {
                        select: {
                            id: true,
                            subredditName: true,
                        },
                    },
                    replies: {
                        select: {
                            id: true,
                            body: true,
                        },
                        where: {
                            body: {
                                not: "[deleted]",
                            },
                        },
                        take: 3,
                    },
                },
            })
            .then(
                ({
                    body,
                    score,
                    redditPost: { id: postId, subredditName },
                    replies,
                }) => ({
                    title: body,
                    voteScore: score,
                    externalUrl: `https://www.reddit.com/r/${subredditName}/comments/${postId}/comment/${id}`,
                    subredditName,
                    replies: {
                        items: replies.map((reply) => ({
                            ...reply,
                            externalUrl: `https://www.reddit.com/r/${subredditName}/comments/${postId}/comment/${reply.id}`,
                        })),
                        count: replyCount,
                    },
                }),
            );
    }

    private async getSalesforcePostDetails(id: string) {
        const post = await this.prisma.salesforcePost.findUniqueOrThrow({
            where: { id },
            select: {
                id: true,
                title: true,
                url: true,
                memberLikeCount: true,
                memberDislikeCount: true,
                guestLikeCount: true,
                guestDislikeCount: true,
                replies: {
                    select: {
                        id: true,
                        name: true,
                        text: true,
                    },
                },
            },
        });

        const replyCount = await this.prisma.salesforcePostReply.count({
            where: {
                postId: id,
            },
        });

        return {
            id: post.id,
            title: post.title,
            voteScore: post.memberLikeCount + post.guestLikeCount,
            commentCount: replyCount,
            externalUrl: post.url,
            replies: {
                items: post.replies.map((reply) => ({
                    id: reply.id,
                    body: htmlToText(reply.text),
                    externalUrl: `${post.url}?comment=${reply.name}`,
                })),
                count: replyCount,
            },
        };
    }

    private async getAllAboutCircuitsPostDetails(id: string) {
        const thread = await this.prisma.allAboutCircuitsPost.findUniqueOrThrow(
            {
                where: { id },
                include: {
                    replies: {
                        select: {
                            id: true,
                            text: true,
                            url: true,
                        },
                    },
                },
            },
        );

        return {
            title: thread.title,
            commentCount: thread.replies.length,
            voteScore: 0,
            externalUrl: thread.url,
            replies: {
                items: thread.replies.map((reply) => ({
                    id: reply.id,
                    body: reply.text,
                    externalUrl: reply.url,
                })),
                count: thread.replies.length,
            },
        };
    }

    async refreshMaterializedView() {
        this.logger.log(`REFRESH MATERIALIZED VIEW "TimelineItem"`);
        await this.prismaRootService
            .$executeRaw`REFRESH MATERIALIZED VIEW "TimelineItem"`;
    }
}
