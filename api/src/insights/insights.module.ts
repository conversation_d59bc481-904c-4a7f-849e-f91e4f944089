import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { CacheModule } from "../cache/cache.module";
import { OpenaiModule } from "../openai/openai.module";
import { AnalyticsController } from "./analytics.controller";
import { AnalyticsService } from "./analytics.service";
import { BookmarkController } from "./bookmark.controller";
import { BookmarkService } from "./bookmark.service";
import { InsightsService } from "./insights.service";
import { SearchController } from "./search.controller";
import { SearchService } from "./search.service";
import { TimelineController } from "./timeline.controller";
import { TimelineService } from "./timeline.service";

@Module({
    imports: [CacheModule, OpenaiModule],
    controllers: [
        AnalyticsController,
        BookmarkController,
        SearchController,
        TimelineController,
    ],
    providers: [
        AnalyticsService,
        BookmarkService,
        InsightsService,
        SearchService,
        TimelineService,
    ],
    exports: [InsightsService, SearchService, TimelineService],
})
export class InsightsModule {}
