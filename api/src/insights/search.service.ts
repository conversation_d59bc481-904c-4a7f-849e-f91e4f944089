import { BadGatewayException, Injectable, Logger } from "@nestjs/common";

import { DataSource, Prisma, SearchableEntity } from "@prisma/client";

import { OpenaiService } from "../openai/openai.service";
import { PrismaService } from "../prisma/prisma.service";
import { SearchFilterParams, SearchResultDto } from "./dto/search.dto";
import { TimelineService } from "./timeline.service";

type SearchParams = SearchFilterParams & {
    query: string;
    type: "hybrid" | "semantic" | "keyword";
    take: number;
    skip: number;
    userId?: string;
    sources?: DataSource[];
};

type SearchEntityWithScore = Pick<SearchableEntity, "source" | "sourceId"> & {
    score: number;
    searchType: "semantic" | "keyword";
    total: number;
};

@Injectable()
export class SearchService {
    private logger = new Logger("SearchService");

    constructor(
        private readonly prisma: PrismaService,
        private readonly openaiService: OpenaiService,
        private readonly timelineService: TimelineService,
    ) {}

    async search(
        params: SearchParams,
    ): Promise<{ items: SearchResultDto[]; total: number }> {
        const {
            query,
            type,
            take,
            skip,
            userId,
            sharedWithGroupIds,
            bookmarkedOnly,
            sources,
        } = params;

        const entities: { items: SearchEntityWithScore[]; total: number } =
            await (async () => {
                switch (type) {
                    case "hybrid":
                        return await this.hybridSearch(
                            query,
                            take,
                            skip,
                            userId,
                            sharedWithGroupIds,
                            bookmarkedOnly,
                            sources,
                        );
                    case "semantic":
                        return await this.semanticSearch(
                            query,
                            take,
                            skip,
                            userId,
                            sharedWithGroupIds,
                            bookmarkedOnly,
                            sources,
                        );
                    case "keyword":
                        return await this.keywordSearch(
                            query,
                            take,
                            skip,
                            userId,
                            sharedWithGroupIds,
                            bookmarkedOnly,
                            sources,
                        );
                    default:
                        throw new Error(`Unknown search type: ${type}`);
                }
            })();

        const itemsWithDetail = await Promise.allSettled(
            entities.items.map(async (entity) => {
                const details =
                    await this.timelineService.getTimelineItemDetails(
                        entity.source,
                        entity.sourceId,
                    );
                return {
                    ...details,
                    score: entity.score,
                    searchType: entity.searchType,
                };
            }),
        ).then((results) =>
            results
                .map((result) => this.safeResolve(result, null))
                .filter(Boolean),
        );

        return { items: itemsWithDetail, total: entities.total };
    }

    async keywordSearch(
        query: string,
        take: number,
        skip: number,
        userId?: string,
        sharedWithGroupIds?: number[],
        bookmarkedOnly?: boolean,
        sources?: DataSource[],
    ): Promise<{ items: SearchEntityWithScore[]; total: number }> {
        const startTime = new Date().getTime();

        const cleanedQuery = query
            .replace(/[^\w\s]/g, " ")
            .replace(/\s+/g, " ")
            .trim()
            .toLowerCase();

        const entities = await this.prisma.$queryRawWithTenant<
            SearchEntityWithScore[]
        >`
            SELECT 
                "SearchableEntity"."source", 
                "SearchableEntity"."sourceId", 
                COUNT(*) OVER()::int AS total, 
                ts_rank_cd("SearchableEntity"."searchVector", plainto_tsquery('english', ${cleanedQuery})) as score,
                'keyword' as searchType
            FROM "SearchableEntity"
                JOIN "TimelineItem"
                    ON "SearchableEntity"."source" = "TimelineItem"."source"
                    AND "SearchableEntity"."sourceId" = "TimelineItem"."sourceId"
                JOIN "Analysis"
                    ON "Analysis"."source" = "SearchableEntity"."source"
                    AND "Analysis"."sourceId" = "SearchableEntity"."sourceId"
                LEFT JOIN "Share"
                    ON "Analysis"."id" = "Share"."analysisId"
                LEFT JOIN "Group"
                    ON "Group"."id" = "Share"."groupId"
                LEFT JOIN "_AnalysisToUser"
                    ON "_AnalysisToUser"."A" = "Analysis"."id"
                    AND "_AnalysisToUser"."B" = ${userId}
            WHERE "SearchableEntity"."searchVector" @@ plainto_tsquery('english', ${cleanedQuery})
            ${sharedWithGroupIds?.length ? Prisma.sql`AND "Group"."id" IN (${Prisma.join(sharedWithGroupIds)})` : Prisma.sql``}
            ${bookmarkedOnly ? Prisma.sql`AND "_AnalysisToUser"."A" IS NOT NULL` : Prisma.sql``}
            ${sources ? Prisma.sql`AND "SearchableEntity"."source" = ANY(${Prisma.join(sources)}::"DataSource"[])` : Prisma.sql``}
            ORDER BY score DESC
            LIMIT ${take}
            OFFSET ${skip}
        `;

        const queryTime = new Date().getTime() - startTime;
        console.log(`Keyword search query execution time: ${queryTime}ms`);

        return {
            items: entities,
            total: entities[0]?.total ?? 0,
        };
    }

    async semanticSearch(
        query: string,
        take: number,
        skip: number,
        userId?: string,
        sharedWithGroupIds?: number[],
        bookmarkedOnly?: boolean,
        sources?: DataSource[],
    ): Promise<{ items: SearchEntityWithScore[]; total: number }> {
        console.log(`Start embedding`);

        const embeddingResponse = await this.openaiService.getEmbedding({
            input: query,
            model: "text-embedding-3-small",
        });
        console.log(`End embedding`);

        const embedding = embeddingResponse.data[0].embedding;
        if (!embedding)
            throw new BadGatewayException(
                "Failed to get embedding from OpenAI",
            );

        const maxDistance = 0.75;

        console.log(`Start query execution`);
        const startTime = new Date().getTime();

        // Step 1: Find similar embeddings first (uses HNSW index efficiently)

        const similarEntities = await this.prisma.$queryRawWithTenant<
            {
                source: DataSource;
                sourceId: string;
                score: number;
            }[]
        >`
            SELECT
                "source",
                "sourceId",
                embedding <=> ${embedding}::vector AS score
            FROM
                "SearchableEntity"
            WHERE
                embedding <=> ${embedding}::vector < ${maxDistance}
            ORDER BY
                embedding <=> ${embedding}::vector
            LIMIT ${take}
            OFFSET ${skip}
            `;

        const queryTime = new Date().getTime() - startTime;
        console.log(`Semantic search query execution time: ${queryTime}ms`);

        if (similarEntities.length === 0) {
            return {
                items: [],
                total: 0,
            };
        }

        // Step 2: Extract the candidate IDs for filtering
        const candidatePairs = similarEntities.map((e) => ({
            source: e.source,
            sourceId: e.sourceId,
        }));
        const scores = similarEntities.map((e) => e.score);

        // Step 3: Apply filters and joins on the much smaller dataset
        // This will be fast because we're only working with ~50-150 records instead of millions
        const entities: {
            source: DataSource;
            sourceId: string;
            total: number;
            score: number;
            searchType: "semantic";
        }[] = await this.prisma.$queryRawWithTenant`
            WITH candidate_entities AS (
                SELECT 
                    unnest(${candidatePairs.map((p) => p.source)}::"DataSource"[]) AS "source",
                    unnest(${candidatePairs.map((p) => p.sourceId)}::text[]) AS "sourceId",
                    unnest(${scores}::float[]) AS "score"
            ),
            filtered_entities AS (
                SELECT
                    ce."source",
                    ce."sourceId",
                    ce."score",
                    "Analysis"."id" AS "analysisId",
                    "Group"."id" AS "groupId",
                    "_AnalysisToUser"."A" IS NOT NULL AS "isBookmarked"
                FROM
                    candidate_entities ce
                    JOIN "TimelineItem"
                        ON ce."source" = "TimelineItem"."source"
                        AND ce."sourceId" = "TimelineItem"."sourceId"
                    JOIN "Analysis"
                        ON "Analysis"."source" = ce."source"
                        AND "Analysis"."sourceId" = ce."sourceId"
                    LEFT JOIN "Share"
                        ON "Analysis"."id" = "Share"."analysisId"
                    LEFT JOIN "Group"
                        ON "Group"."id" = "Share"."groupId"
                    LEFT JOIN "_AnalysisToUser"
                        ON "_AnalysisToUser"."A"::text = "Analysis"."id"::text
                        AND "_AnalysisToUser"."B" = ${userId}
                WHERE
                    1 = 1
                    ${sharedWithGroupIds?.length ? Prisma.sql`AND "Group"."id" IN (${Prisma.join(sharedWithGroupIds)})` : Prisma.empty}
                    ${bookmarkedOnly ? Prisma.sql`AND "_AnalysisToUser"."A" IS NOT NULL` : Prisma.empty}
                    ${sources ? Prisma.sql`AND ce."source" = ANY(${Prisma.join(sources)}::"DataSource"[])` : Prisma.empty}
            )
            SELECT
                "source",
                "sourceId",
                COUNT(*) OVER()::int AS total,
                "score",
                'semantic' as searchType
            FROM
                filtered_entities
            ORDER BY
                "score" ASC
            LIMIT ${take}
            OFFSET ${skip}
        `;

        return {
            items: entities,
            total: entities[0]?.total ?? 0,
        };
    }

    async rerankContextIds(
        items: SearchEntityWithScore[],
        query: string,
        take: number,
        skip: number,
    ): Promise<string[]> {
        const startTime = new Date().getTime();

        // Format the search entities for the AI prompt
        const content = items
            .map((entity) => {
                return `ID: ${entity.sourceId}\nSource: ${entity.source}\nScore: ${entity.score}\nSearch Type: ${entity.searchType}`;
            })
            .join("\n\n");

        const prompt = `Given the following search query: "${query}"
And these search results:
${content}
Please rerank these results by relevance to the query. Return ONLY the source IDs in order of relevance (most relevant first), separated by commas. Do not return source types, scores, or any other text. Limit to ${take + skip} results.
Example format: abc123,def456,ghi789`;

        try {
            const response = await this.openaiService.getCompletion({
                model: process.env.OPENAI_MODEL,
                messages: [
                    {
                        role: "user",
                        content: prompt,
                    },
                ],
                temperature: 0.1,
                max_tokens: 200,
            });

            const aiResponse = response.choices[0]?.message?.content;

            const rankedIds =
                aiResponse
                    ?.split(",")
                    .map((id) => id.trim())
                    .filter((id) => id.length > 0) || [];

            const rerankTime = new Date().getTime() - startTime;
            console.log(`Reranking execution time: ${rerankTime}ms`);

            // Apply skip and take here
            return rankedIds.slice(skip, skip + take);
        } catch (error) {
            this.logger.error("Error reranking search entities:", error);
            return [];
        }
    }

    async hybridSearch(
        query: string,
        take: number,
        skip: number,
        userId?: string,
        sharedWithGroupIds?: number[],
        bookmarkedOnly?: boolean,
        sources?: DataSource[],
    ): Promise<{ items: SearchEntityWithScore[]; total: number }> {
        const totalStartTime = new Date().getTime();
        console.log(`🚀 Starting hybrid search for query: "${query}"`);

        // Step 1: Semantic Search
        const semanticStartTime = new Date().getTime();
        const { items: semanticItems } = await this.semanticSearch(
            query,
            50,
            0,
            userId,
            sharedWithGroupIds,
            bookmarkedOnly,
            sources,
        );
        const semanticTime = new Date().getTime() - semanticStartTime;
        console.log(
            `🔍 Semantic search completed in ${semanticTime}ms (found ${semanticItems.length} items)`,
        );

        // Step 2: Keyword Search
        const keywordStartTime = new Date().getTime();
        const { items: keywordItems } = await this.keywordSearch(
            query,
            50,
            0,
            userId,
            sharedWithGroupIds,
            bookmarkedOnly,
            sources,
        );
        const keywordTime = new Date().getTime() - keywordStartTime;
        console.log(
            `🔤 Keyword search completed in ${keywordTime}ms (found ${keywordItems.length} items)`,
        );

        // Step 3: Deduplicate
        const dedupeStartTime = new Date().getTime();
        const combinedMap = new Map<string, SearchEntityWithScore>();
        keywordItems.forEach((item) => {
            combinedMap.set(item.sourceId, item);
        });
        semanticItems.forEach((item) => {
            if (!combinedMap.has(item.sourceId)) {
                combinedMap.set(item.sourceId, item);
            }
        });
        const items = Array.from(combinedMap.values());
        const dedupeTime = new Date().getTime() - dedupeStartTime;
        console.log(
            `🔄 Deduplication completed in ${dedupeTime}ms (${items.length} unique items)`,
        );

        if (items.length === 0) {
            return {
                items: [],
                total: 0,
            };
        }

        // Step 4: Reranking
        const rerankStartTime = new Date().getTime();
        const rerankedIds = await this.rerankContextIds(
            items,
            query,
            take,
            skip,
        );
        const rerankTime = new Date().getTime() - rerankStartTime;
        console.log(
            `🎯 Reranking completed in ${rerankTime}ms (${rerankedIds.length} items)`,
        );

        // Step 5: Map back to original entities
        const mappingStartTime = new Date().getTime();
        const rerankedEntities = rerankedIds
            .map((id) => items.find((entity) => entity.sourceId === id))
            .filter(Boolean) as SearchEntityWithScore[];
        const mappingTime = new Date().getTime() - mappingStartTime;
        console.log(`📋 Entity mapping completed in ${mappingTime}ms`);

        const totalTime = new Date().getTime() - totalStartTime;
        console.log(`📊 Performance breakdown:`);
        console.log(
            `   - Semantic search: ${semanticTime}ms (${((semanticTime / totalTime) * 100).toFixed(1)}%)`,
        );
        console.log(
            `   - Keyword search: ${keywordTime}ms (${((keywordTime / totalTime) * 100).toFixed(1)}%)`,
        );
        console.log(
            `   - Deduplication: ${dedupeTime}ms (${((dedupeTime / totalTime) * 100).toFixed(1)}%)`,
        );
        console.log(
            `   - Reranking: ${rerankTime}ms (${((rerankTime / totalTime) * 100).toFixed(1)}%)`,
        );
        console.log(
            `   - Entity mapping: ${mappingTime}ms (${((mappingTime / totalTime) * 100).toFixed(1)}%)`,
        );
        console.log(`✅ Hybrid search completed in ${totalTime}ms total`);

        return {
            items: rerankedEntities,
            total: rerankedEntities.length,
        };
    }

    private safeResolve<T>(
        result: PromiseSettledResult<T>,
        defaultValue: T,
    ): T {
        if (result.status === "rejected") {
            this.logger.error(result.reason);
            return defaultValue;
        }
        return result.value;
    }
}
