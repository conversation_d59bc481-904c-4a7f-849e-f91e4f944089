import {
    Injectable,
    InternalServerErrorException,
    Logger,
} from "@nestjs/common";

import cachified from "@epic-web/cachified";
import { Topic } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { RedisCacheService } from "../cache/redis-cache.service";
import { MICROCHIP_TOPIC_NAME } from "../common/constants";
import { PaginatedResponseDto } from "../generic/dto/paginated.dto";
import { PrismaService } from "../prisma/prisma.service";
import {
    TenantTimelineFilterParams,
    TimelineFilterParams,
    TimelineItemDto,
} from "./dto/timeline.dto";
import { TimelineService } from "./timeline.service";

type TimelineParams = TimelineFilterParams & {
    take: number;
    skip: number;
    userId: string;
};

type TenantTimelineParams = TenantTimelineFilterParams & {
    take: number;
    skip: number;
    userId: string;
};

@Injectable()
export class InsightsService {
    private logger = new Logger("InsightsService");
    constructor(
        private readonly prisma: PrismaService,
        private readonly cls: ClsService,
        private readonly redisCacheService: RedisCacheService,
        private readonly timelineService: TimelineService,
    ) {}

    async getTimeline(
        params: TimelineParams,
    ): Promise<Pick<PaginatedResponseDto<TimelineItemDto>, "items" | "total">> {
        // Cache the timeline across users within a tenant
        const isCachable = !params.bookmarkedOnly; // ...except if we need to filter by the user's bookmarks
        const { items, total } = isCachable
            ? await this.getCachedTimelineWithoutBookmarkStatus(params)
            : await this.getTimelineWithoutBookmarkStatus(params);

        const itemIds = items.map((item) => item.id);

        // Always get fresh data for the user's bookmark status of each timeline item
        const bookmarkStatus = await this.getBookmarkStatus(
            params.userId,
            itemIds,
        );

        const itemsWithBookmarkStatus = items.map((item) => ({
            ...item,
            bookmarked: bookmarkStatus[item.id],
        }));

        return { items: itemsWithBookmarkStatus, total };
    }

    async getTimelineWithoutBookmarkStatus(
        params: TimelineParams,
    ): Promise<Pick<PaginatedResponseDto<TimelineItemDto>, "items" | "total">> {
        const { items, total } =
            await this.timelineService.getTimelineItems(params);

        const itemsWithDetail = await Promise.allSettled(
            items.map(async (item) => ({
                ...item,
                ...(await this.timelineService
                    .getTimelineItemDetails(
                        item.source,
                        item.sourceId,
                        params.userId,
                    )
                    .catch((error) => {
                        this.logger.error(
                            `Error querying details for ${item.source}:${item.sourceId}`,
                            error,
                        );
                        throw error;
                    })),
            })),
        ).then((results) =>
            results
                .map((result) => this.safeResolve(result, null))
                .filter(Boolean),
        );
        return { items: itemsWithDetail, total };
    }

    async getCachedTimelineWithoutBookmarkStatus(
        params: TimelineParams,
    ): Promise<Pick<PaginatedResponseDto<TimelineItemDto>, "items" | "total">> {
        const cacheTtlParams = {
            swr:
                process.env.NODE_ENV === "production"
                    ? // Serve timeline up to 12 hours out of date
                      12 * 60 * 60 * 1000
                    : 0,
            ttl:
                process.env.NODE_ENV === "production"
                    ? // Refresh timeline in the background if more than 30 seconds out of date
                      30 * 1000
                    : -1,
        };

        const tenantId = this.cls.get("tenantId");
        const cachKeyParams = {
            ...params,
            userId: undefined, // Exclude userId to cache across users
            tenantId, // Include tenantId to cache only within the tenant
        };

        // Use filters in cache key
        const paramsKey = Object.entries(cachKeyParams)
            .filter(([key]) =>
                Array.isArray(cachKeyParams[key])
                    ? cachKeyParams[key].length > 0
                    : !!cachKeyParams[key],
            )
            .map(([key, value]) => `${key}=${value}`)
            .join("&");

        return await cachified({
            key: `timeline:${tenantId}:${paramsKey}`,
            cache: this.redisCacheService.cache,
            getFreshValue: () => this.getTimelineWithoutBookmarkStatus(params),
            ...cacheTtlParams,
        });
    }

    async getDefaultTopicForTenant(): Promise<Topic> {
        const tenantId = this.cls.get("tenantId");
        const tenant = await this.prisma.tenant.findUniqueOrThrow({
            where: {
                id: tenantId,
            },
        });

        const defaultTopicName =
            tenantId === "DEFAULT" ? MICROCHIP_TOPIC_NAME : tenant.name;

        const topic = await this.prisma.topic.findFirst({
            where: {
                name: defaultTopicName,
            },
        });

        if (!topic) {
            throw new InternalServerErrorException("Topic not found");
        }

        return topic;
    }

    async getTenantTimeline(
        params: TenantTimelineParams,
    ): ReturnType<typeof this.getTimeline> {
        const { id } = await this.getDefaultTopicForTenant();

        return this.getTimeline({
            ...params,
            topicIds: [id],
        });
    }

    async getTimelineItem(analysisId: number): Promise<TimelineItemDto> {
        const timelineItem = await this.prisma.timelineItem.findUniqueOrThrow({
            where: {
                analysisId,
            },
        });

        const detail = await this.timelineService.getTimelineItemDetails(
            timelineItem.source,
            timelineItem.sourceId,
        );

        return detail;
    }

    async shareWithGroup(userId: string, analysisId: number, groupId: number) {
        await this.prisma.share.create({
            data: {
                analysisId,
                groupId,
                userId,
            },
        });
    }

    async getBookmarkStatus(userId: string, analysisIds: number[]) {
        const { bookmarked } = await this.prisma.user.findUnique({
            where: {
                id: userId,
            },
            include: {
                bookmarked: {
                    where: {
                        id: { in: analysisIds },
                    },
                },
            },
        });

        const bookmarkedAnalysisIds = new Set(bookmarked.map((b) => b.id));

        const bookmarkStatus: Record<number, boolean> = analysisIds.reduce(
            (acc, id) => ({ ...acc, [id]: bookmarkedAnalysisIds.has(id) }),
            {},
        );

        return bookmarkStatus;
    }

    private safeResolve<T>(
        result: PromiseSettledResult<T>,
        defaultValue: T,
    ): T {
        if (result.status === "rejected") {
            this.logger.error(result.reason);
            return defaultValue;
        }
        return result.value;
    }
}
