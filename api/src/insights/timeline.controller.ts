import { <PERSON>, Get, Param, Post, Query, Req, Res } from "@nestjs/common";
import { ApiCreatedResponse, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Request, Response } from "express";

import { PaginatedResponseDto } from "../generic/dto/paginated.dto";
import { paginated } from "../generic/util";
import {
    TenantTimelineRequestDto,
    TimelineItemDto,
    TimelineRequestDto,
} from "./dto/timeline.dto";
import { InsightsService } from "./insights.service";
import { TimelineQueryPipe } from "./pipes/timeline-query.pipe";

@Controller("insights")
@ApiTags("*** insights - timeline")
export class TimelineController {
    constructor(private readonly insightsService: InsightsService) {}

    @Get("timeline")
    @ApiResponse({ type: PaginatedResponseDto<TimelineItemDto> })
    async getTimeline(
        @Req() req: Request,
        @Query(TimelineQueryPipe) query: TimelineRequestDto,
    ): Promise<PaginatedResponseDto<TimelineItemDto>> {
        const userId = req.user["userId"];
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { page: _page, size: _size, ...filters } = query;
        return paginated(
            (take, skip) =>
                this.insightsService.getTimeline({
                    take,
                    skip,
                    userId,
                    ...filters,
                }),
            query,
        );
    }

    @Get("timeline/tenant")
    @ApiResponse({ type: PaginatedResponseDto<TimelineItemDto> })
    async getTenantTimeline(
        @Req() req: Request,
        @Query(TimelineQueryPipe) query: TenantTimelineRequestDto,
    ): Promise<PaginatedResponseDto<TimelineItemDto>> {
        const userId = req.user["userId"];
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { page: _page, size: _size, ...filters } = query;
        return paginated(
            (take, skip) =>
                this.insightsService.getTenantTimeline({
                    take,
                    skip,
                    userId,
                    ...filters,
                }),
            query,
        );
    }

    @Get("timeline/:id")
    @ApiResponse({ type: PaginatedResponseDto<TimelineItemDto> })
    async getTimelineItem(
        @Param("id") id: string,
    ): Promise<PaginatedResponseDto<TimelineItemDto>> {
        const item = await this.insightsService.getTimelineItem(Number(id));

        return {
            items: [item],
            total: 1,
            totalPages: 1,
            page: 1,
            size: 1,
        };
    }

    @Post("timeline/:analysisId/groups/:groupId")
    @ApiCreatedResponse()
    async shareAnalysis(
        @Req() req: Request,
        @Param("analysisId") analysisId: string,
        @Param("groupId") groupId: string,
        @Res() res: Response,
    ): Promise<void> {
        const userId = req.user["userId"];
        await this.insightsService.shareWithGroup(
            userId,
            Number(analysisId),
            Number(groupId),
        );

        res.sendStatus(201);
    }
}
