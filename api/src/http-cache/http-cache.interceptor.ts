import { CacheInterceptor } from "@nestjs/cache-manager";
import { ExecutionContext, Injectable } from "@nestjs/common";

@Injectable()
export class HttpCacheInterceptor extends CacheInterceptor {
    trackBy(context: ExecutionContext): string | undefined {
        const request = context.switchToHttp().getRequest();
        const tenantId = request.user?.tenantId;

        if (!tenantId) {
            return undefined;
        }

        const cacheKeyFields =
            request.headers["accept"] === "application/json"
                ? [tenantId, request.url]
                : [tenantId, request.url, request.headers["accept"]];

        const key = cacheKeyFields.join(":");

        return key;
    }
}
