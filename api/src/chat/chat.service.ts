import { BadRequestException, Injectable, Logger } from "@nestjs/common";

import { DataSource } from "@prisma/client";
import { ClsService } from "nestjs-cls";
import { zodResponsesFunction } from "openai/helpers/zod";
import {
    ParsedResponse,
    ParsedResponseFunctionToolCall,
    ResponseInputItem,
} from "openai/resources/responses/responses";
import { z } from "zod";

import { SearchResultDto } from "../insights/dto/search.dto";
import { TimelineItemDto } from "../insights/dto/timeline.dto";
import { OpenaiService } from "../openai/openai.service";
import { PrismaService } from "../prisma/prisma.service";
import { SearchService } from "./../insights/search.service";
import { SearchParams, SearchSimilarParams } from "./constants";
import {
    ChatRequestDto,
    ChatResponseDto,
    OutputMessageDto,
} from "./dto/chat.dto";
import {
    inputMessageToOpenAIInputItem,
    openAIResponseToOutputMessage,
    responseHasFunctionCalls,
} from "./util";

type FunctionCallProcessingResult = {
    output: ResponseInputItem.FunctionCallOutput;
    analysisIds: string[];
};

type CitationValidationResult =
    | { valid: true }
    | {
          valid: false;
          fallback?: string;
          details?: Record<string, unknown>;
      };

@Injectable()
export class ChatService {
    private readonly logger = new Logger("ChatService");

    constructor(
        private readonly openaiService: OpenaiService,
        private readonly prisma: PrismaService,
        private readonly cls: ClsService,
        private readonly searchService: SearchService,
    ) {}

    async chat(body: ChatRequestDto): Promise<ChatResponseDto> {
        const initialResponse = await this.getInitialResponse(body);

        // Return the first pass response if there are no function calls
        const hasFunctionCalls = responseHasFunctionCalls(initialResponse);
        if (!hasFunctionCalls) {
            return {
                messages: [openAIResponseToOutputMessage(initialResponse)],
            };
        }

        // Process the function calls
        const functionCalls = initialResponse.output.filter(
            (output) => output.type === "function_call",
        );
        const functionCallResults = await Promise.all(
            functionCalls.map((functionCall) =>
                this.processFunctionCall(functionCall)
                    .then((result) => result)
                    .catch((error) => {
                        this.logger.error(
                            `Error processing function call ${functionCall.name} ${functionCall.arguments}`,
                            error,
                        );
                        const errorResponse = this.buildErrorResponse(
                            functionCall,
                            error,
                        );
                        return {
                            output: {
                                call_id: functionCall.call_id,
                                type: "function_call_output",
                                output: JSON.stringify(errorResponse, null, 2),
                            } as ResponseInputItem.FunctionCallOutput,
                            analysisIds: [],
                        } satisfies FunctionCallProcessingResult;
                    }),
            ),
        );

        const aggregatedAnalysisIds = new Set(
            functionCallResults.flatMap((result) => result.analysisIds),
        );

        const functionCallOutputs = functionCallResults.map(
            (result) => result.output,
        );

        const secondPassResponse = await this.openaiService.createResponse({
            previous_response_id: initialResponse.id,
            input: functionCallOutputs,
            stream: false,
            model: process.env["OPENAI_MODEL"],
            tool_choice: "auto",
            temperature: 0.1,
        });

        return this.buildValidatedResponse(
            initialResponse,
            secondPassResponse,
            aggregatedAnalysisIds,
        );
    }

    private async getInitialResponse(body: ChatRequestDto) {
        const searchTool = zodResponsesFunction({
            name: "search",
            parameters: SearchParams,
            description:
                "Search for social media posts and comments by keyword",
        });

        const searchSimilarTool = zodResponsesFunction({
            name: "search_similar",
            parameters: SearchSimilarParams,
            description:
                "Search for social media posts and comments similar to a specific post",
        });

        const chatSystemInstructions = [
            "You are a Truthkeep AI assistant specializing in microchip, electronics, and sensor market analysis.",
            "",
            "CORE PRINCIPLES:",
            "1. Base every response on retrieved data from your tools",
            "2. NEVER fabricate IDs, URLs, dates, or metrics",
            "3. Cite analysisId for every quantitative or data-driven claim using format [Analysis: 123]",
            "4. If insufficient data exists, explicitly state 'Insufficient data available'",
            "",
            "DATA HANDLING:",
            "- You receive structured JSON objects with rich metadata",
            "- Use sentiment scores, narratives, topics, and relevance data to provide context",
            "- Include timeline IDs and source information for traceability",
            "- Preserve exact dates, metrics, and engagement numbers from the data",
            "",
            "RESPONSE FORMAT:",
            "- Start with key insights from narratives and topics",
            "- Provide specific examples with proper citations",
            "- Include sentiment context when relevant (e.g., 'negative sentiment: 25/100')",
            "- End with actionable insights from tipsAndActions when available",
            "",
            "DOMAIN FOCUS:",
            "- Prioritize content about microcontrollers, sensors, PCB design, embedded systems",
            "- Understand technical terminology: PIC, Arduino, ESP32, I2C, SPI, GPIO, etc.",
            "- Recognize part numbers, version numbers, and technical specifications",
            "",
            "UNCERTAINTY HANDLING:",
            "- If data is limited, acknowledge the limitation",
            "- Suggest related topics you do have data for",
            "- Never guess or extrapolate beyond available data",
            "",
            "Use the tools available to you: search, search_similar.",
        ].join("\n");

        const response: ParsedResponse<z.infer<typeof SearchSimilarParams>> =
            await this.openaiService.createResponse({
                previous_response_id: body.previousResponseId,
                input: [
                    {
                        role: "system",
                        content: chatSystemInstructions,
                    },
                    ...body.messages.map(inputMessageToOpenAIInputItem),
                ],
                stream: false,
                model: process.env["OPENAI_MODEL"],
                tools: [searchSimilarTool, searchTool],
                tool_choice: "auto",
            });

        return response;
    }

    private async processFunctionCall(
        functionCall: ParsedResponseFunctionToolCall,
    ): Promise<FunctionCallProcessingResult> {
        this.logger.log(
            `Processing function "${functionCall.name}" with arguments ${functionCall.arguments}`,
        );
        console.log("processing function call", functionCall);

        try {
            const parsedArguments = this.extractFunctionArguments(functionCall);
            const functionName =
                functionCall.name ??
                this.inferFunctionNameFromArguments(parsedArguments);

            if (!functionName) {
                throw new BadRequestException(
                    "Unsupported function: unable to determine function name",
                );
            }

            let searchResults: Awaited<ReturnType<typeof this.search>>;
            if (functionName === "search") {
                const { query } = parsedArguments;
                if (!query) {
                    throw new BadRequestException(
                        "search function requires a query argument",
                    );
                }
                searchResults = await this.search(query);
            } else if (functionName === "search_similar") {
                const { source, id } = parsedArguments;
                if (!source || !id) {
                    throw new BadRequestException(
                        "search_similar requires both source and id arguments",
                    );
                }
                searchResults = await this.searchSimilar(source, id);
            } else {
                throw new BadRequestException(
                    `Unsupported function: ${functionName}`,
                );
            }

            const validItems = this.filterValidSearchItems(searchResults.items);

            if (validItems.length === 0) {
                const emptyResponse = this.buildEmptyStructuredResponse(
                    functionCall,
                    functionName,
                );

                return {
                    output: {
                        call_id: functionCall.call_id,
                        type: "function_call_output",
                        output: JSON.stringify(emptyResponse, null, 2),
                    },
                    analysisIds: [],
                };
            }

            const structuredOutput = this.buildStructuredResponse(
                functionCall,
                functionName,
                validItems,
                searchResults.total ?? validItems.length,
            );

            this.logStructuredOutputTelemetry(
                functionCall,
                functionName,
                structuredOutput,
            );

            return {
                output: {
                    call_id: functionCall.call_id,
                    type: "function_call_output",
                    output: JSON.stringify(structuredOutput, null, 2),
                },
                analysisIds: structuredOutput.items.map((item) =>
                    String(item.analysisId),
                ),
            };
        } catch (error) {
            console.log("error processing function call", error);
            this.logger.error(
                `Error processing function call ${functionCall.name}:`,
                error,
            );

            const errorResponse = this.buildErrorResponse(functionCall, error);

            return {
                output: {
                    call_id: functionCall.call_id,
                    type: "function_call_output",
                    output: JSON.stringify(errorResponse, null, 2),
                },
                analysisIds: [],
            };
        }
    }

    private buildValidatedResponse(
        initialResponse: ParsedResponse<unknown>,
        secondPassResponse: ParsedResponse<unknown>,
        aggregatedAnalysisIds: Set<string>,
    ): ChatResponseDto {
        const baseMessages = [initialResponse, secondPassResponse]
            .map(openAIResponseToOutputMessage)
            .filter(({ content }) => content);

        if (baseMessages.length === 0) {
            return { messages: [] };
        }

        const finalMessage = baseMessages[baseMessages.length - 1];
        const validation = this.validateFinalMessage(
            finalMessage.content,
            aggregatedAnalysisIds,
        );

        if (validation.valid) {
            return { messages: baseMessages };
        }

        const failedValidation = validation as Extract<
            CitationValidationResult,
            { valid: false }
        >;

        this.logger.warn("Final response failed citation validation", {
            details: failedValidation.details ?? {},
        });

        const fallbackContent =
            failedValidation.fallback ??
            this.buildCitationFallbackContent(
                Array.from(aggregatedAnalysisIds),
            );

        const fallbackMessage: OutputMessageDto = {
            id: `${secondPassResponse.id}-fallback`,
            type: "text",
            content: fallbackContent,
        };

        return {
            messages: [...baseMessages.slice(0, -1), fallbackMessage],
        };
    }

    private validateFinalMessage(
        content: string,
        aggregatedAnalysisIds: Set<string>,
    ): CitationValidationResult {
        if (!content?.trim()) {
            return { valid: false, details: { reason: "empty_content" } };
        }

        const citations = this.extractCitations(content);

        if (aggregatedAnalysisIds.size === 0) {
            const mentionsInsufficient = content
                .toLowerCase()
                .includes("insufficient data available");

            if (mentionsInsufficient) {
                return { valid: true };
            }

            return {
                valid: false,
                details: { reason: "no_data_without_acknowledgement" },
            };
        }

        if (citations.length === 0) {
            const requiresCitation = this.containsQuantitativeClaim(content);
            if (!requiresCitation) {
                return { valid: true };
            }

            return {
                valid: false,
                details: {
                    reason: "missing_citations",
                    requiresCitation,
                },
            };
        }

        const unknownCitations = citations.filter(
            (citation) => !aggregatedAnalysisIds.has(citation),
        );

        if (unknownCitations.length > 0) {
            return {
                valid: false,
                details: {
                    reason: "unknown_citations",
                    citations: unknownCitations,
                },
            };
        }

        return { valid: true };
    }

    private extractCitations(content: string) {
        const regex = /\[Analysis:\s*([^\]]+)\]/g;
        const matches = new Set<string>();
        let match: RegExpExecArray | null;

        while ((match = regex.exec(content)) !== null) {
            matches.add(match[1].trim());
        }

        return Array.from(matches);
    }

    private containsQuantitativeClaim(content: string) {
        if (!content) {
            return false;
        }

        if (/\d+(?:[.,]\d+)?%?/.test(content)) {
            return true;
        }

        const lowerContent = content.toLowerCase();
        const keywords = [
            "percent",
            "percentage",
            "score",
            "rating",
            "increase",
            "decrease",
            "growth",
            "decline",
            "rank",
        ];

        return keywords.some((keyword) => lowerContent.includes(keyword));
    }

    private buildCitationFallbackContent(analysisIds: string[]) {
        if (!analysisIds.length) {
            return "Insufficient data available.";
        }

        const limitedIds = analysisIds.slice(0, 5);
        const formatted = limitedIds
            .map((id) => `[Analysis: ${id}]`)
            .join(", ");

        const suffix = analysisIds.length > limitedIds.length ? " …" : "";

        return `Unable to generate a fully cited response. Relevant analyses: ${formatted}${suffix}`;
    }

    private async searchSimilar(source: DataSource, id: string) {
        switch (source) {
            case DataSource.REDDIT_POST:
                return this.searchSimilarByRedditPost(id);
            case DataSource.REDDIT_COMMENT:
                return this.searchSimilarByRedditComment(id);
            case DataSource.YOUTUBE_VIDEO:
                return this.searchSimilarByYoutubeVideo(id);
            case DataSource.YOUTUBE_COMMENT:
                return this.searchSimilarByYoutubeComment(id);
            default:
                throw new BadRequestException(`Unsupported source: ${source}`);
        }
    }

    private async searchSimilarByRedditPost(id: string) {
        const post = await this.prisma.redditPost.findUniqueOrThrow({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id,
                },
            },
        });

        const query = `${post.title}\n${post.text}`;
        return this.search(query);
    }

    private async searchSimilarByRedditComment(id: string) {
        const comment = await this.prisma.redditComment.findUniqueOrThrow({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id,
                },
            },
        });

        const query = `${comment.body}`;
        return this.search(query);
    }

    private async searchSimilarByYoutubeVideo(id: string) {
        const video = await this.prisma.youtubeVideo.findUniqueOrThrow({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id,
                },
            },
        });

        const query = `${video.title}\n${video.subtitles}`;
        return this.search(query);
    }

    private async searchSimilarByYoutubeComment(id: string) {
        const comment = await this.prisma.youtubeComment.findUniqueOrThrow({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id,
                },
            },
        });

        const query = `${comment.body}`;
        return this.search(query);
    }

    private async search(query: string) {
        return this.searchService.search({
            query,
            type: "hybrid",
            take: 5,
            skip: 0,
        });
    }

    private filterValidSearchItems(items: SearchResultDto[]) {
        return items.filter((item) => {
            const hasAnalysisId = Boolean(item?.id);
            const hasExternalUrl = Boolean(item?.externalUrl);

            if (!hasAnalysisId || !hasExternalUrl) {
                this.logger.warn(
                    "Filtering search result missing critical metadata",
                    {
                        hasAnalysisId,
                        hasExternalUrl,
                    },
                );
                return false;
            }

            return true;
        });
    }

    private buildStructuredResponse(
        functionCall: ParsedResponseFunctionToolCall,
        functionName: "search" | "search_similar",
        items: SearchResultDto[],
        totalResults: number,
    ) {
        const queryDescriptor = this.describeFunctionCallQuery(
            functionCall,
            functionName,
        );

        const extendedItems = items.map((item) =>
            this.enrichSearchResult(item),
        );

        return {
            query: queryDescriptor,
            totalResults,
            retrievalMetadata: {
                toolUsed: functionName,
                searchTypes: Array.from(
                    new Set(extendedItems.map((item) => item.searchType)),
                ),
                timestamp: new Date().toISOString(),
            },
            items: extendedItems,
        };
    }

    private enrichSearchResult(item: SearchResultDto) {
        const extendedItem = item as SearchResultDto &
            Partial<{
                isActionable: boolean;
                actionableReasoning: string;
                commentCount: number;
                likeCount: number;
                voteScore: number;
                subredditName: string;
                channelHandle: string;
            }>;

        return {
            analysisId: item.id,
            source: item.type,
            sourceId: item.sourceId,
            externalUrl: item.externalUrl,
            title: item.title,
            summary: item.summary,
            longSummary: item.longSummary?.slice(0, 512) ?? null,
            sentiment: item.sentiment,
            sentimentReasoning: item.sentimentReasoning,
            relevance: item.relevance?.score ?? null,
            relevanceReasoning: item.relevance?.reasoning ?? null,
            narratives: this.normalizeNarratives(item.narratives ?? []),
            topics:
                item.topics?.map((topic) => ({
                    id: topic.id,
                    name: topic.name,
                })) ?? [],
            tags:
                item.tags?.map((tag) => ({
                    id: tag.id,
                    name: tag.name,
                })) ?? [],
            tipsAndActions: item.tipsAndActions,
            isActionable: extendedItem.isActionable ?? null,
            actionableReasoning: extendedItem.actionableReasoning ?? null,
            publishedAt: this.serializeDate(item.publishedAt),
            createdAt: this.serializeDate(item.createdAt),
            updatedAt: this.serializeDate(item.updatedAt),
            commentCount: extendedItem.commentCount ?? null,
            likeCount: extendedItem.likeCount ?? null,
            voteScore: extendedItem.voteScore ?? null,
            replies: item.replies
                ? {
                      count: item.replies.count,
                  }
                : null,
            bookmarked: item.bookmarked ?? false,
            subredditName: extendedItem.subredditName ?? null,
            channelHandle: extendedItem.channelHandle ?? null,
            score: item.score,
            searchType: item.searchType,
        };
    }

    private normalizeNarratives(narratives: TimelineItemDto["narratives"]) {
        return narratives.map((narrative) => ({
            summary: narrative.summary,
            aspect: narrative.aspect,
            sentiment: narrative.sentiment,
            sentimentReasoning: narrative.sentimentReasoning,
            topic: narrative.topic
                ? { id: narrative.topic.id, name: narrative.topic.name }
                : null,
        }));
    }

    private buildEmptyStructuredResponse(
        functionCall: ParsedResponseFunctionToolCall,
        functionName: "search" | "search_similar",
    ) {
        const queryDescriptor = this.describeFunctionCallQuery(
            functionCall,
            functionName,
        );

        return {
            query: queryDescriptor,
            totalResults: 0,
            message: "No matching data found in the knowledge base",
            suggestions: [
                "Try broader search terms",
                "Check spelling of technical terms",
                "Ask about related topics such as Arduino, ESP32, or sensors",
            ],
            availableTopics: [],
        };
    }

    private describeFunctionCallQuery(
        functionCall: ParsedResponseFunctionToolCall,
        functionName: "search" | "search_similar",
    ) {
        const parsed = this.extractFunctionArguments(functionCall);

        if (functionName === "search") {
            return parsed.query ?? "";
        }

        return `Similar to ${parsed.source ?? "unknown"}:${parsed.id ?? "unknown"}`;
    }

    private extractFunctionArguments(
        functionCall: ParsedResponseFunctionToolCall,
    ) {
        const parsed = functionCall.parsed_arguments ?? null;
        const nestedArguments =
            parsed && typeof parsed === "object" && "parsed_arguments" in parsed
                ? (parsed as { parsed_arguments?: unknown }).parsed_arguments
                : parsed;

        const normalizedArguments =
            nestedArguments && typeof nestedArguments === "object"
                ? (nestedArguments as Record<string, unknown>)
                : {};

        const fallbackArguments = this.safeParseFunctionArguments(
            functionCall.arguments,
        );

        const mergedArguments = {
            ...fallbackArguments,
            ...normalizedArguments,
        } as Record<string, unknown>;

        return {
            query:
                typeof mergedArguments.query === "string"
                    ? (mergedArguments.query as string)
                    : undefined,
            source:
                typeof mergedArguments.source === "string"
                    ? (mergedArguments.source as DataSource)
                    : undefined,
            id:
                typeof mergedArguments.id === "string"
                    ? (mergedArguments.id as string)
                    : undefined,
        };
    }

    private inferFunctionNameFromArguments(parsedArguments: {
        query?: string;
        source?: DataSource;
        id?: string;
    }) {
        if (parsedArguments?.query) {
            return "search" as const;
        }

        if (parsedArguments?.source && parsedArguments?.id) {
            return "search_similar" as const;
        }

        return undefined;
    }

    private safeParseFunctionArguments(argumentsJson?: string) {
        if (!argumentsJson) {
            return {};
        }

        try {
            const parsed = JSON.parse(argumentsJson);
            return parsed && typeof parsed === "object"
                ? (parsed as Record<string, unknown>)
                : {};
        } catch (error) {
            this.logger.warn("Failed to parse function call arguments", {
                argumentsJson,
                error,
            });
            return {};
        }
    }

    private serializeDate(value: Date | string | null | undefined) {
        if (!value) {
            return null;
        }

        if (value instanceof Date) {
            if (Number.isNaN(value.getTime())) {
                this.logger.warn("Invalid Date object encountered", {
                    value,
                });
                return null;
            }

            return value.toISOString();
        }

        const parsed = new Date(value);
        if (Number.isNaN(parsed.getTime())) {
            this.logger.warn("Invalid date string encountered", {
                value,
            });
            return null;
        }

        return parsed.toISOString();
    }

    private buildErrorResponse(
        functionCall: ParsedResponseFunctionToolCall,
        error: unknown,
    ) {
        const resolvedFunctionName =
            functionCall.name === "search" ||
            functionCall.name === "search_similar"
                ? functionCall.name
                : "search";

        const queryDescriptor = this.describeFunctionCallQuery(
            functionCall,
            resolvedFunctionName,
        );

        return {
            error: true,
            message: "Failed to retrieve data",
            details:
                error instanceof Error ? error.message : "Unexpected error",
            toolUsed: resolvedFunctionName,
            query: queryDescriptor,
        };
    }

    private logStructuredOutputTelemetry(
        functionCall: ParsedResponseFunctionToolCall,
        functionName: "search" | "search_similar",
        structuredOutput: ReturnType<ChatService["buildStructuredResponse"]>,
    ) {
        const itemCount = structuredOutput.items.length;
        const payloadSize = this.measureJsonSize(structuredOutput);

        this.logger.log(
            `Structured output for ${functionName}: ${itemCount} items (${payloadSize} bytes)`,
        );

        const sampleKeys = structuredOutput.items[0]
            ? Object.keys(structuredOutput.items[0]).join(", ")
            : "none";
        this.logger.debug(
            `Structured output sample fields: ${sampleKeys.substring(0, 200)}`,
        );
    }

    private measureJsonSize(payload: unknown) {
        try {
            return JSON.stringify(payload).length;
        } catch (error) {
            this.logger.warn("Failed to measure structured output size", {
                error,
            });
            return 0;
        }
    }
}
