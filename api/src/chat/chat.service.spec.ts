import { DataSource } from "@prisma/client";
import { ParsedResponseFunctionToolCall } from "openai/resources/responses/responses";

import { ChatService } from "./chat.service";

describe("ChatService structured outputs", () => {
    const mockOpenAiService = {
        createResponse: jest.fn(),
    } as const;
    const mockPrismaService = {} as const;
    const mockClsService = {
        get: jest.fn(),
    } as const;
    const mockSearchService = {
        search: jest.fn(),
    } as const;

    const chatService = new ChatService(
        mockOpenAiService as unknown as ChatService["openaiService"],
        mockPrismaService as unknown as ChatService["prisma"],
        mockClsService as unknown as ChatService["cls"],
        mockSearchService as unknown as ChatService["searchService"],
    );

    const buildFunctionCall = (
        name: "search" | "search_similar",
        payload: Record<string, unknown>,
    ) =>
        ({
            name,
            call_id: "call-1",
            arguments: JSON.stringify(payload),
            parsed_arguments: payload,
            type: "function_call",
        }) as unknown as ParsedResponseFunctionToolCall;

    beforeEach(() => {
        jest.resetAllMocks();
    });

    it("returns rich structured JSON when search results exist", async () => {
        const searchItem = {
            id: 123,
            sourceId: "abc",
            type: DataSource.REDDIT_POST,
            externalUrl: "https://example.com/post",
            title: "ESP32 power issue",
            summary: "User reports power instability",
            longSummary: "Detailed exploration of ESP32 power rail challenges",
            sentiment: 45,
            sentimentReasoning: "mixed feedback",
            relevance: { score: 82, reasoning: "Directly matches query" },
            topics: [],
            tags: [],
            narratives: [],
            tipsAndActions: "Check regulator selection",
            publishedAt: new Date("2024-01-01T00:00:00Z"),
            createdAt: new Date("2024-01-02T00:00:00Z"),
            updatedAt: new Date("2024-01-03T00:00:00Z"),
            replies: { items: [], count: 0 },
            score: 0.12,
            searchType: "semantic",
            commentCount: 3,
            likeCount: 10,
            voteScore: 7,
            bookmarked: false,
        } satisfies Record<string, unknown>;

        mockSearchService.search.mockResolvedValue({
            items: [searchItem],
            total: 1,
        });

        const functionCall = buildFunctionCall("search", {
            query: "ESP32 power",
        });

        const result = await (
            chatService as unknown as {
                processFunctionCall: ChatService["processFunctionCall"];
            }
        ).processFunctionCall(functionCall);

        expect(result.output.type).toBe("function_call_output");
        const payload = JSON.parse(result.output.output);

        expect(payload.query).toBe("ESP32 power");
        expect(payload.totalResults).toBe(1);
        expect(payload.retrievalMetadata.toolUsed).toBe("search");
        expect(payload.retrievalMetadata.searchTypes).toEqual(["semantic"]);
        expect(payload.items).toHaveLength(1);
        expect(payload.items[0].analysisId).toBe(searchItem.id);
        expect(payload.items[0].commentCount).toBe(3);
        expect(payload.items[0].voteScore).toBe(7);
        expect(payload.items[0].publishedAt).toBe("2024-01-01T00:00:00.000Z");
    });

    it("returns empty structured response when no valid results", async () => {
        mockSearchService.search.mockResolvedValue({
            items: [
                {
                    externalUrl: "https://example.com/invalid",
                },
            ],
            total: 0,
        });

        const functionCall = buildFunctionCall("search", {
            query: "unknown topic",
        });

        const result = await (
            chatService as unknown as {
                processFunctionCall: ChatService["processFunctionCall"];
            }
        ).processFunctionCall(functionCall);

        expect(result.output.type).toBe("function_call_output");
        const payload = JSON.parse(result.output.output);

        expect(payload.totalResults).toBe(0);
        expect(payload.message).toContain("No matching data");
        expect(Array.isArray(payload.suggestions)).toBe(true);
    });
});
