import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";

import { Topic } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { HISTORICAL_ANALYSIS_QUEUE } from "../task/constants";
import { HistoricalAnalysisQueue } from "../task/types";

@Injectable()
export class HistoricalAnalysisService {
    constructor(
        private readonly cls: ClsService,
        @InjectQueue(HISTORICAL_ANALYSIS_QUEUE)
        private readonly historicalAnalysisQueue: HistoricalAnalysisQueue,
    ) {}

    async addTopicToHistoricalPosts(topic: Topic): Promise<void> {
        await this.historicalAnalysisQueue.add(HISTORICAL_ANALYSIS_QUEUE, {
            topidId: topic.id,
            tenantId: this.cls.get("tenantId"),
        });
    }
}
