import { Processor, WorkerHost } from "@nestjs/bullmq";

import { NarrativeAspect, Topic } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { UseTenantCls } from "../generic/bullmq/processor";
import { SearchService } from "../insights/search.service";
import { PrismaService } from "../prisma/prisma.service";
import { HISTORICAL_ANALYSIS_QUEUE } from "../task/constants";
import { HistoricalAnalysisJob } from "../task/types";

@Processor(HISTORICAL_ANALYSIS_QUEUE)
export class HistoricalAnalysisProcessor extends WorkerHost {
    constructor(
        private readonly prisma: PrismaService,
        private readonly searchService: SearchService,
        private readonly cls: ClsService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: HistoricalAnalysisJob) {
        const { topidId } = job.data;
        const topic = await this.prisma.topic.findUniqueOrThrow({
            where: {
                id: topidId,
            },
        });

        job.log(`Processing topic: ${topic.name}`);

        const narrative = await this.getNarrative(topic);
        const posts = await this.getPosts(topic);

        for await (const post of posts) {
            const analysis = await this.getAnalysis(post);
            if (!analysis) continue;

            job.log(`${post.source} ${post.sourceId}`);

            await this.prisma.analysisToNarrative
                .create({
                    data: {
                        analysisId: analysis.id,
                        narrativeId: narrative.id,
                        sentiment: -1,
                        sentimentReasoning: "",
                    },
                })
                .catch((error) => {
                    job.log(
                        `Error connecting analysis to narrative: ${error.message}`,
                    );
                });
        }
    }

    private async *getPosts(topic: Topic) {
        const sourceIds = new Set<string>();

        const keywordSearchResults = await this.searchService.keywordSearch(
            topic.name,
            500,
            0,
            "",
        );

        for (const item of keywordSearchResults.items) {
            if (sourceIds.has(item.sourceId)) continue;
            sourceIds.add(item.sourceId);
            yield item;
        }

        const semanticSearchResults = await this.searchService.semanticSearch(
            `"${topic.name}": ${topic.description}`,
            100,
            0,
            "",
        );

        for (const item of semanticSearchResults.items) {
            if (sourceIds.has(item.sourceId)) continue;
            sourceIds.add(item.sourceId);
            yield item;
        }
    }

    private async getNarrative(topic: Topic) {
        // Ensure narrative exists for topic with UNKNOWN aspect
        const existingNarrative = await this.prisma.narrative.findFirst({
            where: {
                aspect: NarrativeAspect.UNKNOWN,
                topicId: topic.id,
            },
        });

        const narrative =
            existingNarrative ||
            (await this.prisma.narrative.create({
                data: {
                    aspect: NarrativeAspect.UNKNOWN,
                    topic: { connect: { id: topic.id } },
                    summary: "",
                },
            }));

        return narrative;
    }

    private async getAnalysis(post: { sourceId: string }) {
        return this.prisma.analysis.findUnique({
            where: {
                tenantId_sourceId: {
                    tenantId: this.cls.get("tenantId"),
                    sourceId: post.sourceId,
                },
            },
        });
    }
}
