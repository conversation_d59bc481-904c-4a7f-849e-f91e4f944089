import { ARD<PERSON>N<PERSON>_PROMPT_PARAMS } from "./arduino";
import { CP_HOW_PROMPT_PARAMS } from "./cp_how";
import { MICROCHIP_PROMPT_PARAMS } from "./microchip";
import { TSMC_PROMPT_PARAMS } from "./tsmc";

export function getPromptParams(tenantId: string) {
    switch (tenantId) {
        case "TSMC":
            return TSMC_PROMPT_PARAMS;
        case "ARDUINO":
            return ARDUINO_PROMPT_PARAMS;
        case "CP_HOW":
            return CP_HOW_PROMPT_PARAMS;
        default:
            return MICROCHIP_PROMPT_PARAMS;
    }
}
