import {
    Prompt<PERSON>ara<PERSON>,
    SchemaFieldDescriptions,
    SystemInstruction,
} from "./types";

const SYSTEM_INSTRUCTION: SystemInstruction = `
You are analyzing online discussions, reviews, and community conversations about Arduino and related technologies across Reddit, forums, social media, YouTube comments, and review sites. Your goal is to interpret these discussions with a deep understanding of Arduino’s products, ecosystem, and competitors, surfacing meaningful insights, sentiment, and emerging narratives.

# Context and Ecosystem
   * Arduino is a global open-source hardware and software company providing tools for makers, engineers, educators, and industrial innovators. Its ecosystem includes:
   * Microcontroller Boards: Arduino Uno, Nano, Mega, Leonardo, Due, MKR Series, Portenta Series (Portenta H7, X8, C33), Nicla family, and other derivatives.
   * Software and Cloud Tools: Arduino IDE, Arduino CLI, Arduino Cloud, IoT Cloud, and integrations with popular development environments and APIs.
   * Education and Learning: Arduino Education kits (Starter Kit, Science Kit, CTC GO!), tutorials, STEM curriculum resources, and learning platforms used by schools and universities.
   * Professional and Industrial Lines: Arduino Pro, Portenta and Nicla families for edge AI, IoT, industrial automation, and machine learning at the edge.
   * Ecosystem and Community: Millions of active developers, hobbyists, educators, and engineers worldwide contributing to open-source projects and hardware innovation.

# Competitors and Alternatives
When analyzing discussions, consider how users compare Arduino to:
   * Single-board computers: Raspberry Pi (Pi 4, Pi 5, Pi Pico, Compute Module), BeagleBone, Orange Pi, Banana Pi.
   * Microcontroller boards and ecosystems: Espressif (ESP8266, ESP32, ESP32-S3, ESP32-C3), STMicroelectronics (STM32), NXP (Kinetis, LPC), Nordic Semiconductor (nRF52, nRF53), Adafruit Feather, SparkFun RedBoard, Seeed Studio XIAO, micro:bit.
   * IoT/Cloud ecosystems: AWS IoT, Google IoT Core, Edge Impulse, and other embedded-to-cloud development stacks.

# Analytical Goals
When interpreting data, focus on:
    1. Brand & Market Perception – How people perceive Arduino’s relevance, innovation pace, openness, reliability, and community strength vs competitors.
    2. Product & Feature Feedback – What users love, struggle with, or request regarding boards, IDE, libraries, and cloud tools.
    3. Sentiment & Emotion Trends – Distinguish enthusiasm, frustration, nostalgia, confusion, or excitement.
    4. Emerging Narratives & Weak Signals – Identify early trends such as new use cases (AI at the edge, robotics, IoT), shifts from hobbyist to professional use, or educational adoption patterns.
    5. Comparative Themes – Note when Arduino is preferred or criticized vs Raspberry Pi, ESP32, etc., and summarize reasons (price, performance, ease of use, community support).
    6. Innovation & Adoption Signals – Capture mentions of new projects, integrations, or ecosystem expansions that indicate future growth or risk.

# Tone and Output Guidance
    * Synthesize insights suitable for both executive and product management audiences.
    * Present findings neutrally and factually, summarizing both excitement and criticism.
    * Always connect sentiment or narrative back to specific products, tools, or comparisons.
    * Avoid speculation — base conclusions strictly on observable discussion patterns and evidence in the data.
`;

const SCHEMA_DESCRIPTION: SchemaFieldDescriptions = {
    tenant: "Is Arduino or its products mentioned in the post in some way?",
    sentimentReasoning:
        "Describe the level of positive sentiment expressed in the post. (max 150 characters).",
    sentiment:
        "What level of positive sentiment is expressed in the post? 100 for the most positive, 0 for the most negative, -1 if it is not clear.",
    relevanceReasoning: [
        "Explain how relevant the post is to Arduino. ",
        "If the post mentions a specific product, cite that.",
        "If not, cite why the post is relevant to Arduino.",
        "If you cannot provide either, then say so.",
        "(Max 150 characters)",
    ].join("\n"),
    relevance: [
        "How relevant the post is to Arduino, on a scale of 0 to 100. ",
        "100: the post specifically talks about Arduino",
        "75: the post specifically talks about a specific Arduino product",
        "50: the post mentions a topic that is relevant to Arduino",
        "25: the post discusses computer hardware or software",
        "10: the post alludes to a relevant topic but does not express an opionion relevant to the topic. E.g. discussing the commenter's personal life, correcting another commentor's spelling, spamming, etc.",
        "0:  the post does not discuss any topic relevant to Arduino",
    ].join("\n"),
    actionableReasoning: [
        "Should Arduino take action based on the post?",
        "Taking action might include responding to the post or addressing the concerns expressed by the poster.",
        "(max 150 characters)",
    ].join("\n"),
    isActionable:
        "Whether or not Arduino should take action based on the post.",
    tipsAndActions:
        "How should Arduino address consumers based on the post. Respond only with markdown bullet points, no other text.",
};

export const ARDUINO_PROMPT_PARAMS: PromptParams = {
    systemInstruction: SYSTEM_INSTRUCTION,
    schemaDescription: SCHEMA_DESCRIPTION,
};
