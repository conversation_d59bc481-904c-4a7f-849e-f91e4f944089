import { BullModule } from "@nestjs/bullmq";
import { Mo<PERSON><PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";

import { InsightsModule } from "../insights/insights.module";
import { OpenaiModule } from "../openai/openai.module";
import {
    AI_ANALYSIS_QUEUE,
    HISTORICAL_ANALYSIS_QUEUE,
} from "../task/constants";
import { AnalysisWriterService } from "./analysis-writer.service";
import { AnalysisController } from "./analysis.controller";
import { AnalysisProcessor } from "./analysis.processor";
import { AnalysisService } from "./analysis.service";
import { CommentLoaderService } from "./comment-loader.service";
import { EmbeddingService } from "./embedding.service";
import { HistoricalAnalysisProcessor } from "./historical-analysis.processor";
import { HistoricalAnalysisService } from "./historical-analysis.service";
import { KeywordService } from "./keyword.service";

@Module({
    imports: [
        BullBoardModule.forFeature(
            {
                name: AI_ANALYSIS_QUEUE,
                adapter: BullMQAdapter,
            },
            {
                name: HISTORICAL_ANALYSIS_QUEUE,
                adapter: BullMQAdapter,
            },
        ),
        BullModule.registerQueue(
            {
                name: AI_ANALYSIS_QUEUE,
            },
            {
                name: HISTORICAL_ANALYSIS_QUEUE,
            },
        ),
        InsightsModule,
        OpenaiModule,
    ],
    controllers: [AnalysisController],
    providers: [
        AnalysisProcessor,
        AnalysisService,
        AnalysisWriterService,
        CommentLoaderService,
        EmbeddingService,
        HistoricalAnalysisProcessor,
        HistoricalAnalysisService,
        KeywordService,
    ],
    exports: [HistoricalAnalysisService],
})
export class AnalysisModule {}
