import { PipeTransform, Injectable, BadRequestException } from "@nestjs/common";

import { PrismaService } from "../../prisma/prisma.service";

@Injectable()
export class GroupFilterPipe implements PipeTransform {
    constructor(private readonly prisma: PrismaService) {}

    // Updates topicIds field to include the topics associated with the given groups, without duplicates
    async transform<T extends { topicIds?: number[]; groupIds?: number[] }>(
        value: T,
    ): Promise<T> {
        const topicIds = value.topicIds || [];
        const groupIds = value.groupIds || [];

        const groupTopics = groupIds?.length
            ? await this.prisma.group.findMany({
                  where: { id: { in: groupIds } },
                  include: {
                      topics: { select: { id: true } },
                      tags: { select: { topics: { select: { id: true } } } },
                  },
              })
            : [];

        if (groupTopics.length != groupIds?.length) {
            throw new BadRequestException("Bad group IDs");
        }

        const groupTopicIds = groupTopics.flatMap((group) => {
            // Topics associated to the group through a tag
            const groupTagTopics = group.tags.flatMap((tag) =>
                tag.topics.map((topic) => topic.id),
            );
            // Topics directly associated to the group
            const groupTopics = group.topics.map((topic) => topic.id);
            return [...groupTopics, ...groupTagTopics];
        });

        const uniqueTopicIds = [...new Set([...topicIds, ...groupTopicIds])];

        return {
            ...value,
            topicIds: uniqueTopicIds,
        };
    }
}
