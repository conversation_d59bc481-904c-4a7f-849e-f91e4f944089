import {
    BadRequestException,
    Body,
    Controller,
    Delete,
    ForbiddenException,
    Get,
    NotFoundException,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
} from "@nestjs/common";
import { ApiNoContentResponse, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Request, Response } from "express";

import { Public } from "../auth/constants";
import { GroupsService } from "../groups/groups.service";
import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { RolesService } from "../roles/roles.service";
import { UserVerificationService } from "../user-verification/user-verification.service";
import { CreateUserRequestDto } from "./dto/create-user.dto";
import { DeleteUsersRequestDto } from "./dto/delete-users.dto";
import { OnboardingDto } from "./dto/get-onboarding.dto";
import { GetAllUsersDto, GetAllUsersResponseDto } from "./dto/get-users.dto";
import { SetPasswordDto } from "./dto/update-onboarding.dto";
import {
    UpdatePasswordRequestDto,
    UpdateUserRequestDto,
} from "./dto/update-user.dto";
import { UserDto } from "./dto/user.dto";
import { UsersService } from "./users.service";

@Controller("users")
@ApiTags("* user management")
export class UsersController {
    constructor(
        private readonly userService: UsersService,
        private readonly userVerificationService: UserVerificationService,
        private readonly rolesService: RolesService,
        private readonly groupsService: GroupsService,
    ) {}

    @Get("me")
    @ApiResponse({ type: UserDto })
    async getProfile(@Req() req: Request): Promise<UserDto> {
        const { roles, groups, ...user } = await this.userService.findById(
            req.user["userId"],
        );
        const isAdmin = !!roles.find(({ role }) => role.name === Role.Admin);

        const flattenedGroups = groups
            .map(({ group }) => group)
            .filter(Boolean);

        return {
            ...user,
            isAdmin,
            groups: flattenedGroups,
            sessionTenantId: req.user["tenantId"],
        };
    }

    @Put("me/password")
    @ApiNoContentResponse()
    async updatePassword(
        @Req() req: Request,
        @Body() updatePasswordRequestDto: UpdatePasswordRequestDto,
        @Res() res: Response,
    ): Promise<void> {
        await this.userService.updatePassword(
            req.user["userId"],
            updatePasswordRequestDto,
        );
        res.sendStatus(204);
    }

    @Post()
    @Roles(Role.Admin)
    @ApiResponse({ status: 201 })
    async create(
        @Req() req: Request,
        @Body() createUserDto: CreateUserRequestDto,
        @Res() res: Response,
    ): Promise<void> {
        const { origin } = req.headers;
        if (!origin) throw new BadRequestException("origin header is required");

        const user = await this.userService.create(createUserDto);
        const userVerification =
            await this.userVerificationService.createUserOnboardingVerification(
                user.id,
            );

        await this.userVerificationService.sendUserOnboardingVerificationEmail(
            user,
            userVerification,
            origin,
        );

        if (createUserDto.isAdmin)
            await this.rolesService.attachRoleToUser(user.id, Role.Admin);

        await this.groupsService.addUserToGroups(user.id, createUserDto.groups);

        res.sendStatus(201);
    }

    @Get("/")
    @Roles(Role.Admin)
    @ApiResponse({ type: GetAllUsersResponseDto })
    async findAll(
        @Query() params: GetAllUsersDto,
    ): Promise<GetAllUsersResponseDto> {
        const { page, size, roles } = params;

        const take = size;
        const skip = (page - 1) * size;

        const { users, totalUsers } = await this.userService.findAll(
            take,
            skip,
            roles,
        );

        const usersWithAdminStatus = users.map(({ roles, ...user }) => {
            const isAdmin = !!roles.find(
                ({ role }) => role.name === Role.Admin,
            );
            return {
                ...user,
                isAdmin,
            };
        });

        const usersWithGroups = usersWithAdminStatus.map(
            ({ groups, ...user }) => {
                const flattenedGroups = groups
                    .map(({ group }) => group)
                    .filter(Boolean);
                return {
                    ...user,
                    groups: flattenedGroups,
                };
            },
        );

        const totalPages = Math.ceil(totalUsers / size);

        if (page > totalPages) throw new NotFoundException();

        return {
            items: usersWithGroups,
            total: totalUsers,
            page,
            size,
            totalPages,
        };
    }

    @Put(":id")
    @ApiResponse({ status: 200, type: UserDto })
    async updateUser(
        @Req() req: Request,
        @Param("id") userId: string,
        @Body() updateUserDto: UpdateUserRequestDto,
    ): Promise<UserDto> {
        const isSelfUpdate = userId === req.user["userId"];
        const requesterHasPermission =
            isSelfUpdate ||
            (await this.rolesService.hasUserRole(
                req.user["userId"],
                Role.Admin,
            ));

        if (!requesterHasPermission) throw new ForbiddenException();

        const user = await this.userService.updateOne(userId, updateUserDto);
        const { id, firstName, lastName, email, groups } = user;
        const isAdmin = await this.rolesService.hasUserRole(userId, Role.Admin);

        const formattedGroups = groups.map(({ group }) => group);

        return {
            id,
            firstName,
            lastName,
            email,
            isAdmin,
            groups: formattedGroups,
        };
    }

    @Delete("/")
    @Roles(Role.Admin)
    @ApiResponse({ status: 204 })
    async delete(
        @Req() req: Request,
        @Body() deleteUsersDto: DeleteUsersRequestDto,
        @Res() res: Response,
    ): Promise<void> {
        const { userIds } = deleteUsersDto;
        if (userIds.includes(req.user["userId"]))
            throw new ForbiddenException();

        for (const userId of userIds) {
            await this.userService.delete(userId);
        }
        res.sendStatus(204);
    }

    @Get(":id/onboarding/:signupCode")
    @Public()
    @ApiResponse({ status: 200, type: OnboardingDto })
    async getOnboardingInfo(
        @Param("id") userId: string,
        @Param("signupCode") signupCode: string,
    ): Promise<OnboardingDto> {
        const { user, userVerification } =
            await this.userService.getOnboardingInfo(userId, signupCode);

        const { email, firstName, lastName, onboardingStep } = user;

        return {
            email,
            firstName,
            lastName,
            onboardingStep,
            signupCode: userVerification.code,
        };
    }

    @Put(":id/onboarding/:signupCode")
    @Public()
    @ApiResponse({ status: 400 })
    @ApiResponse({ status: 204 })
    async completeSignup(
        @Param("id") userId: string,
        @Param("signupCode") signupCode: string,
        @Body() setPasswordDto: SetPasswordDto,
        @Res() res: Response,
    ): Promise<void> {
        await this.userService.completeOnboarding(
            userId,
            signupCode,
            setPasswordDto,
        );
        res.sendStatus(204);
    }
}
