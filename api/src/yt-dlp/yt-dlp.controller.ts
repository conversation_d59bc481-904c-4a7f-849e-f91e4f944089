import { Controller, Get, Body, Put, Res, Delete } from "@nestjs/common";
import {
    ApiBody,
    ApiConsumes,
    ApiNoContentResponse,
    ApiOkResponse,
    ApiOperation,
    ApiResponse,
    ApiTags,
} from "@nestjs/swagger";

import { Response } from "express";

import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { YtDlpService } from "./yt-dlp.service";

@Controller("yt-dlp")
@ApiTags("background jobs - yt-dlp config")
export class YtDlpController {
    constructor(private readonly ytDlpService: YtDlpService) {}

    @Put("proxy")
    @Roles(Role.Admin)
    @ApiConsumes("text/plain")
    @ApiOperation({
        summary: "Set proxy URL for yt-dlp",
        description: "https://github.com/yt-dlp/yt-dlp#network-options",
    })
    @ApiBody({
        schema: {
            type: "string",
            example: "socks5://8.tcp.ngrok.io:18085",
        },
    })
    @ApiNoContentResponse()
    async setProxyUrl(
        @Body() proxyUrl: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.ytDlpService.setProxyUrl(proxyUrl);
        res.sendStatus(204);
    }

    @Get("proxy")
    @Roles(Role.Admin)
    @ApiResponse({ status: 200 })
    async getProxyUrl(): Promise<string> {
        return this.ytDlpService.getProxyUrl();
    }

    @Delete("proxy")
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    async clearProxyUrl(@Res() res: Response): Promise<void> {
        await this.ytDlpService.clearProxyUrl();
        res.sendStatus(204);
    }

    @Put("cookies")
    @ApiConsumes("text/plain")
    @ApiNoContentResponse()
    @ApiOperation({
        summary: "Set cookies for yt-dlp",
        description:
            "CLOUDFRONT MAY BLOCK THIS REQUEST IF YOUR COOKIES FILE IS LARGER THAN 8kb.",
    })
    @ApiBody({
        schema: {
            type: "string",
            example:
                "See github.com/yt-dlp/yt-dlp/wiki/FAQ#how-do-i-pass-cookies-to-yt-dlp",
        },
    })
    async setCookies(
        @Body() body: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.ytDlpService.setCookies(body);
        res.sendStatus(204);
    }

    @Get("cookies")
    @ApiOkResponse()
    async getCookies(): Promise<string> {
        const cacheEntry = await this.ytDlpService.getCookies();
        return cacheEntry?.value;
    }

    @Delete("cookies")
    @ApiOkResponse()
    async clearCookies(@Res() res: Response): Promise<void> {
        await this.ytDlpService.clearCookies();
        res.sendStatus(204);
    }
}
