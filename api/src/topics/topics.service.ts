import { BadRequestException, Injectable } from "@nestjs/common";

import { Prisma } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { PrismaService } from "../prisma/prisma.service";
import { CreateTopicRequestDto } from "./dto/create-topic.dto";
import { TopicFilterParams } from "./dto/get-topics.dto";
import { BulkPatchRequestDto } from "./dto/patch-topic-preferences.dto";
import { UpdateTopicRequestDto } from "./dto/update-topic.dto";

// Going over 500 topics will cause issues with the AI analysis
// https://platform.openai.com/docs/guides/structured-outputs/supported-schemas?api-mode=responses#limitations-on-enum-size
const MAX_TOPICS = 500;

@Injectable()
export class TopicsService {
    constructor(
        private readonly prismaService: PrismaService,
        private readonly cls: ClsService<{ tenantId: string }>,
    ) {}

    async createTopic(
        createTopicRequestDto: CreateTopicRequestDto,
        userId: string,
    ) {
        await this.assertTopicLimitNotExceeded();

        const { name, description, userTopicPreference } =
            createTopicRequestDto;

        const topic = await this.prismaService.topic.create({
            data: {
                name,
                description,
                createdBy: {
                    connect: {
                        id: userId,
                    },
                },
                tags: {
                    connect: createTopicRequestDto.tags.map((id) => ({
                        id,
                    })),
                },
                groups: {
                    connect: createTopicRequestDto.groups.map((groupName) => ({
                        tenantId_name: {
                            tenantId: this.cls.get("tenantId"),
                            name: groupName,
                        },
                    })),
                },
                userTopicPreference: {
                    create: {
                        userId,
                        ...userTopicPreference,
                    },
                },
            },
            include: {
                tags: true,
                groups: true,
                userTopicPreference: true,
            },
        });

        return {
            ...topic,
            userTopicPreference: topic.userTopicPreference[0],
        };
    }

    async updateTopic(
        topicId: number,
        updateTopicRequestDto: UpdateTopicRequestDto,
        userId: string,
    ) {
        // TODO - put this all in a transaction
        const { name, description, tags, groups, userTopicPreference } =
            updateTopicRequestDto;

        await this.prismaService.topic.update({
            where: { id: topicId },
            data: {
                name,
                description,
                groups: {
                    set: groups.map((groupName) => ({
                        tenantId_name: {
                            tenantId: this.cls.get("tenantId"),
                            name: groupName,
                        },
                    })),
                },
                userTopicPreference: {
                    upsert: {
                        where: { topicId_userId: { userId, topicId } },
                        create: {
                            ...userTopicPreference,
                            userId,
                        },
                        update: userTopicPreference,
                    },
                },
            },
        });

        await this.updateTopicTags(topicId, tags, userId);

        // Clear old embedding
        await this.prismaService.$executeRaw`
            UPDATE "Topic"
            SET embedding = NULL
            WHERE id = ${topicId};
        `;

        const updatedTopic = await this.prismaService.topic.findUnique({
            where: { id: topicId },
            include: {
                tags: true,
                groups: true,
                userTopicPreference: true,
            },
        });

        return {
            ...updatedTopic,
            tags: updatedTopic.tags.filter(
                // Hide other user's personaltags
                (tag) => !tag.isPersonal || tag.createdByUserId === userId,
            ),
            userTopicPreference: updatedTopic.userTopicPreference[0],
        };
    }

    async bulkPatch(bulkPatchRequestDto: BulkPatchRequestDto, userId: string) {
        await this.prismaService.$transaction(
            bulkPatchRequestDto.items.map(
                ({
                    id: topicId,
                    isFavourite,
                    notifications,
                    highRel,
                    negSentiment,
                }) => {
                    const notificationCadence = notifications;
                    const highRelevanceNotifications = highRel;
                    const negativeSentimentNotifications = negSentiment;

                    return this.prismaService.userTopicPreference.upsert({
                        where: {
                            topicId_userId: {
                                topicId,
                                userId,
                            },
                        },
                        update: {
                            isFavourite,
                            notificationCadence,
                            highRelevanceNotifications,
                            negativeSentimentNotifications,
                        },
                        create: {
                            user: { connect: { id: userId } },
                            topic: { connect: { id: topicId } },
                            isFavourite,
                            notificationCadence,
                            highRelevanceNotifications,
                            negativeSentimentNotifications,
                        },
                    });
                },
            ),
        );
    }

    async updateTopicGroups(id: number, groupNames: string[]) {
        const { groups } = await this.prismaService.topic.update({
            where: { id },
            include: {
                groups: true,
            },
            data: {
                groups: {
                    set: groupNames.map((groupName) => ({
                        tenantId_name: {
                            tenantId: this.cls.get("tenantId"),
                            name: groupName,
                        },
                    })),
                },
            },
        });

        return groups;
    }

    async updateTopicTags(topicId: number, tagIds: number[], userId: string) {
        await this.prismaService.$transaction(async (tx) => {
            // Delete all tag associations for this topic, except other users' personal tags
            await tx.$executeRaw`
                DELETE FROM "_TagToTopic"
                WHERE "B" = ${topicId}
                AND "A" NOT IN (
                    SELECT id FROM "Tag" WHERE "isPersonal" = true AND "createdByUserId" != ${userId}
                )
            `;

            // Set tag associations for this topic, if any
            if (tagIds.length === 0) return;
            await tx.$executeRaw`
                INSERT INTO "_TagToTopic" ("A", "B")
                VALUES ${Prisma.join(
                    tagIds.map((tagId) => Prisma.sql`(${tagId}, ${topicId})`),
                    ",",
                )}
                `;
        });

        const updatedTopic = await this.prismaService.topic.findUnique({
            where: { id: topicId },
            include: {
                tags: true,
            },
        });

        return updatedTopic.tags;
    }

    async getTopics(
        take: number,
        skip: number,
        userId: string,
        filterParams: TopicFilterParams = {},
    ) {
        const { tagIds, groupIds, onlyFavourited } = filterParams;

        const tagFilter: Prisma.TopicWhereInput =
            tagIds?.length > 0
                ? {
                      tags: { some: { id: { in: tagIds } } },
                  }
                : {};

        const groupFilter: Prisma.TopicWhereInput =
            groupIds?.length > 0
                ? {
                      groups: { some: { id: { in: groupIds } } },
                  }
                : {};

        const favouriteFilter: Prisma.TopicWhereInput = onlyFavourited
            ? {
                  userTopicPreference: { some: { userId, isFavourite: true } },
              }
            : {};

        const sqlFilters: Prisma.TopicWhereInput = {
            ...tagFilter,
            ...groupFilter,
            ...favouriteFilter,
        };

        const topics = await this.prismaService.topic.findMany({
            where: sqlFilters,
            include: {
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                tags: {
                    where: {
                        OR: [
                            {
                                isPersonal: false,
                            },
                            {
                                isPersonal: true,
                                createdByUserId: userId,
                            },
                        ],
                    },
                },
                groups: true,
                userTopicPreference: {
                    where: {
                        userId,
                    },
                    select: {
                        isFavourite: true,
                        notificationCadence: true,
                        highRelevanceNotifications: true,
                        negativeSentimentNotifications: true,
                    },
                },
            },
            omit: {
                userId: true,
            },
            take,
            skip,
            orderBy: {
                createdAt: "desc",
            },
        });

        const total = await this.prismaService.topic.count({
            where: sqlFilters,
        });

        const formattedTopics = topics.map(
            ({ userTopicPreference: [userTopicPreference], ...topic }) => ({
                ...topic,
                userTopicPreference,
            }),
        );

        return { items: formattedTopics, total };
    }

    async deleteTopics(topicIds: number[]) {
        await this.prismaService.topic.deleteMany({
            where: {
                id: {
                    in: topicIds,
                },
            },
        });
    }

    private async assertTopicLimitNotExceeded() {
        const numExistingTopics = await this.prismaService.topic.count();

        if (numExistingTopics >= MAX_TOPICS) {
            throw new BadRequestException(
                "You have reached the maximum number of topics",
            );
        }
    }
}
