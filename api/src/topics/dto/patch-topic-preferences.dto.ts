import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { NotificationCadence } from "@prisma/client";
import { Type } from "class-transformer";
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsNumber,
    IsOptional,
    ValidateNested,
} from "class-validator";

class BulkPatchRequestItem {
    @Type(() => Number)
    @IsNumber()
    id: number;

    @IsBoolean()
    @IsOptional()
    isFavourite?: boolean;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    notifications?: NotificationCadence;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    highRel?: NotificationCadence;

    @IsEnum(NotificationCadence)
    @IsOptional()
    @ApiPropertyOptional({ example: "OFF" })
    negSentiment?: NotificationCadence;
}

export class BulkPatchRequestDto {
    @ApiProperty({ type: () => BulkPatchRequestItem, isArray: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => BulkPatchRequestItem)
    items: BulkPatchRequestItem[];
}
