import { ApiPropertyOptional, IntersectionType } from "@nestjs/swagger";

import { Transform } from "class-transformer";
import { IsBoolean, IsNumber, IsOptional } from "class-validator";

import {
    GroupFilteredRequestDto,
    TagFilteredParams,
    TagFilteredRequestDto,
    GroupFilteredParams,
} from "../../common/dto/filtered-request.dto";
import { PaginatedRequestDto } from "../../generic/dto/paginated.dto";

export type TopicFilterParams = TagFilteredParams &
    GroupFilteredParams & {
        onlyFavourited?: boolean;
    };

export class GetTopicsRequestDto
    extends IntersectionType(
        PaginatedRequestDto,
        TagFilteredRequestDto,
        GroupFilteredRequestDto,
    )
    implements TopicFilterParams
{
    @IsNumber()
    size: number = 500;

    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    @ApiPropertyOptional({ default: false })
    onlyFavourited?: boolean;
}
