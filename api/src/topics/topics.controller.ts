import {
    Body,
    Controller,
    Logger,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    Put,
    Query,
    Req,
    Res,
} from "@nestjs/common";
import {
    ApiBody,
    ApiCreatedResponse,
    ApiNoContentResponse,
    ApiOkResponse,
    ApiOperation,
    ApiTags,
} from "@nestjs/swagger";

import { Request, Response } from "express";

import { HistoricalAnalysisService } from "../analysis/historical-analysis.service";
import { PaginatedResponseDto } from "../generic/dto/paginated.dto";
import { paginated } from "../generic/util";
import { GroupDto } from "../groups/dto/get-groups.dto";
import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import { TagDto } from "../tags/dto/get-tags.dto";
import {
    CreateTopicRequestDto,
    CreateTopicResponseDto,
} from "./dto/create-topic.dto";
import { DeleteTopicsRequestDto } from "./dto/delete-topics.dto";
import { GetTopicsRequestDto } from "./dto/get-topics.dto";
import { BulkPatchRequestDto } from "./dto/patch-topic-preferences.dto";
import { TopicDto } from "./dto/topic.dto";
import {
    UpdateTopicRequestDto,
    UpdateTopicResponseDto,
} from "./dto/update-topic.dto";
import { TopicsService } from "./topics.service";

@Controller("topics")
@ApiTags("** topic management - topics")
export class TopicsController {
    private readonly logger = new Logger("TopicsController");

    constructor(
        private readonly topicsService: TopicsService,
        private readonly historicalAnalysisService: HistoricalAnalysisService,
    ) {}

    @Get()
    @ApiOkResponse({ type: PaginatedResponseDto<TopicDto> })
    async getTopics(
        @Req() req: Request,
        @Query() params: GetTopicsRequestDto,
    ): Promise<PaginatedResponseDto<TopicDto>> {
        return paginated(
            (take, skip) =>
                this.topicsService.getTopics(
                    take,
                    skip,
                    req.user["userId"],
                    params,
                ),
            params,
        );
    }

    @Post()
    @ApiCreatedResponse({ type: CreateTopicResponseDto })
    async createTopic(
        @Req() req: Request,
        @Body() createTopicRequestDto: CreateTopicRequestDto,
    ): Promise<CreateTopicResponseDto> {
        const topic = await this.topicsService.createTopic(
            createTopicRequestDto,
            req.user["userId"],
        );

        await this.historicalAnalysisService
            .addTopicToHistoricalPosts(topic)
            .catch((error) => {
                this.logger.error(
                    `Error adding topic ${topic.name} to historical posts`,
                    error,
                );
            });

        return topic;
    }

    @Post("bulk")
    @ApiCreatedResponse({ type: [CreateTopicResponseDto] })
    async createTopics(
        @Req() req: Request,
        @Body() topicNames: string[] = [],
    ): Promise<CreateTopicResponseDto[]> {
        for (const topicName of topicNames) {
            await this.topicsService.createTopic(
                {
                    name: topicName,
                    description: "",
                    tags: [],
                    groups: [],
                    userTopicPreference: {},
                },
                req.user["userId"],
            );
        }
        return [];
    }

    @Put(":id")
    @ApiOkResponse({ type: CreateTopicResponseDto })
    async updateTopic(
        @Req() req: Request,
        @Param("id") id: string,
        @Body() updateTopicRequestDto: UpdateTopicRequestDto,
    ): Promise<UpdateTopicResponseDto> {
        const userId = req.user["userId"];
        return this.topicsService.updateTopic(
            Number(id),
            updateTopicRequestDto,
            userId,
        );
    }

    @Put(":id/groups")
    @ApiOkResponse({ type: [GroupDto] })
    async updateTopicGroups(
        @Param("id") id: string,
        @Body() groups: string[],
    ): Promise<GroupDto[]> {
        return this.topicsService.updateTopicGroups(Number(id), groups);
    }

    @Put(":id/tags")
    @ApiOkResponse({ type: [TagDto] })
    @ApiBody({ type: [Number] })
    async updateTopicTags(
        @Param("id") id: string,
        @Body() tags: number[],
        @Req() req: Request,
    ): Promise<TagDto[]> {
        const userId = req.user["userId"];
        return this.topicsService.updateTopicTags(Number(id), tags, userId);
    }

    @Patch("preferences")
    @ApiOperation({
        summary:
            "Update favourite status or notification prefences for a topic(s)",
    })
    @ApiBody({
        type: BulkPatchRequestDto,
        examples: {
            favourite: {
                summary: "Update favourite status for a topic",
                value: {
                    items: [
                        {
                            id: 1,
                            isFavourite: true,
                        },
                    ],
                },
            },
            updateNotificationPreferences: {
                summary: "Update notification preferences for several topics",
                value: {
                    items: [
                        {
                            id: 1,
                            notifications: "WEEKLY",
                            highRel: "MONTHLY",
                            negSentiment: "DAILY",
                        },
                        {
                            id: 2,
                            notifications: "OFF",
                        },
                    ],
                },
            },
        },
    })
    @ApiNoContentResponse()
    async patchTopics(
        @Req() req: Request,
        @Body() bulkPatchRequestDto: BulkPatchRequestDto,
        @Res() res: Response,
    ): Promise<void> {
        await this.topicsService.bulkPatch(
            bulkPatchRequestDto,
            req.user["userId"],
        );
        res.sendStatus(204);
    }

    @Delete()
    @Roles(Role.Admin)
    @ApiNoContentResponse()
    async deleteTopics(
        @Body() deleteTopicsRequestDto: DeleteTopicsRequestDto,
        @Res() res: Response,
    ): Promise<void> {
        await this.topicsService.deleteTopics(deleteTopicsRequestDto.topicIds);
        res.sendStatus(204);
    }
}
