import { <PERSON>du<PERSON> } from "@nestjs/common";

import { AnalysisModule } from "../analysis/analysis.module";
import { OpenaiModule } from "../openai/openai.module";
import { TopicsController } from "./topics.controller";
import { TopicsService } from "./topics.service";

@Module({
    imports: [AnalysisModule, OpenaiModule],
    controllers: [TopicsController],
    providers: [TopicsService],
    exports: [TopicsService],
})
export class TopicsModule {}
