import { Injectable } from "@nestjs/common";

import { Prisma } from "@prisma/client";
import { ClsService } from "nestjs-cls";

import { PrismaService } from "../prisma/prisma.service";
import { CreateTenantDto } from "./dto/create-tenant.dto";

type ClsStore = {
    tenantId: string;
};

@Injectable()
export class TenantsService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly cls: ClsService<ClsStore>,
    ) {}

    async createTenant(createTenantDto: CreateTenantDto, userId: string) {
        const tenant = await this.prisma.tenant.create({
            data: {
                ...createTenantDto,
                users: {
                    connect: {
                        id: userId,
                    },
                },
            },
        });

        // Create default topic
        await this.cls.runWith({ tenantId: tenant.id }, async () =>
            this.prisma.topic.create({
                data: {
                    name: createTenantDto.name,
                },
            }),
        );

        return tenant;
    }

    async getTenants(take: number, skip: number) {
        const items = await this.prisma.tenant.findMany({
            skip,
            take,
        });

        const total = await this.prisma.tenant.count();

        return {
            items,
            total,
        };
    }

    async getTenantUsers(tenantId: string, take: number, skip: number) {
        const tenantFilter: Prisma.UserWhereInput = {
            tenants: {
                some: { id: tenantId },
            },
        };

        const items = await this.prisma.user.findMany({
            skip,
            take,
            where: { ...tenantFilter },
        });

        const total = await this.prisma.user.count({
            where: { ...tenantFilter },
        });

        return {
            items,
            total,
        };
    }

    async runForAllTenants(fn: () => Promise<void>) {
        const tenants = await this.prisma.tenant.findMany();

        for (const { id } of tenants) {
            await this.cls.runWith({ tenantId: id }, fn);
        }
    }
}
