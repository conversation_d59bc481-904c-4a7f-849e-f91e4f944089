import { InjectQueue, Processor, WorkerHost } from "@nestjs/bullmq";

import cachified from "@epic-web/cachified";
import { Job } from "bullmq";
import * as fs from "fs/promises";
import { ClsService } from "nestjs-cls";

import { CacheService } from "../cache/cache.service";
import { UseTenantCls } from "../generic/bullmq/processor";
import { PrismaService } from "../prisma/prisma.service";
import { AI_ANALYSIS_QUEUE, YOUTUBE_VIDEO_QUEUE } from "../task/constants";
import { AIAnalysisQueue, YoutubeVideoJob } from "../task/types";
import { YoutubeApiService } from "../youtube-api/youtube-api.service";
import { YoutubeSubtitlesService } from "../youtube-subtitles/youtube-subtitles.service";
import { YtDlpService } from "../yt-dlp/yt-dlp.service";
import { ManifestURL, SubtitleURL } from "./types";
import { getCleanFlatComments } from "./util/comments";
import {
    cleanSubtitles,
    isValidSubtitleContent,
} from "./util/subtitles-cleaning";
import {
    extractSubtitleURLFromManifest,
    isManifestURL,
    isSubtitleURL,
} from "./util/subtitles-fetching";
import { YoutubeService } from "./youtube.service";

@Processor(YOUTUBE_VIDEO_QUEUE)
export class YoutubeVideoProcessor extends WorkerHost {
    constructor(
        private readonly youtubeApiService: YoutubeApiService,
        private readonly youtubeService: YoutubeService,
        private readonly youtubeSubtitlesService: YoutubeSubtitlesService,
        private readonly ytDlpService: YtDlpService,
        private readonly prisma: PrismaService,
        private readonly cacheService: CacheService,
        @InjectQueue(AI_ANALYSIS_QUEUE)
        private readonly aiAnalysisQueue: AIAnalysisQueue,
        private readonly cls: ClsService,
    ) {
        super();
    }

    @UseTenantCls()
    async process(job: YoutubeVideoJob) {
        const { youtubeId } = job.data;

        job.log("Fetching video statistics");
        const statistics =
            await this.youtubeService.fetchVideoStatistics(youtubeId);
        await this.prisma.youtubeVideo.update({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id: youtubeId,
                },
            },
            data: {
                ...statistics,
            },
        });
        const { viewCount, likeCount, commentCount } = statistics;
        job.log(
            `${viewCount} views, ${commentCount} comments, ${likeCount} likes`,
        );

        job.log("Fetching video comment threads");
        const commentThreads =
            await this.youtubeService.getCommentThreads(youtubeId);
        const comments = getCleanFlatComments(commentThreads);

        for (const comment of comments) {
            await this.prisma.youtubeComment.upsert({
                where: {
                    tenantId_id: {
                        tenantId: this.cls.get("tenantId"),
                        id: comment.id,
                    },
                },
                create: comment,
                update: comment,
            });
        }

        const [firstComment] = comments || [];
        const firstCommentText = firstComment?.body;
        if (firstCommentText) {
            job.log(
                `Processing ${comments.length} comments. First comment: "${firstCommentText}"`,
            );
        } else {
            job.log("No comments yet");
        }

        job.log("Downloading video subtitles");

        const proxyUrl = await this.ytDlpService.getProxyUrl();
        const subtitleOrManifestURL = await this.getSubtitleOrManifestURL(
            job,
            proxyUrl,
        );
        const manifest = await this.getManifest(
            subtitleOrManifestURL,
            proxyUrl,
        );
        const subtitleURL = extractSubtitleURLFromManifest(manifest);

        const isValidSubtitleURL = isSubtitleURL(subtitleURL);
        if (!isValidSubtitleURL) {
            job.log(`Invalid subtitle URL: "${subtitleURL}"`);
        }

        const rawSubtitles = isValidSubtitleURL
            ? await this.getRawSubtitles(subtitleURL as SubtitleURL, proxyUrl)
            : "";

        if (!isValidSubtitleContent(rawSubtitles)) {
            throw new Error(`Invalid subtitles: ${rawSubtitles}`);
        }

        const subtitles = cleanSubtitles(rawSubtitles);

        if (!(subtitles?.length > 0)) throw new Error("No subtitles found");

        job.log(`"${subtitles.slice(0, 250)}..."`);

        await this.prisma.youtubeVideo.update({
            where: {
                tenantId_id: {
                    tenantId: this.cls.get("tenantId"),
                    id: youtubeId,
                },
            },
            data: {
                subtitles,
                commentCount,
            },
        });

        await this.aiAnalysisQueue.add(job.name, {
            type: "youtube",
            ...job.data,
        });
    }

    private async getSubtitleOrManifestURL(
        job: Job,
        proxyUrl?: string,
    ): Promise<string> {
        const { videoURL } = job.data;
        const ytDlpCookies = await this.ytDlpService.getCookies();
        const cookies = ytDlpCookies?.value;

        // Write yt-dlp cookies to file for yt-dlp command to access
        if (cookies) {
            job.log(`Writing cookies file.`);
            await fs.writeFile("cookies.txt", cookies);
        } else {
            job.log("Found no cookies for yt-dlp");
        }

        if (proxyUrl) job.log(`Using proxy URL: ${proxyUrl}`);

        const subtitleOrManifestURL = await cachified({
            key: `youtube-subtitle-or-manifest-url-${videoURL}`,
            cache: this.cacheService.cache,
            getFreshValue: () =>
                this.ytDlpService.getSubtitleURL(videoURL, !!cookies, proxyUrl),
        });

        if (ytDlpCookies) {
            // Persist changes to cookies value by yt-dlp command
            const updatedCookies = await fs
                .readFile("cookies.txt")
                .catch((error) => {
                    job.log(`Failed to read updated cookies file: ${error}`);
                    return "";
                });
            this.ytDlpService.setCookies(updatedCookies.toString());
        }

        return subtitleOrManifestURL;
    }

    private async getManifest(
        subtitleOrManifestURL: string,
        proxyUrl?: string,
    ): Promise<string> {
        const { subtitleURL, manifestURL } = isManifestURL(
            subtitleOrManifestURL,
        )
            ? { manifestURL: subtitleOrManifestURL }
            : { subtitleURL: subtitleOrManifestURL };

        if (subtitleURL) {
            // The URL was already a subtitle URL, so itself as a string works as a manifest file
            return subtitleURL;
        }

        if (!isManifestURL(manifestURL)) return "";

        const manifest = await cachified({
            key: manifestURL,
            cache: this.cacheService.cache,
            getFreshValue: () =>
                this.youtubeSubtitlesService.getSubtitleManifest(
                    manifestURL as ManifestURL,
                    proxyUrl,
                ),
        });

        return manifest;
    }

    private async getRawSubtitles(
        subtitleURL: SubtitleURL,
        proxyUrl?: string,
    ): Promise<string> {
        return cachified({
            key: subtitleURL,
            cache: this.cacheService.cache,
            getFreshValue: () =>
                this.youtubeSubtitlesService.getSubtitles(
                    subtitleURL,
                    proxyUrl,
                ),
        });
    }
}
