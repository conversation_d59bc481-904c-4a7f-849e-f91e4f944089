import { BullModule } from "@nestjs/bullmq";
import { Modu<PERSON> } from "@nestjs/common";
import { ServeStaticModule } from "@nestjs/serve-static";

import { ClsModule } from "nestjs-cls";
import { join } from "path";

import { AllAboutCircuitsModule } from "./all-about-circuits/all-about-circuits.module";
import { AnalysisModule } from "./analysis/analysis.module";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { AuthModule } from "./auth/auth.module";
import { SecureBullBoardModule } from "./bullboard/secure-bullboard.module";
import { ChatModule } from "./chat/chat.module";
import { EmailModule } from "./email/email.module";
import "./env";
import { GroupsModule } from "./groups/groups.module";
import { HttpCacheModule } from "./http-cache/http-cache.module";
import { InsightsModule } from "./insights/insights.module";
import { NarrativesModule } from "./narratives/narratives.module";
import { NotificationsModule } from "./notifications/notifications.module";
import { OpenaiModule } from "./openai/openai.module";
import { PrismaRootModule } from "./prisma-root/prisma-root.module";
import { PrismaModule } from "./prisma/prisma.module";
import { RedditModule } from "./reddit/reddit.module";
import { RolesModule } from "./roles/roles.module";
import { SalesforceApiModule } from "./salesforce-api/salesforce-api.module";
import { SalesforceModule } from "./salesforce/salesforce.module";
import { TagsModule } from "./tags/tags.module";
import { TaskModule } from "./task/task.module";
import { TenantsModule } from "./tenants/tenants.module";
import { TopicsModule } from "./topics/topics.module";
import { TruthkeepAnalyticsModule } from "./truthkeep-analytics/truthkeep-analytics.module";
import { UserVerificationModule } from "./user-verification/user-verification.module";
import { UsersModule } from "./users/users.module";
import { YoutubeApiModule } from "./youtube-api/youtube-api.module";
import { YoutubeSubtitlesModule } from "./youtube-subtitles/youtube-subtitles.module";
import { YoutubeModule } from "./youtube/youtube.module";
import { YtDlpModule } from "./yt-dlp/yt-dlp.module";

@Module({
    imports: [
        AllAboutCircuitsModule,
        AnalysisModule,
        AuthModule,
        BullModule.forRoot({
            connection: {
                host: process.env.REDIS_HOST,
                port: process.env.REDIS_PORT,
                dnsLookup: (address, callback) => callback(null, address),
                tls: process.env.ENVIRONMENT === "test" ? undefined : {},
            },
            defaultJobOptions: {
                removeOnComplete: {
                    count: 100_000,
                    age: 60 * 60 * 24 * 30, // 30 days
                },
                removeOnFail: {
                    count: 100_000,
                    age: 60 * 60 * 24 * 30, // 30 days
                },
            },
            prefix: "{BULLMQ}",
        }),
        ChatModule,
        ClsModule.forRoot({ global: true }),
        EmailModule,
        GroupsModule,
        HttpCacheModule,
        InsightsModule,
        NarrativesModule,
        NotificationsModule,
        OpenaiModule,
        PrismaModule,
        PrismaRootModule,
        RedditModule,
        RolesModule,
        SalesforceApiModule,
        SalesforceModule,
        SecureBullBoardModule,
        ServeStaticModule.forRoot({
            rootPath: join(__dirname, "..", "static"),
        }),
        TagsModule,
        TaskModule,
        TenantsModule,
        TopicsModule,
        TruthkeepAnalyticsModule,
        UsersModule,
        UserVerificationModule,
        YoutubeApiModule,
        YoutubeModule,
        YoutubeSubtitlesModule,
        YtDlpModule,
    ],
    controllers: [AppController],
    providers: [AppService],
    exports: [],
})
export class AppModule {}
