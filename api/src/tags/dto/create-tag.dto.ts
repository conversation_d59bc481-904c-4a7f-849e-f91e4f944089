import {
    <PERSON><PERSON><PERSON>y,
    IsBoolean,
    <PERSON><PERSON><PERSON>ber,
    <PERSON><PERSON><PERSON>al,
    IsString,
} from "class-validator";

export class CreateTagRequestDto {
    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    @IsBoolean()
    @IsOptional()
    isPersonal?: boolean = false;

    @IsArray()
    @IsOptional()
    topics: number[] = [];

    @IsArray()
    @IsOptional()
    groups: number[] = [];
}

export class CreateTagResponseDto {
    @IsNumber()
    id: number;

    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsArray()
    @IsOptional()
    topics?: number[];

    @IsArray()
    @IsOptional()
    groups?: number[];
}
