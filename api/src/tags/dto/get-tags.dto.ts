import { ApiPropertyOptional, IntersectionType } from "@nestjs/swagger";

import { Transform } from "class-transformer";
import { IsBoolean, IsNumber, IsOptional } from "class-validator";

import {
    TopicFilteredRequestDto,
    GroupFilteredRequestDto,
    TopicFilteredParams,
    GroupFilteredParams,
    MyTeamsFilteredParams,
    MyTeamsFilteredRequestDto,
} from "../../common/dto/filtered-request.dto";
import {
    PaginatedRequestDto,
    PaginatedResponseDto,
} from "../../generic/dto/paginated.dto";

export type TagFilterParams = TopicFilteredParams &
    GroupFilteredParams &
    MyTeamsFilteredParams & {
        personalTagsOnly?: boolean;
        excludePersonalTags?: boolean;
    };

export class TagDto {
    id: number;
    name: string;
    description: string;
}

export class GetTagsRequestDto
    extends IntersectionType(
        PaginatedRequestDto,
        TopicFilteredRequestDto,
        GroupFilteredRequestDto,
        MyTeamsFilteredRequestDto,
    )
    implements TagFilterParams
{
    @IsNumber()
    size: number = 500;

    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    @ApiPropertyOptional({ default: false })
    personalTagsOnly?: boolean;

    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === "true" || value === true)
    @ApiPropertyOptional({ default: false })
    excludePersonalTags?: boolean;
}

export class GetTagsResponseDto extends PaginatedResponseDto<TagDto> {}
