import { ForbiddenException, Injectable } from "@nestjs/common";

import { Prisma } from "@prisma/client";

import { BulkPatchPreferenceRequestDto } from "../common/dto/preference.dto";
import { PrismaService } from "../prisma/prisma.service";
import { Role } from "../roles/role.enum";
import { RolesService } from "../roles/roles.service";
import { CreateTagRequestDto } from "./dto/create-tag.dto";
import { TagFilterParams } from "./dto/get-tags.dto";
import { UpdateTagRequestDto } from "./dto/update-tag.dto";

@Injectable()
export class TagsService {
    constructor(
        private readonly prismaService: PrismaService,
        private readonly rolesService: RolesService,
    ) {}

    async create(createTagDto: CreateTagRequestDto, userId: string) {
        return this.prismaService.tag.create({
            data: {
                ...createTagDto,
                topics: {
                    connect: createTagDto.topics.map((id) => ({
                        id,
                    })),
                },
                groups: {
                    connect: createTagDto.groups.map((id) => ({
                        id,
                    })),
                },
                createdByUserId: userId,
            },
        });
    }

    async findAll({
        take,
        skip,
        filterParams,
        userId,
    }: {
        take: number;
        skip: number;
        filterParams: TagFilterParams;
        userId: string;
    }) {
        const {
            topicIds,
            groupIds,
            excludePersonalTags,
            personalTagsOnly,
            myTeamsOnly,
        } = filterParams;

        const topicFilter: Prisma.TagWhereInput =
            topicIds?.length > 0
                ? { topics: { some: { id: { in: topicIds } } } }
                : {};

        const groupFilter: Prisma.TagWhereInput =
            groupIds?.length > 0
                ? { groups: { some: { id: { in: groupIds } } } }
                : {};

        const defaultVisibilityFilter: Prisma.TagWhereInput = {
            OR: [
                {
                    AND: [
                        {
                            isPersonal: true,
                            createdByUserId: userId,
                        },
                    ],
                },
                {
                    isPersonal: false,
                },
            ],
        };

        const personalTagsFilter: Prisma.TagWhereInput = personalTagsOnly
            ? {
                  isPersonal: true,
                  createdByUserId: userId,
              }
            : excludePersonalTags
              ? { isPersonal: false }
              : undefined;

        const myTeamsFilter: Prisma.TagWhereInput = myTeamsOnly
            ? { groups: { some: { users: { some: { userId } } } } }
            : undefined;

        const sqlFilters: Prisma.TagWhereInput = {
            ...topicFilter,
            ...groupFilter,
            ...(personalTagsFilter ?? myTeamsFilter ?? defaultVisibilityFilter),
        };

        const tags = await this.prismaService.tag.findMany({
            where: sqlFilters,
            include: {
                groups: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                topics: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                userTagPreferences: {
                    where: {
                        userId,
                    },
                    select: {
                        notificationCadence: true,
                        highRelevanceNotifications: true,
                        negativeSentimentNotifications: true,
                    },
                },
            },
            take,
            skip,
            orderBy: {
                createdAt: "desc",
            },
        });

        const total = await this.prismaService.tag.count({
            where: sqlFilters,
        });

        const formattedTags = tags.map((tag) => ({
            ...tag,
            userTagPreferences: tag.userTagPreferences[0],
        }));

        return { items: formattedTags, total };
    }

    async update(id: number, updateTagRequestDto: UpdateTagRequestDto) {
        await this.prismaService.tag.update({
            where: { id },
            data: {
                ...updateTagRequestDto,
                topics: {
                    set: updateTagRequestDto.topics?.map((id) => ({
                        id,
                    })),
                },
                groups: {
                    set: updateTagRequestDto.groups?.map((id) => ({
                        id,
                    })),
                },
            },
        });
    }

    async bulkPatch(
        bulkPatchRequestDto: BulkPatchPreferenceRequestDto,
        userId: string,
    ) {
        for (const item of bulkPatchRequestDto.items) {
            await this.prismaService.userTagPreference.upsert({
                where: { tagId_userId: { tagId: item.id, userId } },
                create: {
                    ...item.patch,
                    userId,
                    tagId: item.id,
                },
                update: item.patch,
            });
        }
    }

    async assertUserHasWritePermission(id: number, userId: string) {
        const isAdmin = await this.rolesService.hasUserRole(userId, Role.Admin);
        const isPersonalTagOwner = await this.prismaService.tag.findUnique({
            where: { id, createdByUserId: userId, isPersonal: true },
        });
        const hasPermission = isAdmin || isPersonalTagOwner;
        if (!hasPermission)
            throw new ForbiddenException(`Permission denied for tag ${id}`);
    }

    async delete(id: number) {
        return this.prismaService.tag.delete({
            where: { id },
        });
    }
}
