import { <PERSON>, Get, Post, Query, Req, Res } from "@nestjs/common";
import { ApiNoContentResponse, ApiOkResponse, ApiTags } from "@nestjs/swagger";

import { Request, Response } from "express";

import { Role } from "../roles/role.enum";
import { Roles } from "../roles/roles.decorator";
import {
    GetTruthkeepAnalyticsRequestDto,
    GetTruthkeepAnalyticsResponseDto,
} from "./dto/get-truthkeep-analytics.dto";
import { TruthkeepAnalyticsService } from "./truthkeep-analytics.service";

@Controller("truthkeep-analytics")
@ApiTags("truthkeep analytics")
export class TruthkeepAnalyticsController {
    constructor(
        private readonly truthkeepAnalyticsService: TruthkeepAnalyticsService,
    ) {}

    @Get()
    @Roles(Role.SuperAdmin)
    @ApiOkResponse()
    async getTruthkeepAnalytics(
        @Query() query: GetTruthkeepAnalyticsRequestDto,
    ): Promise<GetTruthkeepAnalyticsResponseDto> {
        return this.truthkeepAnalyticsService.getAnalytics(query);
    }

    @Post()
    @ApiNoContentResponse()
    async updateTruthkeepAnalytics(
        @Req() req: Request,
        @Res() res: Response,
    ): Promise<void> {
        const referer = req.headers["referer"];
        const refererUrl = new URL(referer);
        const path = refererUrl.pathname;
        const userId = req.user["userId"];

        await this.truthkeepAnalyticsService.trackAccess(userId, path);
        res.sendStatus(204);
    }

    @Get("users")
    @Roles(Role.SuperAdmin)
    @ApiOkResponse()
    async getTruthkeepAnalyticsUsers(@Res() res: Response): Promise<void> {
        const users = await this.truthkeepAnalyticsService.getUsers();
        const text = users.map((user) => user.email).join("\n");
        res.setHeader("Content-Type", "text/plain");
        res.send(text);
    }
}
