import { normalizeRefererPathname } from "./util";

describe("normalizeRefererPathname", () => {
    it("returns the pathname", () => {
        expect(normalizeRefererPathname("/timeline")).toBe("/timeline");
    });

    it("removes trailing slash", () => {
        expect(normalizeRefererPathname("/timeline/")).toBe("/timeline");
    });

    it("handles empty path", () => {
        expect(normalizeRefererPathname("/")).toBe("/");
    });
});
