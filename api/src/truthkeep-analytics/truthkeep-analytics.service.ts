import { Injectable } from "@nestjs/common";

import { DateFilterParams } from "../generic/dto/date-filtered.dto";
import { getLocalDateRangeEnd, getLocalDateRangeStart } from "../insights/util";
import { PrismaService } from "../prisma/prisma.service";
import { normalizeRefererPathname } from "./util";

@Injectable()
export class TruthkeepAnalyticsService {
    constructor(private readonly prisma: PrismaService) {}

    async getAnalytics(query: DateFilterParams) {
        const localDateFrom = getLocalDateRangeStart(
            query.dateFrom ?? "1900-01-01",
            query.timeZone ?? "America/Phoenix",
        );
        const localDateTo = getLocalDateRangeEnd(
            query.dateTo ?? new Date().toISOString().slice(0, 10),
            query.timeZone ?? "America/Phoenix",
        );

        const users: {
            userId: string;
            firstName: string;
            lastName: string;
            path: string;
            timeline: number;
            analytics: number;
            topicManagement: number;
            lastAccess: string;
        }[] = await this.prisma.$queryRawWithTenant`
            SELECT
                "User"."id",
                "firstName",
                "lastName",
                SUM(CASE WHEN "path" = '/timeline' OR "path" = '/' THEN "count" ELSE 0 END)::integer AS "timeline",
                SUM(CASE WHEN "path" = '/analytics' THEN "count" ELSE 0 END)::integer AS "analytics",
                SUM(CASE WHEN "path" LIKE '/topic-management%' THEN "count" ELSE 0 END)::integer AS "topicManagement",
                MAX("date") AS "lastAccess"
            FROM "User"
                LEFT JOIN "TruthkeepAccess"
                    ON "TruthkeepAccess"."userId" = "User"."id"
                    AND "date" BETWEEN ${localDateFrom} AND ${localDateTo}
            GROUP BY
                "User"."id",
                "firstName",
                "lastName"
            ORDER BY
                "lastAccess" DESC NULLS LAST;
        `;

        return { users };
    }

    async trackAccess(userId: string, path: string) {
        const normalizedPath = normalizeRefererPathname(path);
        const date = new Date();
        date.setHours(0, 0, 0, 0);

        await this.prisma.truthkeepAccess.upsert({
            where: {
                userId_path_date: {
                    userId,
                    path: normalizedPath,
                    date,
                },
            },
            update: {
                count: {
                    increment: 1,
                },
            },
            create: {
                userId,
                path,
                date,
            },
        });
    }

    async getUsers() {
        return this.prisma.user.findMany({
            select: {
                email: true,
            },
        });
    }
}
