import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Edit Team Modal Field Validation Works", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await page.getByRole("tab", { name: "Teams", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Teams", exact: true })
  ).toBeVisible();

  const teamsTable = page.getByRole("table");
  await expect(teamsTable).toBeVisible();

  await teamsTable
    .getByRole("button", { name: /edit team/i })
    .first()
    .click();

  const editTeamModal = page.getByRole("dialog", { name: "Edit Team" });
  await expect(editTeamModal).toBeVisible();

  await expect(editTeamModal.getByText("Team Name")).toBeVisible();
  const teamNameInput = editTeamModal.getByLabel("Team Name");
  await teamNameInput.fill("");

  await editTeamModal.getByRole("button", { name: "Save" }).click();

  await expect(editTeamModal.getByText("Team name is required")).toBeVisible();
});

test("Edit modal preloads the correct team data", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await page.getByRole("tab", { name: "Teams", exact: true }).click();

  await expect(
    page.getByRole("heading", { level: 3, name: "Teams", exact: true })
  ).toBeVisible();

  const teamsTable = page.getByRole("table");
  await expect(teamsTable).toBeVisible();

  const firstRow = teamsTable.getByRole("row").nth(1);
  const teamNameCell = firstRow.getByRole("cell").nth(1);
  const teamName = (await teamNameCell.innerText()).trim();

  await firstRow.getByRole("button", { name: /edit team/i }).click();

  const editTeamModal = page.getByRole("dialog", { name: "Edit Team" });
  await expect(editTeamModal).toBeVisible();

  const teamNameInput = editTeamModal.locator('input[data-path="name"]');
  await expect(teamNameInput).toHaveValue(teamName);
});

test("Edit modal shows confirmation modal when attempting to delete", async ({
  page,
}) => {
  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await page.getByRole("tab", { name: "Teams", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Teams", exact: true })
  ).toBeVisible();

  const teamsTable = page.getByRole("table");
  await expect(teamsTable).toBeVisible();
  const firstRow = teamsTable.getByRole("row").nth(1);

  await firstRow.getByRole("button", { name: /edit team/i }).click();

  const editTeamModal = page.getByRole("dialog", { name: "Edit Team" });
  await expect(editTeamModal).toBeVisible();

  await editTeamModal.getByRole("button", { name: "Delete Team" }).click();

  const deleteModal = page.getByRole("dialog", { name: "Delete Team" });
  await expect(deleteModal).toBeVisible();

  await expect(
    deleteModal.getByText("Are you sure you want to delete this team?")
  ).toBeVisible();
});
