import { test, expect } from "@playwright/test";
import { loginAsAdmin, turnOffViewMyTeamsTopicToggleOff } from "./util";

test("Edit modal preloads the correct topic data", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");
  await turnOffViewMyTeamsTopicToggleOff(page);

  await page.waitForSelector("table tbody tr");

  const firstRow = page.locator("table tbody tr").first();

  const topicText = (await firstRow.locator("td").nth(2).innerText()).trim();

  await firstRow.locator('button:has(img[alt="Edit topic"])').click();

  const editModal = page.getByRole("dialog", { name: "Edit Topic" });
  await expect(editModal).toBeVisible();

  const topicNameInput = editModal.locator('input[data-path="name"]');

  await expect(topicNameInput).toHaveValue(topicText);
});

test("Edit modal show confirmation modal when attempting to delete", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");
  await turnOffViewMyTeamsTopicToggleOff(page);

  await page.waitForSelector("table tbody tr");

  const firstRow = page.locator("table tbody tr").first();
  const nameText = (await firstRow.locator("td").nth(2).innerText()).trim();

  await firstRow.locator('button:has(img[alt="Edit topic"])').click();

  const editModal = page.getByRole("dialog", { name: "Edit Topic" });
  await expect(editModal).toBeVisible();

  await editModal.getByRole("button", { name: "Delete Topic" }).click();

  const deleteModal = page.getByRole("dialog", { name: "Delete Topic" });
  await expect(deleteModal).toBeVisible();

  await expect(
    deleteModal.getByText("Are you sure you want to delete this topic?")
  ).toBeVisible();
  await expect(deleteModal.getByText(nameText)).toBeVisible();
});
