import { expect, test } from "@playwright/test";
import {
  loginAsAdmin,
  turnOffViewMyTeamsTopicToggleOff,
  waitFor,
} from "./util";
import { randomUUID } from "crypto";

test("Visiting Topic & Team Management page without being authenticated brings you to login page", async ({
  page,
}) => {
  await page.goto("/topic-management");
  await page.waitForURL("**/login");

  expect(page.url()).toContain("/login");
  await expect(page.getByRole("textbox", { name: "Email" })).toBeVisible();
  await expect(page.getByRole("textbox", { name: "Password" })).toBeVisible();
});

test("Topics are successfully seeded", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  const seededTopics = ["sample-topic-1", "sample-topic-2", "sample-topic-3"];

  for (const topic of seededTopics) {
    const row = page.getByRole("row", { name: topic });
    await expect(row).toBeVisible();
  }
});

test("Can successfully add a new topic and delete it", async ({ page }) => {
  const topicName = `random-topic-${randomUUID()}`;

  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await expect(page.getByText(/^Add Topic$/)).toBeVisible();
  await page.getByRole("button", { name: "Add Topic", exact: true }).click();

  const addTopicModal = page.getByRole("dialog", { name: "Add Topic" });
  await expect(addTopicModal).toBeVisible();

  await expect(addTopicModal.getByText("Topic Name")).toBeVisible();
  await addTopicModal.getByLabel("Topic Name").fill(topicName);

  await addTopicModal.getByRole("button", { name: "Save" }).click();

  await expect(addTopicModal).toBeHidden();
  await expect(page.getByText(/^Add Topic$/)).toBeVisible();

  const row = page.getByRole("row", { name: topicName });
  await expect(row).toBeVisible();

  const checkbox = row.getByRole("checkbox", { name: "Select row" });
  await checkbox.check();

  await expect(
    page.getByRole("button", { name: "Delete Selected (1)" })
  ).toBeVisible();
  await page.getByRole("button", { name: "Delete Selected (1)" }).click();

  const deleteTopicModal = page.getByRole("dialog", { name: "Delete Topic" });
  await expect(deleteTopicModal).toBeVisible();

  await expect(
    deleteTopicModal.getByRole("button", { name: "Delete", exact: true })
  ).toBeVisible();
  await deleteTopicModal
    .getByRole("button", { name: "Delete", exact: true })
    .click();

  await expect(row).toHaveCount(0);
});

test("Can successfully add a new team and delete it", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await page.getByRole("tab", { name: "Teams", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Teams", exact: true })
  ).toBeVisible();

  const teamsTable = page.getByRole("table");
  await expect(teamsTable).toBeVisible();
  const beforeCount = (await teamsTable.getByRole("row").count()) - 1;

  const newGroupName = `new-team-${randomUUID()}`;
  const newGroupDesc = `new-desc-${randomUUID()}`;

  await page.getByRole("button", { name: /Add Team/i }).click();

  const addTeamModal = page.getByRole("dialog", {
    name: "Add Team",
    exact: true,
  });
  await expect(addTeamModal).toBeVisible();
  await addTeamModal.getByLabel("Team Name").fill(newGroupName);
  await addTeamModal.getByLabel("Description").fill(newGroupDesc);
  await addTeamModal.getByRole("button", { name: "Save" }).click();
  await expect(addTeamModal).toBeHidden();

  const addedRow = teamsTable.getByRole("row", {
    name: new RegExp(`\\b${newGroupName}\\b`),
  });
  await expect(addedRow).toBeVisible();
  await expect(teamsTable.getByRole("row")).toHaveCount(beforeCount + 1 + 1);

  await addedRow.getByRole("button", { name: "Edit team" }).click();

  const editTeamModal = page.getByRole("dialog", { name: "Edit Team" });
  await expect(editTeamModal).toBeVisible();

  await editTeamModal.getByRole("button", { name: "Delete Team" }).click();

  const deleteTeamModal = page.getByRole("dialog", {
    name: "Delete Team",
    exact: true,
  });
  await expect(deleteTeamModal).toBeVisible();
  await expect(
    deleteTeamModal.getByText("Are you sure you want to delete this team?")
  ).toBeVisible();

  await deleteTeamModal.getByRole("button", { name: "Delete" }).click();
  await expect(deleteTeamModal).toBeHidden();

  await expect(addedRow).toHaveCount(0);
  await expect(teamsTable.getByRole("row")).toHaveCount(beforeCount + 1);
});

test("Can successfully add a new topic group and delete it", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await page.getByRole("tab", { name: "Topic Groups", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Topic Groups" })
  ).toBeVisible();

  const topicGroupsTable = page.getByRole("table");
  await expect(topicGroupsTable).toBeVisible();
  const beforeCount = (await topicGroupsTable.getByRole("row").count()) - 1;

  // Add a new topic group
  const newTopicGroupName = `new-topic-group-${randomUUID()}`;
  const newTopicGroupDesc = `new-desc-${randomUUID()}`;

  await page.getByRole("button", { name: /Add Topic Group/i }).click();

  const addTopicGroupModal = page.getByRole("dialog", {
    name: "Add Topic Group",
  });
  await expect(addTopicGroupModal).toBeVisible();
  await addTopicGroupModal
    .getByLabel("Topic Group Name")
    .fill(newTopicGroupName);
  await addTopicGroupModal.getByLabel("Description").fill(newTopicGroupDesc);
  await addTopicGroupModal.getByRole("button", { name: "Save" }).click();
  await expect(addTopicGroupModal).toBeHidden();

  const addedRow = topicGroupsTable.getByRole("row", {
    name: new RegExp(`\\b${newTopicGroupName}\\b`),
  });
  await expect(addedRow).toBeVisible();
  await expect(topicGroupsTable.getByRole("row")).toHaveCount(
    beforeCount + 1 + 1
  );

  await addedRow.getByRole("button", { name: "Edit topic group" }).click();

  const editTopicGroupModal = page.getByRole("dialog", {
    name: "Edit Topic Group",
  });
  await expect(editTopicGroupModal).toBeVisible();

  await editTopicGroupModal
    .getByRole("button", { name: "Delete Topic Group" })
    .click();

  const deleteTopicGroupModal = page.getByRole("dialog", {
    name: "Delete Topic Group",
  });
  await expect(deleteTopicGroupModal).toBeVisible();

  await expect(
    deleteTopicGroupModal.getByText(
      "Are you sure you want to delete this topic group?"
    )
  ).toBeVisible();

  await deleteTopicGroupModal.getByRole("button", { name: "Delete" }).click();
  await expect(deleteTopicGroupModal).toBeHidden();

  await expect(addedRow).toHaveCount(0);
  await expect(topicGroupsTable.getByRole("row")).toHaveCount(beforeCount + 1);
});

test("Can successfully edit a topic and then also delete it using the edit modal", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);

  const firstRow = page.locator("table tbody tr").first();
  const name = (await firstRow.locator("td").nth(2).textContent())?.trim();

  // open edit
  await firstRow.locator('button:has(img[alt="Edit topic"])').click();
  const editModal = page.getByRole("dialog", { name: "Edit Topic" });
  await expect(editModal).toBeVisible();

  const inputs = editModal.locator('input[aria-haspopup="listbox"]');

  const tagsInput = inputs.nth(0);
  const groupsInput = inputs.nth(1);

  // 1) Add a tag
  await tagsInput.click();
  const tagOption = page.getByRole("option").first();
  const addedTag = (await tagOption.innerText()).trim();
  await tagOption.click();
  await tagsInput.press("Escape");

  // 2) Add a team
  await groupsInput.click();
  const groupOption = page.getByRole("option").first();
  const addedGroup = (await groupOption.innerText()).trim();
  await groupOption.click();
  await groupsInput.press("Escape");

  // save
  await Promise.all([
    editModal.getByRole("button", { name: "Save" }).click(),
    page.waitForResponse(
      (resp) =>
        resp.request().method() === "PUT" &&
        resp.url().includes("/api/topics") &&
        resp.status() === 200
    ),
  ]);
  await expect(editModal).toBeHidden();

  // assert new topic group & team
  await expect(firstRow.locator("td").nth(3)).toContainText(addedTag);
  await expect(firstRow.locator("td").nth(4)).toContainText(addedGroup);

  // now delete via modal
  await firstRow.locator('button:has(img[alt="Edit topic"])').click();
  await expect(editModal).toBeVisible();
  await editModal.getByRole("button", { name: "Delete Topic" }).click();

  const deleteModal = page.getByRole("dialog", { name: "Delete Topic" });
  await expect(
    deleteModal.getByText("Are you sure you want to delete this topic?")
  ).toBeVisible();

  await Promise.all([
    deleteModal.getByRole("button", { name: "Delete" }).click(),
    page.waitForResponse(
      (resp) =>
        resp.request().method() === "DELETE" &&
        resp.url().endsWith("/api/topics") &&
        resp.status() === 204
    ),
  ]);

  await expect(deleteModal).toBeHidden();
  await expect(
    page.locator("table tbody tr").filter({ hasText: name })
  ).not.toBeVisible();
});

test("Can successfully edit a team and then also delete it using the edit modal", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await page.getByRole("tab", { name: "Teams", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Teams", exact: true })
  ).toBeVisible();

  const teamsTable = page.getByRole("table");
  await expect(teamsTable).toBeVisible();
  const beforeCount = (await teamsTable.getByRole("row").count()) - 1;
  expect(beforeCount).toBeGreaterThan(0);

  const firstRow = teamsTable.getByRole("row").nth(1);

  const teamNameCell = firstRow.getByRole("cell").nth(1);

  await firstRow.getByRole("button", { name: "Edit team" }).click();

  const editTeamModal = page.getByRole("dialog", {
    name: "Edit Team",
    exact: true,
  });
  await expect(editTeamModal).toBeVisible();

  const newName = `edited-gname-${randomUUID()}`;
  await editTeamModal.getByLabel("Team Name").fill(newName);
  await editTeamModal.getByRole("button", { name: "Save" }).click();
  await expect(editTeamModal).toBeHidden();

  await expect(teamNameCell).toHaveText(newName);

  await firstRow.getByRole("button", { name: "Edit team" }).click();
  await expect(editTeamModal).toBeVisible();

  await editTeamModal
    .getByRole("button", { name: "Delete Team", exact: true })
    .click();

  const deleteTeamModal = page.getByRole("dialog", {
    name: "Delete Team",
    exact: true,
  });
  await expect(deleteTeamModal).toBeVisible();
  await expect(
    deleteTeamModal.getByText("Are you sure you want to delete this team?")
  ).toBeVisible();

  await deleteTeamModal.getByRole("button", { name: "Delete" }).click();
  await expect(deleteTeamModal).toBeHidden();
  await expect(editTeamModal).toBeHidden();

  await expect(teamsTable.getByRole("row")).toHaveCount(beforeCount - 1 + 1);
});

test("Can successfully edit a topic group's description and then delete it using the edit modal", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await page.getByRole("tab", { name: "Topic Groups", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Topic Groups" })
  ).toBeVisible();

  const topicGroupsTable = page.getByRole("table");
  await expect(topicGroupsTable).toBeVisible();
  const beforeCount = (await topicGroupsTable.getByRole("row").count()) - 1;
  expect(beforeCount).toBeGreaterThan(0);

  const firstRow = topicGroupsTable.getByRole("row").nth(1);

  await firstRow.getByRole("button", { name: "Edit topic group" }).click();

  const editTopicGroupModal = page.getByRole("dialog", {
    name: "Edit Topic Group",
  });
  await expect(editTopicGroupModal).toBeVisible();

  const newDescription = `edited-description-${randomUUID()}`;
  await editTopicGroupModal.getByLabel("Description").fill(newDescription);
  await editTopicGroupModal.getByRole("button", { name: "Save" }).click();
  await expect(editTopicGroupModal).toBeHidden();

  await firstRow.getByRole("button", { name: "Edit topic group" }).click();
  await expect(editTopicGroupModal).toBeVisible();
  await expect(editTopicGroupModal.getByLabel("Description")).toHaveValue(
    newDescription
  );

  await editTopicGroupModal
    .getByRole("button", { name: "Delete Topic Group" })
    .click();

  const deleteTopicGroupModal = page.getByRole("dialog", {
    name: "Delete Topic Group",
  });
  await expect(deleteTopicGroupModal).toBeVisible();
  await expect(
    deleteTopicGroupModal.getByText(
      "Are you sure you want to delete this topic group?"
    )
  ).toBeVisible();

  await deleteTopicGroupModal.getByRole("button", { name: "Delete" }).click();
  await expect(deleteTopicGroupModal).toBeHidden();
  await expect(editTopicGroupModal).toBeHidden();

  await expect(topicGroupsTable.getByRole("row")).toHaveCount(
    beforeCount - 1 + 1
  );
});

test("Can successfully favorite and unfavorite a topic", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);

  const firstRow = page.locator("table tbody tr").first();
  const favCell = firstRow.locator("td").nth(1);
  const favBtn = favCell.locator("button");
  const favIcon = favBtn.locator("img");

  await expect(favIcon).toHaveAttribute("alt", "not favorite");

  await Promise.all([
    page.waitForResponse(
      (resp) =>
        resp.url().endsWith("/topics/preferences") && resp.status() === 204
    ),
    favBtn.click(),
  ]);
  await expect(favIcon).toHaveAttribute("alt", "favorite");

  await page.reload();

  await expect(
    page.getByRole("heading", {
      level: 2,
      name: "Topic & Team Management",
      exact: true,
    })
  ).toBeVisible();

  await turnOffViewMyTeamsTopicToggleOff(page);

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);
  const reloadedIcon = page
    .locator("table tbody tr")
    .first()
    .locator("td")
    .nth(1)
    .locator("img");
  await expect(reloadedIcon).toHaveAttribute("alt", "favorite");

  await Promise.all([
    page.waitForResponse(
      (resp) =>
        resp.url().endsWith("/topics/preferences") && resp.status() === 204
    ),
    page
      .locator("table tbody tr")
      .first()
      .locator("td")
      .nth(1)
      .locator("button")
      .click(),
  ]);
  await expect(reloadedIcon).toHaveAttribute("alt", "not favorite");

  await page.reload();
  await expect(
    page.getByRole("heading", {
      level: 2,
      name: "Topic & Team Management",
      exact: true,
    })
  ).toBeVisible();

  await turnOffViewMyTeamsTopicToggleOff(page);

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);
  const reloadedIcon2 = page
    .locator("table tbody tr")
    .first()
    .locator("td")
    .nth(1)
    .locator("img");
  await expect(reloadedIcon2).toHaveAttribute("alt", "not favorite");
});

test("Can successfully assign topic groups to topics", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);

  const firstRow = page.locator("table tbody tr").first();
  const tagsCell = firstRow.locator("td").nth(3);
  const beforeCount = await tagsCell.locator("span.mantine-Pill-root").count();

  const checkbox = firstRow.getByRole("checkbox", { name: "Select row" });
  await checkbox.check();

  await expect(
    page.getByRole("button", { name: "Assign Topic Group (1)" })
  ).toBeVisible();
  await page.getByRole("button", { name: "Assign Topic Group (1)" }).click();

  const assignTagsToTopicModal = page.getByRole("dialog", {
    name: "Assign Topic Groups to Topic",
  });
  await expect(assignTagsToTopicModal).toBeVisible();

  const tagsInput = assignTagsToTopicModal
    .locator('input[aria-haspopup="listbox"]')
    .first();
  await tagsInput.click();
  const firstOption = page.getByRole("option").first();
  const tagName = (await firstOption.innerText()).trim();
  await firstOption.click();

  await assignTagsToTopicModal.getByRole("button", { name: "Save" }).click();
  await expect(assignTagsToTopicModal).toBeHidden();

  await expect(tagsCell).toHaveText(new RegExp(`.*${tagName}.*`));
  const afterCount = await tagsCell.locator("span.mantine-Pill-root").count();
  expect(afterCount).toBe(beforeCount + 1);
});

test("Can successfully add a topic group if you can't find one in assign topic group search", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);

  const firstRow = page.locator("table tbody tr").first();

  await firstRow.getByRole("checkbox", { name: "Select row" }).check();
  await expect(
    page.getByRole("button", { name: "Assign Topic Group (1)" })
  ).toBeVisible();
  await page.getByRole("button", { name: "Assign Topic Group (1)" }).click();

  const assignModal = page.getByRole("dialog", {
    name: "Assign Topic Groups to Topic",
  });
  await expect(assignModal).toBeVisible();

  const uniqueTopicGroup = `auto-tag-${randomUUID()}`;

  const topicGroupsInput = assignModal
    .locator('input[aria-haspopup="listbox"]')
    .first();
  await topicGroupsInput.click();
  await topicGroupsInput.fill(uniqueTopicGroup);

  const createBtn = page.getByRole("button", {
    name: `Create new topic group "${uniqueTopicGroup}"`,
  });
  await expect(createBtn).toBeVisible();
  await createBtn.click();

  const addTagModal = page.getByRole("dialog", { name: "Add Topic Group" });
  await expect(addTagModal).toBeVisible();

  const description = `desc-${randomUUID()}`;
  await addTagModal.getByLabel("Topic Group Name").fill(uniqueTopicGroup);
  await addTagModal.getByLabel("Description").fill(description);

  await Promise.all([
    page.waitForResponse(
      (r) => r.url().endsWith("/tags") && r.status() === 201
    ),
    addTagModal.getByRole("button", { name: "Save", exact: true }).click(),
  ]);

  await expect(addTagModal).toBeHidden();
  await expect(assignModal).toBeHidden();

  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await page.getByRole("tab", { name: "Topic Groups" }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Topic Groups" })
  ).toBeVisible();

  const topicGroupsTable = page.getByRole("table");
  await expect(topicGroupsTable).toBeVisible();

  const addedRow = topicGroupsTable.getByRole("row", {
    name: new RegExp(`\\b${uniqueTopicGroup}\\b`),
  });
  await expect(addedRow).toBeVisible();
});

test("Can successfully edit notification preferences using the modal", async ({
  page,
}) => {
  await loginAsAdmin(page);
  await page.goto("/topic-management");
  await page.waitForURL("/topic-management");

  await turnOffViewMyTeamsTopicToggleOff(page);

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);

  const firstRow = page.locator("table tbody tr").first();

  await firstRow.getByRole("checkbox", { name: "Select row" }).check();
  await expect(
    page.getByRole("button", { name: "Edit Notification Preferences (1)" })
  ).toBeVisible();
  await page
    .getByRole("button", { name: "Edit Notification Preferences (1)" })
    .click();

  const notificationPreferencesModal = page.getByRole("dialog", {
    name: "Notification Preferences",
  });
  await expect(notificationPreferencesModal).toBeVisible();

  await notificationPreferencesModal
    .locator('label:has-text("Negative Sentiment Notification")')
    .click();

  const negSwitch = notificationPreferencesModal.getByRole("switch", {
    name: "Negative Sentiment Notification",
  });
  await expect(negSwitch).toBeChecked();

  const cadenceInput = notificationPreferencesModal.getByLabel(
    "Email Notifications",
    { exact: true }
  );
  await cadenceInput.click();
  await page.getByRole("option", { name: "Off" }).click();

  await Promise.all([
    page.waitForResponse(
      (resp) =>
        resp.url().endsWith("/topics/preferences") && resp.status() === 204
    ),
    notificationPreferencesModal.getByRole("button", { name: "Save" }).click(),
  ]);

  await expect(notificationPreferencesModal).toBeHidden();

  await waitFor(async () => (await page.locator("table tbody tr").count()) > 0);

  await firstRow
    .locator('button:has(img[alt="Edit notification preferences"])')
    .click();
  const editModal = page.getByRole("dialog", {
    name: "Notification Settings",
  });
  await expect(editModal).toBeVisible();

  const inputs = editModal.locator('input[aria-haspopup="listbox"]');
  const editModalcadenceInput = inputs.nth(2);
  const editModalNegSwitch = editModal.getByRole("switch", {
    name: "Negative Sentiment Notifications",
  });

  await expect(editModalNegSwitch).toBeChecked();
  await expect(editModalcadenceInput).toHaveValue("Off");
});
