import { readFileSync } from "fs";
import path from "path";
import { Page } from "@playwright/test";
import dotenv from "dotenv";
dotenv.config();
const fixturesDirPath = path.join(__dirname, "api/test/mocks/fixtures");

/**
 * This allows you to wait for something (like an email to be available).
 *
 * It calls the callback every 50ms until it returns a value (and does not throw
 * an error). After the timeout, it will throw the last error that was thrown
 */
export async function waitFor<ReturnValue>(
  cb: () => ReturnValue | Promise<ReturnValue>,
  timeout = 5000
) {
  const endTime = Date.now() + timeout;
  let lastError;
  while (Date.now() < endTime) {
    try {
      const response = await cb();
      if (response) return response;
    } catch (e: unknown) {
      lastError = e;
    }
    await new Promise((r) => setTimeout(r, 100));
  }
  throw lastError;
}

export async function readLatestEmailTo(to: string) {
  const localSESHost = process.env.LOCAL_SES_HOST || "http://localhost:8282";
  const emails = await fetch(`${localSESHost}/emails`).then((response) =>
    response.json()
  );

  const [lastEmail] = emails.filter((email) => email.to === to).reverse();

  return lastEmail;
}

export async function loginAsAdmin(page: Page) {
  const email = process.env.ADMIN_EMAIL || "<EMAIL>";
  const password = process.env.ADMIN_PASS as string;

  await page.goto("/login");
  await page.getByRole("textbox", { name: "Email" }).fill(email);
  await page.getByRole("textbox", { name: "Password" }).fill(password);
  await page.getByRole("button", { name: "Login" }).click();
  await page.waitForURL((url) => url.pathname !== "/login");
}

export async function loginAsUser(page: Page, email: string, password: string) {
  await page.goto("/login");
  await page.getByRole("textbox", { name: "Email" }).fill(email);
  await page.getByRole("textbox", { name: "Password" }).fill(password);
  await page.getByRole("button", { name: "Login" }).click();
  await page.waitForURL((url) => url.pathname !== "/login");
}

export async function turnOffViewMyTeamsTopicToggleOff(page: Page) {
  const teamTopicsCheckbox = page.getByRole("textbox", {
    name: "Teams",
  });
  await teamTopicsCheckbox.clear();
}
