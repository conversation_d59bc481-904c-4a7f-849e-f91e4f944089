import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Edit modal preloads the correct topic group data", async ({ page }) => {
  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await page.getByRole("tab", { name: "Topic Groups", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Topic Groups" })
  ).toBeVisible();

  const topicGroupsTable = page.getByRole("table");
  await expect(topicGroupsTable).toBeVisible();

  const firstRow = topicGroupsTable.getByRole("row").nth(1);

  const topicGroupNameCell = firstRow.getByRole("cell").nth(1);
  const topicGroupName = (await topicGroupNameCell.innerText()).trim();

  await firstRow.getByRole("button", { name: /edit topic group/i }).click();

  const editTopicGroupModal = page.getByRole("dialog", {
    name: "Edit Topic Group",
  });
  await expect(editTopicGroupModal).toBeVisible();

  const topicGroupNameInput = editTopicGroupModal.locator(
    'input[data-path="name"]'
  );
  await expect(topicGroupNameInput).toHaveValue(topicGroupName);
});

test("Edit modal shows confirmation modal when attempting to delete a topic group", async ({
  page,
}) => {
  await loginAsAdmin(page);

  await page.goto("/topic-management");
  await page.waitForURL("**/topic-management");

  await page.getByRole("tab", { name: "Topic Groups", exact: true }).click();
  await expect(
    page.getByRole("heading", { level: 3, name: "Topic Groups" })
  ).toBeVisible();

  const topicGroupsTable = page.getByRole("table");
  await expect(topicGroupsTable).toBeVisible();

  const firstRow = topicGroupsTable.getByRole("row").nth(1);

  await firstRow.getByRole("button", { name: /edit topic group/i }).click();

  const editTopicGroupModal = page.getByRole("dialog", {
    name: "Edit Topic Group",
  });
  await expect(editTopicGroupModal).toBeVisible();

  await editTopicGroupModal
    .getByRole("button", { name: "Delete Topic Group" })
    .click();

  const deleteTopicGroupModal = page.getByRole("dialog", {
    name: "Delete Topic Group",
  });
  await expect(deleteTopicGroupModal).toBeVisible();

  await expect(
    deleteTopicGroupModal.getByText(
      "Are you sure you want to delete this topic group?"
    )
  ).toBeVisible();
});
