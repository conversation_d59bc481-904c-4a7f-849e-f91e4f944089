import { test, expect } from "@playwright/test";
import { loginAsAdmin } from "./util";

test("Can successfully filter by source", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/timeline");
  await page.waitForURL("/timeline");

  await page.click('role=link[name="Timeline"]');

  await expect(page.locator('img[alt="youtube icon"]')).not.toHaveCount(0);

  await page.getByRole("button", { name: "More Filters" }).click();
  const sourceFilter = page.getByPlaceholder("Select sources");
  await sourceFilter.click();

  await page.getByRole("option", { name: "Reddit Post" }).click();

  (await page.locator('img[alt="youtube icon"]').count()) === 0;
});

test("Can successfully filter by sentiment", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/timeline");
  await page.waitForURL("/timeline");

  await page.click('role=link[name="Timeline"]');
  await page.getByRole("button", { name: "More Filters" }).click();

  const sentimentBoxes = page.locator(
    '.mantine-Accordion-item div[class*="bg-[#"]'
  );

  const boxCount = await sentimentBoxes.count();
  let hasNegative = false;

  for (let i = 0; i < boxCount; i++) {
    const classAttr = await sentimentBoxes.nth(i).getAttribute("class");
    if (classAttr?.includes("bg-[#FA5252]")) {
      hasNegative = true;
      break;
    }
  }

  if (hasNegative) {
    const sentimentFilter = page.getByPlaceholder("Select sentiments");
    await sentimentFilter.click();
    await page.getByRole("option", { name: "Positive" }).click();

    const visibleTimelinePanel = page.locator('[id$="-panel-timeline"]');
    const visibleBoxes = visibleTimelinePanel.locator(
      '.mantine-Accordion-item div[class*="bg-[#FA5252]"]'
    );
    await expect(visibleBoxes).toHaveCount(0);
  }
});

test("Can successfully filter by topic", async ({ page }) => {
  const topicOption = "sample-topic-2";

  await loginAsAdmin(page);
  await page.goto("/timeline");
  await page.waitForURL("/timeline");
  await page.getByRole("button", { name: "More Filters" }).click();

  const timelinePanel = page.getByTestId("timeline-cards-section");
  const timelineItems = timelinePanel.getByTestId("timeline-card");

  // Wait for initial timeline to load
  await expect(timelineItems).not.toHaveCount(0);

  // Prepare to wait for the timeline request to complete
  const timelineRequestPromise = page.waitForRequest(/timeline/);

  // Select the topic from the topic filter
  const topicFilter = page.getByRole("textbox", {
    name: "timeline-topic-filter",
  });
  await topicFilter.click();
  await page.getByRole("option", { name: topicOption }).click();

  // Wait for the timeline request to complete
  await timelineRequestPromise;

  // Wait for timeline to re-render
  await timelineItems.first().waitFor({ state: "visible" });

  // Check that the timeline item has sample-topic-2
  await expect(timelineItems.first()).toHaveText(/sample-topic-2/i);
});

// Each seeded topic is associated with a tag that shares the same number.
// For example, sample-tag-1 is associated with sample-topic-1.
// This means, if tag 1 is selected, it should return timeline entries related to topic 1, and so on.
test("Can successfully filter by topic group", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/timeline");
  await page.waitForURL("/timeline");
  await page.getByRole("button", { name: "More Filters" }).click();

  const timelinePanel = page.getByTestId("timeline-cards-section");
  const timelineItems = timelinePanel.getByTestId("timeline-card");
  await timelineItems.first().waitFor({ state: "visible" });

  // Prepare to wait for the timeline request to complete
  const timelineRequestPromise = page.waitForRequest(/timeline/);

  // Set tags filter
  const tagsFilter = page.getByRole("textbox", {
    name: "timeline-topic-groups-filter",
  });
  await tagsFilter.click();
  await page.getByRole("option", { name: "sample-topic-group-2" }).click();

  // Wait for the timeline request to complete
  await timelineRequestPromise;

  // Wait for timeline to re-render
  await timelineItems.first().waitFor({ state: "visible" });
  const itemCount = await timelineItems.count();

  for (let i = 0; i < itemCount; i++) {
    const item = timelineItems.nth(i);

    const topicList = item.locator("ul").first();
    const topicEls = topicList.locator("li:not([hidden])");
    const topicTexts = await topicEls.allInnerTexts();

    // Each topic text should be 'sample-topic-2', since sample-tag-2 is associated with sample-topic-2.
    for (const txt of topicTexts) {
      expect(txt.trim()).toMatch(/sample-topic-2/i);
    }
  }
});

// Similar to the tag filter, each group is associated with a topic.
test("Can successfully filter by teams", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/timeline");
  await page.waitForURL("/timeline");
  await page.click('role=link[name="Timeline"]');

  const timelinePanel = page.getByTestId("timeline-cards-section");
  const timelineItems = timelinePanel.getByTestId("timeline-card");

  // Wait for initial timeline to load
  await expect(timelineItems).not.toHaveCount(0);

  // Filter by sample-group-1
  const teamsFilter = page.getByRole("textbox", { name: "Teams" });
  await teamsFilter.click();
  await page.getByRole("option", { name: "sample-group-1" }).click();

  // Set date range to last year
  const dateRangeFilter = page.getByLabel(/date range/i);
  await dateRangeFilter.click();
  await page.getByText("Last year", { exact: true }).click();

  // Check that the timeline item has sample-topic-1
  await expect(timelineItems.first()).toHaveText(/sample-topic-1/i);
});

// Reddit post timeline will always have publishedAt as 7 months in the past from the date
// it is seeded.
test("Can successfully filter by date range", async ({ page }) => {
  await loginAsAdmin(page);
  await page.goto("/timeline");
  await page.waitForURL("/timeline");
  await page.click('role=link[name="Timeline"]');

  const timelinePanel = page.getByTestId("timeline-cards-section");
  const timelineItems = timelinePanel.getByTestId("timeline-card");
  const dateRangeFilter = page.getByPlaceholder(/select a date range/i);

  await expect(timelineItems).toHaveCount(1);

  await dateRangeFilter.click();
  await page.getByText("Last year", { exact: true }).click();

  await expect(timelineItems).toHaveCount(2);
});
